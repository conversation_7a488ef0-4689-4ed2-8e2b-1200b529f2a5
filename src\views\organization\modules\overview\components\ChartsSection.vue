<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, watch } from "vue";
import { $t } from "@/plugins/i18n";
import { ElCard, ElRow, ElCol, ElSkeleton } from "element-plus";
import * as echarts from "echarts";
import type { OrganizationOverviewStats } from "../../../shared/utils/type";

interface Props {
  stats: OrganizationOverviewStats | null;
  loading: boolean;
}

const props = defineProps<Props>();

// Chart refs
const conversationChartRef = ref<HTMLDivElement>();
const botUsageChartRef = ref<HTMLDivElement>();
const tokenUsageChartRef = ref<HTMLDivElement>();
const activityChartRef = ref<HTMLDivElement>();

let conversationChart: echarts.ECharts | null = null;
let botUsageChart: echarts.ECharts | null = null;
let tokenUsageChart: echarts.ECharts | null = null;
let activityChart: echarts.ECharts | null = null;

// Chart initialization
const initConversationChart = () => {
  if (!conversationChartRef.value) return;

  conversationChart = echarts.init(conversationChartRef.value);

  const option = {
    title: {
      text: $t("Conversation Trends"),
      textStyle: {
        fontSize: 14,
        fontWeight: "normal",
        color: "#374151"
      }
    },
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(255, 255, 255, 0.95)",
      borderColor: "#e5e7eb",
      textStyle: {
        color: "#374151"
      }
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      data: [
        $t("Mon"),
        $t("Tue"),
        $t("Wed"),
        $t("Thu"),
        $t("Fri"),
        $t("Sat"),
        $t("Sun")
      ],
      axisLine: {
        lineStyle: {
          color: "#e5e7eb"
        }
      },
      axisLabel: {
        color: "#6b7280"
      }
    },
    yAxis: {
      type: "value",
      axisLine: {
        lineStyle: {
          color: "#e5e7eb"
        }
      },
      axisLabel: {
        color: "#6b7280"
      },
      splitLine: {
        lineStyle: {
          color: "#f3f4f6"
        }
      }
    },
    series: [
      {
        data: [120, 200, 150, 80, 70, 110, 130],
        type: "line",
        smooth: true,
        symbol: "circle",
        symbolSize: 6,
        itemStyle: {
          color: "#3b82f6"
        },
        lineStyle: {
          width: 3
        },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(59, 130, 246, 0.3)"
              },
              {
                offset: 1,
                color: "rgba(59, 130, 246, 0.05)"
              }
            ]
          }
        }
      }
    ]
  };

  conversationChart.setOption(option);
};

const initBotUsageChart = () => {
  if (!botUsageChartRef.value) return;

  botUsageChart = echarts.init(botUsageChartRef.value);

  // Get popular bots data
  const popularBots = props.stats?.activity?.popularBots || [];
  const botNames = popularBots.map(bot =>
    bot.name.length > 15 ? bot.name.substring(0, 15) + "..." : bot.name
  );
  const botUsages = popularBots.map(bot => bot.usage);

  const option = {
    title: {
      text: "Bot Usage Distribution",
      textStyle: {
        fontSize: 14,
        fontWeight: "normal",
        color: "#374151"
      }
    },
    tooltip: {
      trigger: "item",
      backgroundColor: "rgba(255, 255, 255, 0.95)",
      borderColor: "#e5e7eb",
      textStyle: {
        color: "#374151"
      },
      formatter: "{b}: {c} conversations ({d}%)"
    },
    series: [
      {
        type: "pie",
        radius: ["40%", "70%"],
        center: ["50%", "60%"],
        data: popularBots.map((bot, index) => ({
          value: bot.usage,
          name: bot.name,
          itemStyle: {
            color: [
              "#3b82f6",
              "#10b981",
              "#f59e0b",
              "#ef4444",
              "#8b5cf6",
              "#06b6d4",
              "#84cc16",
              "#f97316"
            ][index % 8]
          }
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)"
          }
        },
        label: {
          show: true,
          formatter: "{b}\n{d}%",
          fontSize: 10
        }
      }
    ]
  };

  botUsageChart.setOption(option);
};

const initTokenUsageChart = () => {
  if (!tokenUsageChartRef.value) return;

  tokenUsageChart = echarts.init(tokenUsageChartRef.value);

  const option = {
    title: {
      text: "Token Usage Trend",
      textStyle: {
        fontSize: 14,
        fontWeight: "normal",
        color: "#374151"
      }
    },
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(255, 255, 255, 0.95)",
      borderColor: "#e5e7eb",
      textStyle: {
        color: "#374151"
      }
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      data: ["Week 1", "Week 2", "Week 3", "Week 4"],
      axisLine: {
        lineStyle: {
          color: "#e5e7eb"
        }
      },
      axisLabel: {
        color: "#6b7280"
      }
    },
    yAxis: {
      type: "value",
      axisLine: {
        lineStyle: {
          color: "#e5e7eb"
        }
      },
      axisLabel: {
        color: "#6b7280",
        formatter: "{value}K"
      },
      splitLine: {
        lineStyle: {
          color: "#f3f4f6"
        }
      }
    },
    series: [
      {
        data: [
          Math.floor((props.stats?.tokens?.thisMonth || 0) * 0.2),
          Math.floor((props.stats?.tokens?.thisMonth || 0) * 0.3),
          Math.floor((props.stats?.tokens?.thisMonth || 0) * 0.25),
          Math.floor((props.stats?.tokens?.thisMonth || 0) * 0.25)
        ],
        type: "line",
        smooth: true,
        symbol: "circle",
        symbolSize: 8,
        itemStyle: {
          color: "#f59e0b"
        },
        lineStyle: {
          width: 3
        },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(245, 158, 11, 0.3)"
              },
              {
                offset: 1,
                color: "rgba(245, 158, 11, 0.05)"
              }
            ]
          }
        }
      }
    ]
  };

  tokenUsageChart.setOption(option);
};

const initActivityChart = () => {
  if (!activityChartRef.value) return;

  activityChart = echarts.init(activityChartRef.value);

  // Use peak hours data if available
  const peakHours = props.stats?.activity?.peakHours || [
    5, 20, 36, 45, 42, 38, 15
  ];
  const hourLabels = [
    "00:00",
    "04:00",
    "08:00",
    "12:00",
    "16:00",
    "20:00",
    "24:00"
  ];

  const option = {
    title: {
      text: "Peak Activity Hours",
      textStyle: {
        fontSize: 14,
        fontWeight: "normal",
        color: "#374151"
      }
    },
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(255, 255, 255, 0.95)",
      borderColor: "#e5e7eb",
      textStyle: {
        color: "#374151"
      },
      formatter: "{b}: {c} conversations"
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      data: hourLabels,
      axisLine: {
        lineStyle: {
          color: "#e5e7eb"
        }
      },
      axisLabel: {
        color: "#6b7280"
      }
    },
    yAxis: {
      type: "value",
      axisLine: {
        lineStyle: {
          color: "#e5e7eb"
        }
      },
      axisLabel: {
        color: "#6b7280"
      },
      splitLine: {
        lineStyle: {
          color: "#f3f4f6"
        }
      }
    },
    series: [
      {
        data: peakHours.slice(0, 7),
        type: "bar",
        itemStyle: {
          color: "#10b981",
          borderRadius: [4, 4, 0, 0]
        }
      }
    ]
  };

  activityChart.setOption(option);
};

const initCharts = async () => {
  await nextTick();
  initConversationChart();
  initBotUsageChart();
  initTokenUsageChart();
  initActivityChart();
};

const resizeCharts = () => {
  conversationChart?.resize();
  botUsageChart?.resize();
  tokenUsageChart?.resize();
  activityChart?.resize();
};

const destroyCharts = () => {
  conversationChart?.dispose();
  botUsageChart?.dispose();
  tokenUsageChart?.dispose();
  activityChart?.dispose();
  conversationChart = null;
  botUsageChart = null;
  tokenUsageChart = null;
  activityChart = null;
};

// Watch for stats changes to update charts
watch(
  () => props.stats,
  () => {
    if (props.stats && !props.loading) {
      initCharts();
    }
  },
  { deep: true }
);

// Handle window resize
const handleResize = () => {
  resizeCharts();
};

onMounted(() => {
  window.addEventListener("resize", handleResize);
  if (props.stats && !props.loading) {
    initCharts();
  }
});

onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
  destroyCharts();
});
</script>

<template>
  <div class="charts-section">
    <!-- First Row: Conversation Trends & Bot Usage -->
    <el-row :gutter="24" class="mb-6">
      <!-- Conversation Trends Chart -->
      <el-col :xs="24" :lg="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span class="text-sm font-medium text-gray-700"
                >Conversation Trends</span
              >
            </div>
          </template>
          <el-skeleton v-if="loading" animated>
            <template #template>
              <div class="h-80 bg-gray-100 rounded" />
            </template>
          </el-skeleton>
          <div
            v-else
            ref="conversationChartRef"
            class="chart-container"
            style="height: 300px"
          />
        </el-card>
      </el-col>

      <!-- Bot Usage Distribution Chart -->
      <el-col :xs="24" :lg="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span class="text-sm font-medium text-gray-700"
                >Bot Usage Distribution</span
              >
            </div>
          </template>
          <el-skeleton v-if="loading" animated>
            <template #template>
              <div class="h-80 bg-gray-100 rounded" />
            </template>
          </el-skeleton>
          <div
            v-else
            ref="botUsageChartRef"
            class="chart-container"
            style="height: 300px"
          />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<style scoped>
.charts-section {
  padding: 0;
}

.chart-card {
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
  height: 100%;
}

.chart-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border-color: #d1d5db;
}

.chart-container {
  width: 100%;
}

.card-header {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .chart-container {
    height: 280px !important;
  }
}

@media (max-width: 768px) {
  .chart-container {
    height: 250px !important;
  }

  .charts-section h2 {
    font-size: 1.25rem;
    margin-bottom: 1rem;
  }
}

/* Chart loading skeleton */
.h-80 {
  height: 20rem;
}

/* Card header styling */
:deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
}

:deep(.el-card__body) {
  padding: 20px;
}
</style>
