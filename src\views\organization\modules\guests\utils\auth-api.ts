import { http } from "@/utils/http";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { Result } from "@/utils/response";

type r = Result;

// Read Operations
export const getGuests = (uuid: string, params?: any) => {
  return http.request<r>("get", `/api/v1/auth/organizations/${uuid}/guests`, {
    params
  });
};

export const getFindGuests = (uuid: string, params?: any) => {
  return http.request<r>(
    "get",
    `/api/v1/auth/organizations/${uuid}/members/find`,
    { params }
  );
};

export const getAllGuests = (uuid: string, params?: any) => {
  return http.request<r>(
    "get",
    `/api/v1/auth/organizations/${uuid}/guests/all`,
    { params }
  );
};

// Create Operations
export const createGuest = (uuid: string, data: any) => {
  return http.request<r>("post", `/api/v1/auth/organizations/${uuid}/guests`, {
    data: useConvertKeyToSnake(data)
  });
};

// Permanent Delete Operations
export const deleteGuestPermanent = (uuid: string, id: number) => {
  return http.request<r>(
    "delete",
    `/api/v1/auth/organizations/${uuid}/guests/${id}/force`
  );
};

export const bulkDeleteGuestsPermanent = (
  uuid: string,
  data: { ids: number[] }
) => {
  return http.request<r>(
    "delete",
    `/api/v1/auth/organizations/${uuid}/guests/bulk/force`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};
