import { reactive, ref } from "vue";
import { useConvertKeyToCamel, useConvertKeyToSnake } from "@/utils/helpers";
import { $t } from "@/plugins/i18n";
import type { FieldValues } from "plus-pro-components";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";
import {
  getOrganizations,
  deleteOrganization,
  bulkDeleteOrganizations,
  deleteOrganizationPermanent,
  bulkDeleteOrganizationsPermanent,
  restoreOrganization,
  bulkRestoreOrganizations,
  createOrganization,
  updateOrganizationById
} from "@/views/organization/shared/utils/auth-api";
import type { OrganizationFilterProps } from "@/views/organization/shared/utils/type";

export function useOrganizationHook() {
  /*
   ***************************
   *   Data/State Management
   ***************************
   */
  const loading = ref(false);
  const filterRef = ref<OrganizationFilterProps>({ isTrashed: "no" });
  const pagination = reactive({
    total: 0,
    pageSize: 20,
    currentPage: 1,
    pageSizes: [10, 20, 50, 100, 200],
    background: true,
    layout: "total, sizes, prev, pager, next, jumper"
  });
  const multipleSelection = ref([]);
  const records = ref([]);
  const sort = ref({ sortBy: "createdAt", sortOrder: "asc" });
  const filterVisible = ref(false);
  const drawerVisible = ref(false);
  const drawerValues = ref<FieldValues>({
    status: "active",
    type: "company",
    isVerified: false,
    employeeCount: 0,
    settings: {},
    metadata: {}
  });
  const organizationFormRef = ref();

  /*
   ***************************
   *   API Data Fetching
   ***************************
   */
  const fnGetOrganizations = async () => {
    loading.value = true;
    try {
      const response = await getOrganizations(
        useConvertKeyToSnake({
          ...filterRef.value,
          order: `${useConvertKeyToSnake(sort.value.sortBy)}:${sort.value.sortOrder}`,
          page: pagination.currentPage,
          limit: pagination.pageSize
        })
      );
      records.value = useConvertKeyToCamel(response.data);
      pagination.total = response.total;
    } catch (e) {
      message(e.response?.data?.message || e?.message || $t("Get failed"), {
        type: "error"
      });
    } finally {
      loading.value = false;
    }
  };

  /*
   ***************************
   *   Table Event Handlers
   ***************************
   */
  const fnHandleSelectionChange = (val: any) => {
    multipleSelection.value = val;
  };

  const fnHandleSortChange = async ({ prop, order }) => {
    sort.value.sortBy = prop;
    sort.value.sortOrder = order === "ascending" ? "asc" : "desc";
    await fnGetOrganizations();
  };

  const fnHandlePageChange = async () => {
    await fnGetOrganizations();
  };

  const fnHandleSizeChange = async (val: number) => {
    pagination.pageSize = val;
    pagination.currentPage = 1;
    await fnGetOrganizations();
  };

  /*
   ***************************
   *   Delete handlers and actions
   ***************************
   */
  const handleDelete = async (uuid: string) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to delete this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      await fnHandleDelete(uuid);
    } catch {}
  };
  const fnHandleDelete = async (uuid: string) => {
    try {
      loading.value = true;
      const response = await deleteOrganization(uuid);
      if (response.success) {
        message(response.message || $t("Delete successful"), {
          type: "success"
        });
        await fnGetOrganizations();
        return true;
      }
      message(response.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Delete failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };
  const handleBulkDelete = async (tableRef?: any) => {
    const ids = multipleSelection.value.map(item => item.uuid);
    if (ids.length === 0) {
      message($t("Please select items to delete"), {
        type: "warning"
      });
      return;
    }
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to delete selected items?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const success = await fnHandleBulkDelete(ids);
      if (success && tableRef?.clearSelection) {
        tableRef.clearSelection();
      }
    } catch {}
  };
  const fnHandleBulkDelete = async (ids: string[]) => {
    try {
      loading.value = true;
      const response = await bulkDeleteOrganizations({ ids });
      if (response.success) {
        message(response.message || $t("Delete successful"), {
          type: "success"
        });
        await fnGetOrganizations();
        return true;
      }
      message(response.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(error.response?.data?.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } finally {
      loading.value = false;
    }
  };

  /*
   ***************************
   *   Permanent Delete handlers and actions
   ***************************
   */
  const handlePermanentDelete = async (uuid: string) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to delete this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      await fnHandlePermanentDelete(uuid);
    } catch {}
  };
  const fnHandlePermanentDelete = async (uuid: string) => {
    try {
      loading.value = true;
      const response = await deleteOrganizationPermanent(uuid);
      if (response.success) {
        message(response.message || $t("Delete successful"), {
          type: "success"
        });
        await fnGetOrganizations();
        return true;
      }
      message(response.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Delete failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };
  const handleBulkPermanentDelete = async (tableRef?: any) => {
    const ids = multipleSelection.value.map(item => item.uuid);
    if (ids.length === 0) {
      message($t("Please select items to delete"), {
        type: "warning"
      });
      return;
    }
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to delete selected items?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const success = await fnHandleBulkPermanentDelete(ids);
      if (success && tableRef?.clearSelection) {
        tableRef.clearSelection();
      }
    } catch {}
  };
  const fnHandleBulkPermanentDelete = async (ids: string[]) => {
    try {
      loading.value = true;
      const response = await bulkDeleteOrganizationsPermanent({ ids });
      if (response.success) {
        message(response.message || $t("Delete successful"), {
          type: "success"
        });
        await fnGetOrganizations();
        return true;
      }
      message(response.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      console.error("Bulk delete Organizations error:", error);
      message(error.response?.data?.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } finally {
      loading.value = false;
    }
  };

  /*
   ***************************
   *   Restore handlers and actions
   ***************************
   */
  const handleRestore = async (uuid: string) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to restore this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      await fnHandleRestore(uuid);
    } catch {}
  };
  const fnHandleRestore = async (uuid: string) => {
    try {
      loading.value = true;
      const response = await restoreOrganization(uuid);
      if (response.success) {
        message(response.message || $t("Restore successful"), {
          type: "success"
        });
        await fnGetOrganizations();
        return true;
      }
      message(response.message || $t("Restore failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message ||
          error?.message ||
          $t("Restores failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };
  const handleBulkRestore = async (tableRef?: any) => {
    const ids = multipleSelection.value.map(item => item.uuid);
    if (ids.length === 0) {
      message($t("Please select items to restore"), {
        type: "warning"
      });
      return;
    }
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to restore selected items?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const success = await fnHandleBulkRestore(ids);
      if (success && tableRef?.clearSelection) {
        tableRef.clearSelection();
      }
    } catch {}
  };
  const fnHandleBulkRestore = async (ids: string[]) => {
    try {
      loading.value = true;
      const response = await bulkRestoreOrganizations({ ids });
      if (response.success) {
        message(response.message || $t("Restore successful"), {
          type: "success"
        });
        await fnGetOrganizations();
        return true;
      }
      message(response.message || $t("Restore failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(error.response?.data?.message || $t("Restore failed"), {
        type: "error"
      });
      return false;
    } finally {
      loading.value = false;
    }
  };

  /*
   ***************************
   *   Form handlers and actions
   ***************************
   */
  const handleSubmit = async (values: FieldValues) => {
    if (values.uuid != null) {
      await fnHandleUpdateOrganization(String(values.uuid), values);
    } else {
      const success = await fnHandleCreateOrganization(values);
      if (success) {
        drawerValues.value = {
          status: "active",
          type: "company",
          isVerified: false,
          employeeCount: 0,
          settings: {},
          metadata: {}
        };
        organizationFormRef.value?.resetForm();
      }
    }
  };
  const handleFilter = async (values: OrganizationFilterProps) => {
    filterRef.value = values;
    await fnGetOrganizations();
  };
  const fnHandleCreateOrganization = async (formData: FieldValues) => {
    try {
      loading.value = true;
      const response = await createOrganization(formData);
      if (response.success) {
        message(response.message || $t("Create successful"), {
          type: "success"
        });
        await fnGetOrganizations();
        return true;
      }
      message(response?.message || $t("Create failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Create failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  const fnHandleUpdateOrganization = async (
    uuid: string,
    formData: FieldValues
  ) => {
    try {
      loading.value = true;
      const response = await updateOrganizationById(uuid, formData);
      if (response.success) {
        message(response.message || $t("Update successful"), {
          type: "success"
        });
        await fnGetOrganizations();
        return true;
      }
      message(response?.message || $t("Update failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Update failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  /*
   ***************************
   *   Return Hook Interface
   ***************************
   */
  return {
    // Data/State
    loading,
    filterRef,
    pagination,
    records,
    multipleSelection,
    sort,
    filterVisible,
    drawerVisible,
    drawerValues,
    organizationFormRef,

    // API Handlers
    fnGetOrganizations,
    fnHandleCreateOrganization,
    fnHandleUpdateOrganization,
    fnHandleDelete,
    fnHandleBulkDelete,

    // Table Event Handlers
    fnHandleSelectionChange,
    fnHandleSortChange,
    fnHandlePageChange,
    fnHandleSizeChange,

    // UI Action Handlers
    handleDelete,
    handleBulkDelete,
    handlePermanentDelete,
    handleBulkPermanentDelete,
    handleRestore,
    handleBulkRestore,

    // Form Handlers
    handleSubmit,
    handleFilter
  };
}
