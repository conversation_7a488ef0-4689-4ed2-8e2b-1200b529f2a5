// src/services/soketi.service.ts
import { computed, reactive } from "vue";
import Pusher, { type Channel } from "pusher-js";
import { ElMessage } from "element-plus";
import { $t } from "@/plugins/i18n";
import { getToken } from "@/utils/auth";

// ===== INTERFACES & TYPES =====
interface SoketiConfig {
  appKey: string;
  host: string;
  port: number;
  useTLS: boolean;
  apiUrl: string;
  cluster?: string;
  enabledTransports?: [];
  maxReconnectAttempts?: number;
  baseReconnectDelay?: number;
  maxReconnectDelay?: number;
}

interface ConnectionState {
  connected: boolean;
  connecting: boolean;
  reconnecting: boolean;
  error: string | null;
  lastConnected: Date | null;
  reconnectAttempts: number;
  connectionId: string | null;
}

interface ChannelSubscription {
  channel: Channel;
  events: Map<string, Set<EventCallback>>;
  subscribed: boolean;
  lastActivity: Date;
}

interface EventCallback<T = any> {
  (data: T): void;
}

// ===== ENUMS =====
enum ConnectionStates {
  CONNECTING = "connecting",
  CONNECTED = "connected",
  DISCONNECTED = "disconnected",
  UNAVAILABLE = "unavailable"
}

enum ChannelTypes {
  PUBLIC = "public",
  PRIVATE = "private",
  PRESENCE = "presence"
}

// ===== UTILITY FUNCTIONS =====
class Logger {
  private static readonly PREFIX = "[Soketi]";

  static info(message: string, ...args: any[]): void {
    console.log(`${this.PREFIX} ${message}`, ...args);
  }

  static warn(message: string, ...args: any[]): void {
    console.warn(`${this.PREFIX} ${message}`, ...args);
  }

  static error(message: string, ...args: any[]): void {
    console.error(`${this.PREFIX} ${message}`, ...args);
  }

  static debug(message: string, ...args: any[]): void {
    // Debug logs are disabled in this production-optimized version.
    console.debug(`${this.PREFIX} ${message}`, ...args);
  }
}

class ConfigValidator {
  private static configCache: SoketiConfig | null = null;
  private static configPromise: Promise<SoketiConfig | null> | null = null;
  private static readonly STORAGE_KEY = "pusher";

  static async loadFromApi(): Promise<SoketiConfig | null> {
    if (this.configCache) {
      return this.configCache;
    }
    if (this.configPromise) {
      return this.configPromise;
    }

    this.configPromise = this.fetchConfig();
    const config = await this.configPromise;
    this.configPromise = null;

    if (config) {
      this.configCache = config;
    }
    return config;
  }

  private static async fetchConfig(): Promise<SoketiConfig | null> {
    try {
      // Removed dependency on import.meta.env
      const apiBaseUrl = "/";
      const response = await fetch(`${apiBaseUrl}pusher.json`, {
        method: "GET",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json"
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const validatedConfig = this.validateAndTransformConfig(data);

      if (validatedConfig) {
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(validatedConfig));
      }

      return validatedConfig;
    } catch (error: any) {
      Logger.error("Failed to load config from API:", error);

      const storedConfig = localStorage.getItem(this.STORAGE_KEY);
      if (storedConfig) {
        Logger.warn(
          "Falling back to last known good configuration from localStorage"
        );
        try {
          return JSON.parse(storedConfig) as SoketiConfig;
        } catch (e) {
          Logger.error("Failed to parse stored config", e);
          localStorage.removeItem(this.STORAGE_KEY);
        }
      }

      // Final fallback removed, will return null if API and localStorage fail
      Logger.error("All configuration sources failed.");
      return null;
    }
  }

  private static validateAndTransformConfig(data: any): SoketiConfig | null {
    const requiredFields = ["appKey", "host", "port", "apiUrl"];
    const missing = requiredFields.filter(field => !data[field]);

    if (missing.length > 0) {
      Logger.error(
        `Missing required fields in API config: ${missing.join(", ")}`
      );
      return null;
    }

    const port = Number(data.port);
    if (isNaN(port) || port <= 0) {
      Logger.error(`Invalid port number in API config: ${data.port}`);
      return null;
    }

    return {
      appKey: data.appKey,
      host: data.host,
      port,
      useTLS: data.useTLS ?? data.scheme === "https",
      apiUrl: data.apiUrl,
      cluster: data.cluster || "mt1",
      enabledTransports: data.enabledTransports || ["ws", "wss"],
      maxReconnectAttempts: data.maxReconnectAttempts || 10,
      baseReconnectDelay: data.baseReconnectDelay || 1000,
      maxReconnectDelay: data.maxReconnectDelay || 30000
    };
  }

  static clearCache(): void {
    this.configCache = null;
    this.configPromise = null;
  }

  static getChannelType(channelName: string): ChannelTypes {
    if (channelName.startsWith("private-")) return ChannelTypes.PRIVATE;
    if (channelName.startsWith("presence-")) return ChannelTypes.PRESENCE;
    return ChannelTypes.PUBLIC;
  }
}

// ===== MAIN SERVICE =====
class SoketiService {
  private static instance: SoketiService;
  private pusher: Pusher | null = null;
  private channels: Map<string, ChannelSubscription> = new Map();
  private config: SoketiConfig | null = null;
  private configLoaded: boolean = false;

  private reconnectTimer: number | null = null;
  private connectionPromise: Promise<boolean> | null = null;
  private eventListeners: Array<() => void> = [];

  public readonly state = reactive<ConnectionState>({
    connected: false,
    connecting: false,
    reconnecting: false,
    error: null,
    lastConnected: null,
    reconnectAttempts: 0,
    connectionId: null
  });

  public readonly isReady = computed(
    () => this.state.connected && !this.state.connecting
  );

  public readonly connectionStatus = computed(() => {
    if (this.state.connecting) return "Đang kết nối...";
    if (this.state.connected) return "Đã kết nối";
    if (this.state.reconnecting) return "Đang kết nối lại...";
    return "Đã ngắt kết nối";
  });

  public static getInstance(): SoketiService {
    if (!SoketiService.instance) {
      SoketiService.instance = new SoketiService();
    }
    return SoketiService.instance;
  }

  private constructor() {
    this.setupWindowEventHandlers();
    Logger.info("Service initialized - config will be loaded on connect");
  }

  private async loadConfig(): Promise<boolean> {
    if (this.config && this.configLoaded) {
      return true;
    }
    try {
      this.config = await ConfigValidator.loadFromApi();
      this.configLoaded = !!this.config;
      if (this.configLoaded) {
        Logger.info("Config loaded successfully:", {
          host: this.config!.host,
          port: this.config!.port,
          useTLS: this.config!.useTLS
        });
      } else {
        Logger.error("Failed to load configuration");
      }
      return this.configLoaded;
    } catch (error: any) {
      Logger.error("Error loading configuration:", error);
      return false;
    }
  }

  public async connect(): Promise<boolean> {
    if (this.connectionPromise) {
      return this.connectionPromise;
    }
    if (this.pusher?.connection.state === ConnectionStates.CONNECTED) {
      return true;
    }
    if (!(await this.loadConfig())) {
      Logger.error("Cannot connect: Failed to load configuration");
      return false;
    }

    this.connectionPromise = this.initializeConnection();
    try {
      return await this.connectionPromise;
    } finally {
      this.connectionPromise = null;
    }
  }

  private async initializeConnection(): Promise<boolean> {
    return new Promise(resolve => {
      if (!this.config) {
        resolve(false);
        return;
      }
      Logger.info("Initializing connection...");
      this.updateState({
        connecting: true,
        error: null,
        reconnecting: this.state.reconnectAttempts > 0
      });
      try {
        this.pusher = new Pusher(this.config.appKey, {
          wsHost: this.config.host,
          wsPort: this.config.port,
          wssPort: this.config.port,
          forceTLS: this.config.useTLS,
          enabledTransports: this.config.enabledTransports,
          disableStats: true,
          cluster: this.config.cluster,
          authorizer: channel => ({
            authorize: (socketId, authCallback) => {
              this.authorizeChannel(socketId, channel.name, authCallback);
            }
          })
        });
        this.setupConnectionEvents(resolve);
      } catch (error: any) {
        Logger.error("Failed to initialize Pusher:", error);
        this.handleConnectionError(error.message || "Initialization failed");
        resolve(false);
      }
    });
  }

  private async authorizeChannel(
    socketId: string,
    channelName: string,
    callback: (error: any, data: any) => void
  ): Promise<void> {
    if (!this.config?.apiUrl) {
      callback(new Error("API URL not configured for authorization"), null);
      return;
    }
    if (ConfigValidator.getChannelType(channelName) === ChannelTypes.PUBLIC) {
      callback(null, {});
      return;
    }

    try {
      Logger.debug(`Authorizing channel: ${channelName}`);
      const token = getToken()?.accessToken ?? getToken();
      if (!token) throw new Error("Authentication token not available");

      const response = await fetch(
        `${this.config.apiUrl}api/broadcasting/auth`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
            Authorization: `Bearer ${token}`,
            "X-Requested-With": "XMLHttpRequest"
          },
          body: JSON.stringify({
            socket_id: socketId,
            channel_name: channelName
          })
        }
      );

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }
      const data = await response.json();
      Logger.debug(`Authorization successful for ${channelName}`);
      callback(null, data);
    } catch (error: any) {
      Logger.error(`Authorization failed for ${channelName}:`, error);
      if (error.message.includes("401")) {
        ElMessage.error($t("Authentication required"));
      } else if (error.message.includes("403")) {
        ElMessage.error(
          $t("Permission denied for channel: {channel}", {
            channel: channelName
          })
        );
      } else {
        ElMessage.error($t("Authorization failed"));
      }
      callback(error, null);
    }
  }

  private setupConnectionEvents(resolve: (success: boolean) => void): void {
    if (!this.pusher) return;
    let resolved = false;
    const resolveOnce = (success: boolean) => {
      if (!resolved) {
        resolved = true;
        clearTimeout(timeoutId);
        resolve(success);
      }
    };

    this.pusher.connection.bind("connected", () => {
      Logger.info("Connected successfully");
      this.updateState({
        connected: true,
        connecting: false,
        reconnecting: false,
        error: null,
        lastConnected: new Date(),
        reconnectAttempts: 0,
        connectionId: this.pusher?.connection.socket_id || null
      });
      this.clearReconnectTimer();
      this.resubscribeChannels();
      resolveOnce(true);
    });

    this.pusher.connection.bind("disconnected", () => {
      Logger.warn("Disconnected from server");
      this.updateState({
        connected: false,
        connecting: false,
        connectionId: null
      });
      if (!this.state.reconnecting) this.scheduleReconnection();
    });

    this.pusher.connection.bind("error", (err: any) => {
      Logger.error("Connection error:", err);
      this.handleConnectionError(
        err.error?.data?.message || err.error?.type || "Connection error"
      );
      resolveOnce(false);
    });

    const timeoutId = setTimeout(() => {
      if (this.state.connecting) {
        this.handleConnectionError("Connection timeout");
        resolveOnce(false);
      }
    }, 15000);
  }

  private resubscribeChannels(): void {
    if (this.channels.size === 0) return;
    Logger.info(`Resubscribing to ${this.channels.size} channels`);
    this.channels.forEach((subscription, channelName) => {
      if (!this.pusher) return;
      this.pusher.unsubscribe(channelName);
      const newChannel = this.pusher.subscribe(channelName);
      subscription.channel = newChannel;
      subscription.subscribed = false;
      this.setupChannelEvents(newChannel, channelName, subscription);
      subscription.events.forEach((callbacks, eventName) => {
        newChannel.bind(eventName, (data: any) => {
          subscription.lastActivity = new Date();
          callbacks.forEach(cb => {
            try {
              cb(data);
            } catch (e) {
              Logger.error("Event callback error:", e);
            }
          });
        });
      });
    });
  }

  private scheduleReconnection(): void {
    if (
      !this.config ||
      this.state.reconnectAttempts >= this.config.maxReconnectAttempts!
    ) {
      Logger.error("Max reconnection attempts reached");
      return;
    }
    this.clearReconnectTimer();
    const delay = Math.min(
      this.config.baseReconnectDelay! *
        Math.pow(2, this.state.reconnectAttempts),
      this.config.maxReconnectDelay!
    );
    Logger.info(
      `Scheduling reconnection in ${delay}ms (attempt ${this.state.reconnectAttempts + 1})`
    );
    this.updateState({ reconnecting: true });
    this.reconnectTimer = window.setTimeout(() => {
      this.updateState({ reconnectAttempts: this.state.reconnectAttempts + 1 });
      this.connect().catch();
    }, delay);
  }

  public disconnect(): void {
    Logger.info("Disconnecting...");
    this.clearReconnectTimer();
    this.unsubscribeAllChannels();
    if (this.pusher) {
      this.pusher.disconnect();
      this.pusher = null;
    }
    this.updateState({
      connected: false,
      connecting: false,
      reconnecting: false,
      reconnectAttempts: 0,
      connectionId: null
    });
  }

  public subscribe<T = any>(
    channelName: string,
    eventName: string,
    callback: EventCallback<T>
  ): boolean {
    if (!this.pusher) {
      Logger.warn(
        "Cannot subscribe: Not connected. Attempting to connect first."
      );
      this.connect().then(success => {
        if (success) this.subscribe(channelName, eventName, callback);
      });
      return false;
    }
    let subscription = this.channels.get(channelName);
    if (!subscription) {
      const channel = this.pusher.subscribe(channelName);
      subscription = {
        channel,
        events: new Map(),
        subscribed: false,
        lastActivity: new Date()
      };
      this.channels.set(channelName, subscription);
      this.setupChannelEvents(channel, channelName, subscription);
    }
    const normalizedEventName = eventName.replace(/^\./, "");
    if (!subscription.events.has(normalizedEventName)) {
      subscription.events.set(normalizedEventName, new Set());
    }
    const callbacks = subscription.events.get(normalizedEventName)!;
    if (callbacks.size === 0) {
      subscription.channel.bind(normalizedEventName, (data: T) => {
        subscription!.lastActivity = new Date();
        callbacks.forEach(cb => {
          try {
            cb(data);
          } catch (e) {
            Logger.error(`Event callback error for ${normalizedEventName}:`, e);
          }
        });
      });
    }
    callbacks.add(callback);
    return true;
  }

  private setupChannelEvents(
    channel: Channel,
    channelName: string,
    subscription: ChannelSubscription
  ): void {
    channel.bind("pusher:subscription_succeeded", () => {
      subscription.subscribed = true;
      Logger.debug(`Successfully subscribed to ${channelName}`);
    });
    channel.bind("pusher:subscription_error", (error: any) => {
      Logger.error(`Subscription failed for ${channelName}:`, error);
      this.channels.delete(channelName);
      if (ConfigValidator.getChannelType(channelName) !== ChannelTypes.PUBLIC) {
        ElMessage.error(
          $t("Failed to subscribe to {channel}", { channel: channelName })
        );
      }
    });
  }

  public broadcast<T = any>(
    channelName: string,
    eventName: string,
    data: T
  ): boolean {
    const subscription = this.channels.get(channelName);
    if (!subscription || !subscription.subscribed) {
      Logger.warn(`Cannot broadcast: Not subscribed to ${channelName}`);
      return false;
    }
    try {
      const clientEventName = eventName.startsWith("client-")
        ? eventName
        : `client-${eventName}`;
      subscription.channel.trigger(clientEventName, data);
      return true;
    } catch (error: any) {
      Logger.error(`Broadcast failed for ${channelName}:`, error);
      return false;
    }
  }

  public unsubscribe(
    channelName: string,
    eventName?: string,
    callback?: EventCallback
  ): void {
    const subscription = this.channels.get(channelName);
    if (!subscription) return;
    if (eventName) {
      const normalizedEventName = eventName.replace(/^\./, "");
      const callbacks = subscription.events.get(normalizedEventName);
      if (callbacks) {
        if (callback) callbacks.delete(callback);
        else callbacks.clear();
        if (callbacks.size === 0) {
          subscription.channel.unbind(normalizedEventName);
          subscription.events.delete(normalizedEventName);
        }
      }
    } else {
      this.unsubscribeChannel(channelName);
    }
  }

  private unsubscribeChannel(channelName: string): void {
    const subscription = this.channels.get(channelName);
    if (!subscription) return;
    subscription.events.forEach((_, eventName) =>
      subscription.channel.unbind(eventName)
    );
    this.pusher?.unsubscribe(channelName);
    this.channels.delete(channelName);
  }

  private unsubscribeAllChannels(): void {
    this.channels.forEach((_, channelName) =>
      this.unsubscribeChannel(channelName)
    );
  }

  private updateState(updates: Partial<ConnectionState>): void {
    Object.assign(this.state, updates);
  }

  private handleConnectionError(message: string): void {
    this.updateState({ error: message, connected: false, connecting: false });
  }

  private clearReconnectTimer(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  private setupWindowEventHandlers(): void {
    const unloadHandler = () => this.destroy();
    const visibilityHandler = () => {
      if (document.hidden) {
        Logger.debug("Page hidden, pausing reconnection");
        this.clearReconnectTimer();
      } else if (!this.state.connected && !this.state.connecting) {
        Logger.debug("Page visible, attempting immediate reconnection");
        this.connect().catch(err => {
          Logger.error("Failed to reconnect on visibility change:", err);
        });
      }
    };
    const onlineHandler = () => {
      if (!this.state.connected) this.connect();
    };
    window.addEventListener("beforeunload", unloadHandler);
    document.addEventListener("visibilitychange", visibilityHandler);
    window.addEventListener("online", onlineHandler);
    this.eventListeners.push(
      () => window.removeEventListener("beforeunload", unloadHandler),
      () => document.removeEventListener("visibilitychange", visibilityHandler),
      () => window.removeEventListener("online", onlineHandler)
    );
  }

  public async reloadConfig(): Promise<boolean> {
    Logger.info("Reloading configuration from API...");
    ConfigValidator.clearCache();
    this.configLoaded = false;
    this.config = null;
    const success = await this.loadConfig();
    if (
      success &&
      this.pusher?.connection.state === ConnectionStates.CONNECTED
    ) {
      Logger.info("Configuration reloaded successfully. Reconnecting...");
      this.disconnect();
      await this.connect();
    }
    return success;
  }

  public destroy(): void {
    Logger.info("Destroying service...");
    this.disconnect();
    this.eventListeners.forEach(cleanup => cleanup());
    this.eventListeners = [];
    SoketiService.instance = null as any;
  }
}

// ===== COMPOSABLE FUNCTION =====
export function useSoketi() {
  const service = SoketiService.getInstance();
  return {
    state: service.state,
    isReady: service.isReady,
    connectionStatus: service.connectionStatus,
    connect: () => service.connect(),
    disconnect: () => service.disconnect(),
    reloadConfig: () => service.reloadConfig(),
    subscribe: service.subscribe.bind(service),
    unsubscribe: service.unsubscribe.bind(service),
    broadcast: service.broadcast.bind(service)
  };
}

// ===== EXPORTS =====
export const soketiService = SoketiService.getInstance();
