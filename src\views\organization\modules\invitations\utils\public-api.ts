import { http } from "@/utils/http";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { Result } from "@/utils/response";

type r = Result;

/*
 ***************************
 *   Public Invitation Operations
 ***************************
 */

/**
 * Accept or decline an organization invitation
 * This is a public endpoint that doesn't require authentication
 * @param token - The invitation token from email
 * @param action - 'accept' or 'decline'
 */
export const acceptInvitation = (data: {
  token: string;
  action: "accept" | "decline";
}) => {
  return http.request<r>("post", "/api/v1/invitations/accept", {
    data: useConvertKeyToSnake(data)
  });
};

/**
 * Get invitation details by token (public endpoint)
 * @param token - The invitation token
 */
export const getInvitationByToken = (token: string) => {
  return http.request<r>("get", `/api/v1/invitations/${token}`);
};

/**
 * Validate invitation token (public endpoint)
 * @param token - The invitation token to validate
 */
export const validateInvitationToken = (token: string) => {
  return http.request<r>("get", `/api/v1/invitations/${token}/validate`);
};
