export type Role = {
  id: number;
  name: string;
  guardName: string;
  displayName?: string;
  description?: string;
  priority?: number;
  isSystem?: boolean;
  status?: "active" | "inactive";
  usersCount?: number;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
};

export type User = {
  id: number;
  username: string;
  firstName: string;
  lastName: string;
  email: string;
  avatar?: string;
  birthday?: string;
  gender?: string;
  phone?: string;
  address?: string;
  geoDivisionId?: number;
  countryId?: number;
  status?: "active" | "inactive" | "suspended" | "banned" | "pending";
  isVerified?: boolean;
  newsletterSubscribed?: boolean;
  roles?: Role[];
  rolesCount?: number;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
};

export type FormItemProps = {
  id?: number | null;
  username?: string;
  password?: string;
  passwordConfirmation?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  avatar?: string;
  birthday?: string;
  gender?: string;
  phone?: string;
  address?: string;
  geoDivisionId?: number;
  countryId?: number;
  status?: "active" | "inactive" | "suspended" | "banned" | "pending";
  isVerified?: boolean;
  newsletterSubscribed?: boolean;
  roles?: number[];
  [key: string]: any;
};

export type UserFilterProps = {
  username?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  gender?: string;
  status?: string;
  isVerified?: boolean;
  roles?: number[];
  isTrashed?: "yes" | "no";
  [key: string]: any;
};
