<script setup lang="ts">
import { ref, computed } from "vue";
import { $t } from "@/plugins/i18n";
import {
  ElCard,
  ElButton,
  ElTag,
  ElProgress,
  ElDescriptions,
  ElDescriptionsItem,
  ElAlert,
  ElDivider,
  ElTable,
  ElTableColumn
} from "element-plus";
import { IconifyIconOnline } from "@/components/ReIcon";

const props = defineProps<{
  settings: any;
  loading: boolean;
}>();

const emit = defineEmits<{
  (e: "upgrade"): void;
  (e: "manage-billing"): void;
  (e: "download-invoice", invoiceId: string): void;
}>();

// Mock billing data
const billingData = ref({
  plan: "Pro",
  status: "active",
  nextBillingDate: "2024-02-15",
  amount: 99,
  currency: "USD",
  usage: {
    bots: { current: 8, limit: 20 },
    members: { current: 12, limit: 50 },
    conversations: { current: 2450, limit: 10000 },
    storage: { current: 2.3, limit: 10 }
  },
  features: [
    { name: "AI Bots", included: true },
    { name: "Team Collaboration", included: true },
    { name: "Advanced Analytics", included: true },
    { name: "API Access", included: true },
    { name: "Priority Support", included: true },
    { name: "Custom Integrations", included: false }
  ],
  invoices: [
    {
      id: "INV-2024-001",
      date: "2024-01-15",
      amount: 99,
      status: "paid",
      downloadUrl: "#"
    },
    {
      id: "INV-2023-012",
      date: "2023-12-15",
      amount: 99,
      status: "paid",
      downloadUrl: "#"
    },
    {
      id: "INV-2023-011",
      date: "2023-11-15",
      amount: 99,
      status: "paid",
      downloadUrl: "#"
    }
  ]
});

const usagePercentage = computed(() => ({
  bots:
    (billingData.value.usage.bots.current /
      billingData.value.usage.bots.limit) *
    100,
  members:
    (billingData.value.usage.members.current /
      billingData.value.usage.members.limit) *
    100,
  conversations:
    (billingData.value.usage.conversations.current /
      billingData.value.usage.conversations.limit) *
    100,
  storage:
    (billingData.value.usage.storage.current /
      billingData.value.usage.storage.limit) *
    100
}));

const getStatusColor = (status: string) => {
  switch (status) {
    case "active":
      return "success";
    case "past_due":
      return "warning";
    case "canceled":
      return "danger";
    default:
      return "info";
  }
};

const getStatusText = (status: string) => {
  switch (status) {
    case "active":
      return $t("Active");
    case "past_due":
      return $t("Past Due");
    case "canceled":
      return $t("Canceled");
    default:
      return $t("Unknown");
  }
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString();
};

const handleUpgrade = () => {
  emit("upgrade");
};

const handleManageBilling = () => {
  emit("manage-billing");
};

const handleDownloadInvoice = (invoiceId: string) => {
  emit("download-invoice", invoiceId);
};
</script>

<template>
  <div class="space-y-6">
    <!-- Current Plan -->
    <ElCard>
      <template #header>
        <div class="flex items-center justify-between">
          <span class="text-lg font-semibold">{{ $t("Current Plan") }}</span>
          <ElTag :type="getStatusColor(billingData.status)" size="large">
            {{ getStatusText(billingData.status) }}
          </ElTag>
        </div>
      </template>

      <ElDescriptions :column="2" border>
        <ElDescriptionsItem :label="$t('Plan')">
          <div class="flex items-center space-x-2">
            <span class="font-medium">{{ billingData.plan }}</span>
            <ElButton type="primary" size="small" @click="handleUpgrade">
              {{ $t("Upgrade") }}
            </ElButton>
          </div>
        </ElDescriptionsItem>
        <ElDescriptionsItem :label="$t('Next Billing Date')">
          {{ formatDate(billingData.nextBillingDate) }}
        </ElDescriptionsItem>
        <ElDescriptionsItem :label="$t('Amount')">
          ${{ billingData.amount }}/{{ $t("month") }}
        </ElDescriptionsItem>
        <ElDescriptionsItem :label="$t('Payment Method')">
          <div class="flex items-center space-x-2">
            <IconifyIconOnline icon="logos:mastercard" width="24" />
            <span>•••• •••• •••• 4242</span>
            <ElButton type="text" size="small" @click="handleManageBilling">
              {{ $t("Manage") }}
            </ElButton>
          </div>
        </ElDescriptionsItem>
      </ElDescriptions>
    </ElCard>

    <!-- Usage Overview -->
    <ElCard>
      <template #header>
        <span class="text-lg font-semibold">{{ $t("Usage Overview") }}</span>
      </template>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <div class="flex justify-between items-center mb-2">
            <span class="text-sm font-medium">{{ $t("AI Bots") }}</span>
            <span class="text-sm text-gray-500">
              {{ billingData.usage.bots.current }}/{{
                billingData.usage.bots.limit
              }}
            </span>
          </div>
          <ElProgress
            :percentage="usagePercentage.bots"
            :color="usagePercentage.bots > 80 ? '#f56565' : '#48bb78'"
          />
        </div>

        <div>
          <div class="flex justify-between items-center mb-2">
            <span class="text-sm font-medium">{{ $t("Team Members") }}</span>
            <span class="text-sm text-gray-500">
              {{ billingData.usage.members.current }}/{{
                billingData.usage.members.limit
              }}
            </span>
          </div>
          <ElProgress
            :percentage="usagePercentage.members"
            :color="usagePercentage.members > 80 ? '#f56565' : '#48bb78'"
          />
        </div>

        <div>
          <div class="flex justify-between items-center mb-2">
            <span class="text-sm font-medium">{{ $t("Conversations") }}</span>
            <span class="text-sm text-gray-500">
              {{ billingData.usage.conversations.current.toLocaleString() }}/{{
                billingData.usage.conversations.limit.toLocaleString()
              }}
            </span>
          </div>
          <ElProgress
            :percentage="usagePercentage.conversations"
            :color="usagePercentage.conversations > 80 ? '#f56565' : '#48bb78'"
          />
        </div>

        <div>
          <div class="flex justify-between items-center mb-2">
            <span class="text-sm font-medium">{{ $t("Storage") }}</span>
            <span class="text-sm text-gray-500">
              {{ billingData.usage.storage.current }}GB/{{
                billingData.usage.storage.limit
              }}GB
            </span>
          </div>
          <ElProgress
            :percentage="usagePercentage.storage"
            :color="usagePercentage.storage > 80 ? '#f56565' : '#48bb78'"
          />
        </div>
      </div>

      <ElAlert
        v-if="Object.values(usagePercentage).some(p => p > 80)"
        type="warning"
        :title="$t('Usage Warning')"
        :description="
          $t(
            'You are approaching your plan limits. Consider upgrading to avoid service interruption.'
          )
        "
        class="mt-4"
        show-icon
      />
    </ElCard>

    <!-- Plan Features -->
    <ElCard>
      <template #header>
        <span class="text-lg font-semibold">{{ $t("Plan Features") }}</span>
      </template>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div
          v-for="feature in billingData.features"
          :key="feature.name"
          class="flex items-center space-x-3"
        >
          <IconifyIconOnline
            :icon="
              feature.included
                ? 'heroicons:check-circle-20-solid'
                : 'heroicons:x-circle-20-solid'
            "
            :class="feature.included ? 'text-green-500' : 'text-gray-400'"
            width="20"
          />
          <span :class="feature.included ? 'text-gray-900' : 'text-gray-400'">
            {{ feature.name }}
          </span>
        </div>
      </div>
    </ElCard>

    <!-- Billing History -->
    <ElCard>
      <template #header>
        <span class="text-lg font-semibold">{{ $t("Billing History") }}</span>
      </template>

      <ElTable :data="billingData.invoices" style="width: 100%">
        <ElTableColumn prop="id" :label="$t('Invoice ID')" width="150" />
        <ElTableColumn prop="date" :label="$t('Date')" width="120">
          <template #default="{ row }">
            {{ formatDate(row.date) }}
          </template>
        </ElTableColumn>
        <ElTableColumn prop="amount" :label="$t('Amount')" width="100">
          <template #default="{ row }"> ${{ row.amount }} </template>
        </ElTableColumn>
        <ElTableColumn prop="status" :label="$t('Status')" width="100">
          <template #default="{ row }">
            <ElTag :type="getStatusColor(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn :label="$t('Actions')" width="120">
          <template #default="{ row }">
            <ElButton
              type="text"
              size="small"
              @click="handleDownloadInvoice(row.id)"
            >
              <IconifyIconOnline
                icon="heroicons:arrow-down-tray-20-solid"
                class="mr-1"
              />
              {{ $t("Download") }}
            </ElButton>
          </template>
        </ElTableColumn>
      </ElTable>
    </ElCard>
  </div>
</template>

<style scoped>
:deep(.el-card__body) {
  padding: 24px;
}

:deep(.el-descriptions__label) {
  font-weight: 500;
}
</style>
