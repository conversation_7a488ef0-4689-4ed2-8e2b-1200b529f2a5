<script setup lang="ts">
import Motion from "./utils/motion";
import { useRouter } from "vue-router";
import { message } from "@/utils/message";
import { profileRules } from "./utils/rule";
import { computed, reactive, ref, onMounted, onUnmounted } from "vue";
import { useNav } from "@/layout/hooks/useNav";
import type { FormInstance } from "element-plus";
import { useLayout } from "@/layout/hooks/useLayout";
import { useUserStoreHook } from "@/store/modules/user";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import {
  updateCurrentUser,
  updateCurrentUserAvatar,
  sendPasswordChangeOTP,
  changePasswordWithOTP
} from "@/views/auth/api/auth-api";
import type { UserResult } from "@/views/auth/api/auth-api";
import User from "@iconify-icons/ri/user-3-fill";
import Mail from "@iconify-icons/ri/mail-fill";
import Phone from "@iconify-icons/ri/phone-fill";
import Calendar from "@iconify-icons/ri/calendar-fill";
import Lock from "@iconify-icons/ri/lock-fill";
import Camera from "@iconify-icons/ri/camera-fill";
import Save from "@iconify-icons/ri/save-fill";
import Shield from "@iconify-icons/ri/shield-keyhole-fill";
import ArrowLeft from "@iconify-icons/ri/arrow-left-line";
import Refresh from "@iconify-icons/ri/refresh-line";
import { $t } from "@/plugins/i18n";
defineOptions({
  name: "Profile"
});

const router = useRouter();
const loading = ref(false);
const passwordLoading = ref(false);
const avatarLoading = ref(false);
const otpLoading = ref(false);
const disabled = ref(false);
const profileFormRef = ref<FormInstance>();
const passwordFormRef = ref<FormInstance>();
const otpFormRef = ref<FormInstance>();
const avatarUploadRef = ref();
const activeTab = ref("profile");

// OTP related states
const showOtpStep = ref(false);
const otpSent = ref(false);
const otpCountdown = ref(0);
const otpTimer = ref<NodeJS.Timeout | null>(null);

const { initStorage, layoutTheme } = useLayout();
initStorage();

const userStore = useUserStoreHook();
const currentUser = computed(() => userStore.userInfo);

// Settings for appearance tab
const setting = reactive({
  darkMode: false,
  language: "vi"
});

// Computed properties
const fullName = computed(() => {
  if (currentUser.value?.fullName) {
    return currentUser.value.fullName;
  }
  if (currentUser.value?.firstName && currentUser.value?.lastName) {
    return `${currentUser.value.firstName} ${currentUser.value.lastName}`;
  }
  return currentUser.value?.username || $t("User Profile");
});

const avatarUrl = computed(() => {
  return currentUser.value?.avatarUrl || "/img/avatars/avatar-1.jpg";
});

// User object for template binding
const user = computed(() => ({
  fullName: fullName.value,
  username: currentUser.value?.username || "",
  email: currentUser.value?.email || "",
  phone: currentUser.value?.phone || "",
  address: currentUser.value?.address || "",
  avatar: avatarUrl.value
}));

// Profile form data
const profileForm = reactive({
  username: "",
  firstName: "",
  lastName: "",
  email: "",
  phone: "",
  address: "",
  birthday: "",
  gender: "male"
});

// Password form data - chỉ cần new password để gửi OTP
const passwordForm = reactive({
  currentPassword: ""
});

// OTP form data - nhập OTP + new password + confirm password
const otpForm = reactive({
  otp: "",
  newPassword: "",
  confirmPassword: ""
});

// Load user data
const loadUserData = () => {
  if (currentUser.value) {
    profileForm.username = currentUser.value.username || "";
    profileForm.firstName = currentUser.value.firstName || "";
    profileForm.lastName = currentUser.value.lastName || "";
    profileForm.email = currentUser.value.email || "";
    profileForm.phone = currentUser.value.phone || "";
    profileForm.address = currentUser.value.address || "";
    profileForm.birthday = currentUser.value.birthday || "";
    profileForm.gender = currentUser.value.gender || "male";
  }
};

// Update profile
const onUpdateProfile = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  try {
    await formEl.validate();
    loading.value = true;
    console.log("-->:::", profileForm);
    const res: UserResult = await updateCurrentUser({
      firstName: profileForm.firstName,
      lastName: profileForm.lastName,
      phone: profileForm.phone,
      address: profileForm.address,
      birthday: profileForm.birthday,
      gender: profileForm.gender
    });

    if (!res.success) {
      message(res.message, { type: "error" });
      return;
    }

    // Update user store
    await userStore.getUserInfo();

    message(res.message || $t("Profile updated successfully"), {
      type: "success"
    });
  } catch (error: any) {
    message(
      error?.response?.data?.message ||
        error?.message ||
        $t("Failed to update profile"),
      { type: "error" }
    );
  } finally {
    loading.value = false;
  }
};

// Update password with OTP flow - chỉ cần new password để gửi OTP
const onUpdatePassword = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  try {
    await formEl.validate();
    passwordLoading.value = true;

    // Send OTP với new password
    const res: UserResult = await sendPasswordChangeOTP({
      currentPassword: passwordForm.currentPassword
    });

    if (!res.success) {
      message(res.message, { type: "error" });
      return;
    }

    // Show OTP step
    showOtpStep.value = true;
    otpSent.value = true;
    startOtpCountdown();

    message(res.message || $t("OTP sent to your email"), {
      type: "success"
    });
  } catch (error: any) {
    message(
      error?.response?.data?.message ||
        error?.message ||
        $t("Failed to send OTP"),
      { type: "error" }
    );
  } finally {
    passwordLoading.value = false;
  }
};

// Verify OTP and change password
const onVerifyOtpAndChangePassword = async (
  formEl: FormInstance | undefined
) => {
  if (!formEl) return;
  try {
    await formEl.validate();
    otpLoading.value = true;

    const res: UserResult = await changePasswordWithOTP({
      otp: otpForm.otp,
      password: otpForm.newPassword,
      passwordConfirmation: otpForm.confirmPassword
    });

    if (!res.success) {
      message(res.message, { type: "error" });
      return;
    }

    // Reset forms and states
    resetPasswordForms();
    showOtpStep.value = false;
    otpSent.value = false;
    clearOtpTimer();

    message(res.message || $t("Password updated successfully"), {
      type: "success"
    });
  } catch (error: any) {
    message(
      error?.response?.data?.message ||
        error?.message ||
        $t("Failed to update password"),
      { type: "error" }
    );
  } finally {
    otpLoading.value = false;
  }
};

// Resend OTP
const onResendOtp = async () => {
  try {
    otpLoading.value = true;

    const res: UserResult = await sendPasswordChangeOTP({
      currentPassword: passwordForm.currentPassword
    });

    if (!res.success) {
      message(res.message, { type: "error" });
      return;
    }

    otpForm.otp = "";
    startOtpCountdown();

    message(res.message || $t("OTP sent to your email"), {
      type: "success"
    });
  } catch (error: any) {
    message(
      error?.response?.data?.message ||
        error?.message ||
        $t("Failed to send OTP"),
      { type: "error" }
    );
  } finally {
    otpLoading.value = false;
  }
};

// Start OTP countdown
const startOtpCountdown = () => {
  clearOtpTimer();
  otpCountdown.value = 60;
  otpTimer.value = setInterval(() => {
    otpCountdown.value--;
    if (otpCountdown.value <= 0) {
      clearOtpTimer();
    }
  }, 1000);
};

// Clear OTP timer
const clearOtpTimer = () => {
  if (otpTimer.value) {
    clearInterval(otpTimer.value);
    otpTimer.value = null;
  }
  otpCountdown.value = 0;
};

// Reset password forms
const resetPasswordForms = () => {
  passwordForm.currentPassword = "";
  otpForm.otp = "";
  otpForm.newPassword = "";
  otpForm.confirmPassword = "";
};

// Cancel OTP process
const onCancelOtp = () => {
  showOtpStep.value = false;
  otpSent.value = false;
  clearOtpTimer();
  otpForm.otp = "";
};

// Handle avatar upload
const handleAvatarUpload = async (file: any) => {
  if (!beforeAvatarUpload(file)) {
    return false;
  }

  try {
    avatarLoading.value = true;
    const formData = new FormData();
    formData.append("avatar", file.raw || file);

    const res: UserResult = await updateCurrentUserAvatar(formData);

    if (!res.success) {
      message(res.message, { type: "error" });
      return false;
    }

    // Update user store
    await userStore.getUserInfo();
    message(res.message || $t("Avatar updated successfully"), {
      type: "success"
    });
    return true;
  } catch (error: any) {
    message(
      error?.response?.data?.message ||
        error?.message ||
        $t("Failed to update avatar"),
      { type: "error" }
    );
    return false;
  } finally {
    avatarLoading.value = false;
  }
};

const beforeAvatarUpload = (file: any) => {
  const fileToCheck = file.raw || file;
  const isImage = fileToCheck.type.startsWith("image/");
  const isLt2M = fileToCheck.size / 1024 / 1024 < 2;

  if (!isImage) {
    message($t("Avatar must be an image"), { type: "error" });
    return false;
  }
  if (!isLt2M) {
    message($t("Avatar size must be less than 2MB"), { type: "error" });
    return false;
  }
  return true;
};

// Dark mode toggle
const toggleDarkMode = () => {
  setting.darkMode = !setting.darkMode;
  // Implement dark mode toggle logic here if needed
  // This would typically involve updating the theme in your layout store
};

onMounted(() => {
  loadUserData();
  setting.darkMode = layoutTheme.value?.darkMode || false;
});

onUnmounted(() => {
  clearOtpTimer();
});
</script>

<template>
  <Motion>
    <div class="main">
      <div ref="contentRef" class="flex flex-wrap gap-4">
        <!-- === CỘT BÊN TRÁI === -->
        <div class="w-full lg:max-w-[310px] flex flex-col gap-8">
          <!-- Card Avatar và Tên -->
          <el-card shadow="never">
            <div class="flex flex-col items-center p-6">
              <!-- Bọc avatar bằng el-upload -->
              <el-upload
                ref="avatarUploadRef"
                class="avatar-uploader"
                action="#"
                :show-file-list="false"
                :auto-upload="false"
                :on-change="handleAvatarUpload"
                :before-upload="beforeAvatarUpload"
                accept="image/*"
              >
                <div class="relative cursor-pointer">
                  <el-avatar
                    :size="120"
                    :src="avatarUrl"
                    alt="User Avatar"
                    class="border-4 border-white shadow-lg"
                  />
                  <div
                    class="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-40 flex items-center justify-center text-white text-sm opacity-0 hover:opacity-100 transition-opacity duration-300 rounded-full"
                  >
                    <component
                      :is="useRenderIcon(Camera)"
                      class="text-2xl mb-1"
                    />
                    <span class="block">{{ $t("Change") }}</span>
                  </div>
                </div>
              </el-upload>
              <h2 class="text-2xl font-bold text-gray-800 mt-4">
                {{ user.fullName }}
              </h2>
              <p class="text-gray-500">@{{ user.username }}</p>
            </div>
          </el-card>
        </div>

        <!-- === CỘT BÊN PHẢI === -->
        <div class="w-full lg:flex-1">
          <el-card shadow="never">
            <el-tabs v-model="activeTab" class="p-4">
              <!-- Tab Profile -->
              <el-tab-pane :label="$t('Profile')" name="profile">
                <h3 class="text-xl font-semibold text-gray-800 mb-6">
                  {{ $t("Personal Information") }}
                </h3>
                <el-form
                  ref="profileFormRef"
                  class="max-w-3xl mx-auto"
                  :model="profileForm"
                  :rules="profileRules"
                  label-position="top"
                  require-asterisk-position="right"
                >
                  <el-row :gutter="24">
                    <el-col :span="12">
                      <el-form-item :label="$t('First Name')" prop="firstName">
                        <el-input
                          v-model="profileForm.firstName"
                          :prefix-icon="useRenderIcon(User)"
                          :placeholder="$t('Enter first name')"
                        />
                      </el-form-item>
                    </el-col>

                    <el-col :span="12">
                      <el-form-item :label="$t('Last Name')" prop="lastName">
                        <el-input
                          v-model="profileForm.lastName"
                          :prefix-icon="useRenderIcon(User)"
                          :placeholder="$t('Enter last name')"
                        />
                      </el-form-item>
                    </el-col>

                    <el-col :span="12">
                      <el-form-item :label="$t('Birthday')" prop="birthday">
                        <el-date-picker
                          v-model="profileForm.birthday"
                          type="date"
                          :placeholder="$t('Select birthday')"
                          format="DD/MM/YYYY"
                          value-format="YYYY-MM-DD"
                          class="!w-full"
                          :prefix-icon="useRenderIcon(Calendar)"
                        />
                      </el-form-item>
                    </el-col>

                    <el-col :span="12">
                      <el-form-item :label="$t('Gender')" prop="gender">
                        <el-select v-model="profileForm.gender">
                          <el-option :label="$t('Male')" value="male">
                            {{ $t("Male") }}
                          </el-option>
                          <el-option :label="$t('Female')" value="female">
                            {{ $t("Female") }}
                          </el-option>
                          <el-option :label="$t('Other')" value="other">
                            {{ $t("Other") }}
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>

                    <el-col :span="12">
                      <el-form-item :label="$t('Email')">
                        <el-input
                          v-model="profileForm.email"
                          disabled
                          :prefix-icon="useRenderIcon(Mail)"
                          :placeholder="$t('Email')"
                        />
                      </el-form-item>
                    </el-col>

                    <el-col :span="12">
                      <el-form-item :label="$t('Phone')" prop="phone">
                        <el-input
                          v-model="profileForm.phone"
                          :prefix-icon="useRenderIcon(Phone)"
                          :placeholder="$t('Enter phone number')"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="24">
                      <el-form-item :label="$t('Address')" prop="address">
                        <el-input
                          v-model="profileForm.address"
                          type="textarea"
                          :rows="3"
                          :placeholder="$t('Enter address')"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <div class="flex items-center justify-end">
                    <el-button
                      type="primary"
                      :loading="loading"
                      :icon="useRenderIcon(Save)"
                      @click="onUpdateProfile(profileFormRef)"
                    >
                      {{ $t("Save Changes") }}
                    </el-button>
                  </div>
                </el-form>
              </el-tab-pane>

              <!-- Tab Password -->
              <el-tab-pane :label="$t('Change Password')" name="password">
                <h3 class="text-xl font-semibold text-gray-800 mb-6">
                  {{ $t("Change Password") }}
                </h3>

                <!-- Step 1: New Password Form -->
                <div v-if="!showOtpStep" class="max-w-md mx-auto">
                  <div class="text-center mb-6">
                    <p class="text-gray-600 text-sm">
                      {{ $t("Enter your current password to receive OTP") }}
                    </p>
                  </div>
                  <el-form
                    ref="passwordFormRef"
                    :model="passwordForm"
                    :rules="profileRules"
                    label-position="top"
                    require-asterisk-position="right"
                  >
                    <el-form-item
                      :label="$t('Current Password')"
                      prop="currentPassword"
                    >
                      <el-input
                        v-model="passwordForm.currentPassword"
                        type="password"
                        show-password
                        :prefix-icon="useRenderIcon(Lock)"
                        :placeholder="$t('Enter current password')"
                      />
                    </el-form-item>

                    <div class="flex items-center justify-end">
                      <el-button
                        type="primary"
                        :loading="passwordLoading"
                        :icon="useRenderIcon(Save)"
                        @click="onUpdatePassword(passwordFormRef)"
                      >
                        {{ $t("Send OTP") }}
                      </el-button>
                    </div>
                  </el-form>
                </div>

                <!-- Step 2: OTP Verification -->
                <div v-else class="max-w-md mx-auto">
                  <div class="text-center mb-6">
                    <div
                      class="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4"
                    >
                      <component
                        :is="useRenderIcon(Shield)"
                        class="text-2xl text-blue-600"
                      />
                    </div>
                    <h4 class="text-lg font-semibold text-gray-800 mb-2">
                      {{ $t("Verify OTP") }}
                    </h4>
                    <p class="text-gray-600 text-sm">
                      {{
                        $t(
                          "We've sent a 6-digit verification code to your email"
                        )
                      }}
                    </p>
                  </div>

                  <el-form
                    ref="otpFormRef"
                    :model="otpForm"
                    :rules="profileRules"
                    label-position="top"
                    require-asterisk-position="right"
                  >
                    <el-form-item :label="$t('Verification Code')" prop="otp">
                      <el-input
                        v-model="otpForm.otp"
                        :prefix-icon="useRenderIcon(Shield)"
                        :placeholder="$t('Enter 6-digit code')"
                        maxlength="6"
                        class="text-center"
                        style="letter-spacing: 0.5em"
                      />
                    </el-form-item>

                    <el-form-item
                      :label="$t('New Password')"
                      prop="newPassword"
                    >
                      <el-input
                        v-model="otpForm.newPassword"
                        type="password"
                        show-password
                        :prefix-icon="useRenderIcon(Lock)"
                        :placeholder="$t('Enter new password')"
                      />
                    </el-form-item>

                    <el-form-item
                      :label="$t('Confirm New Password')"
                      prop="confirmPassword"
                    >
                      <el-input
                        v-model="otpForm.confirmPassword"
                        type="password"
                        show-password
                        :prefix-icon="useRenderIcon(Lock)"
                        :placeholder="$t('Confirm new password')"
                      />
                    </el-form-item>

                    <div class="flex items-center justify-between mb-4">
                      <el-button
                        text
                        :icon="useRenderIcon(ArrowLeft)"
                        @click="onCancelOtp"
                      >
                        {{ $t("Back") }}
                      </el-button>

                      <el-button v-if="otpCountdown > 0" text disabled>
                        {{ $t("Resend in") }} {{ otpCountdown }}s
                      </el-button>
                      <el-button
                        v-else
                        text
                        :loading="otpLoading"
                        :icon="useRenderIcon(Refresh)"
                        @click="onResendOtp"
                      >
                        {{ $t("Resend OTP") }}
                      </el-button>
                    </div>

                    <div class="flex items-center justify-end">
                      <el-button
                        type="primary"
                        :loading="otpLoading"
                        :icon="useRenderIcon(Save)"
                        @click="onVerifyOtpAndChangePassword(otpFormRef)"
                      >
                        {{ $t("Update Password") }}
                      </el-button>
                    </div>
                  </el-form>
                </div>
              </el-tab-pane>
            </el-tabs>
          </el-card>
        </div>
      </div>
    </div>
  </Motion>
</template>

<style scoped>
.profile-container {
  min-height: calc(100vh - 200px);
  background: var(--el-bg-color-page);
}

.flex-c {
  display: flex;
  align-items: center;
}
</style>

<style lang="scss" scoped>
:deep(.el-input-group__append, .el-input-group__prepend) {
  padding: 0;
}

:deep(.el-upload) {
  border: none;
  border-radius: 50%;
}

:deep(.el-avatar) {
  border: 4px solid #fff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

:deep(.el-card) {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

:deep(.el-form-item__label) {
  font-weight: 600;
  color: var(--el-text-color-primary);
}
</style>
