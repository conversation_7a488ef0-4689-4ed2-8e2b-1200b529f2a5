<template>
  <div class="social-callback-page">
    <div class="callback-container">
      <!-- Loading State -->
      <div v-if="isProcessing" class="loading-section">
        <div class="loading-spinner">
          <el-icon class="rotating" :size="48">
            <Loading />
          </el-icon>
        </div>
        <h2 class="loading-title">{{ $t("Processing social login...") }}</h2>
        <p class="loading-description">
          {{ $t("Please wait while we complete your authentication.") }}
        </p>
        <div class="progress-steps">
          <div class="step" :class="{ active: currentStep >= 1 }">
            {{ $t("Verifying credentials") }}
          </div>
          <div class="step" :class="{ active: currentStep >= 2 }">
            {{ $t("Loading user information") }}
          </div>
          <div class="step" :class="{ active: currentStep >= 3 }">
            {{ $t("Initializing application") }}
          </div>
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="error-section">
        <div class="error-icon">
          <el-icon :size="64" color="#f56c6c">
            <CircleCloseFilled />
          </el-icon>
        </div>
        <h2 class="error-title">{{ $t("Social Login Failed") }}</h2>
        <p class="error-message">{{ error }}</p>
        <div class="error-actions">
          <el-button type="primary" @click="goToLogin">
            {{ $t("Back to Login") }}
          </el-button>
          <el-button @click="retryLogin" v-if="canRetry">
            {{ $t("Try Again") }}
          </el-button>
        </div>
      </div>

      <!-- Success State (brief) -->
      <div v-else-if="isSuccess" class="success-section">
        <div class="success-icon">
          <el-icon :size="64" color="#67c23a">
            <CircleCheckFilled />
          </el-icon>
        </div>
        <h2 class="success-title">{{ $t("Social login successful") }}</h2>
        <p class="success-message">{{ $t("Redirecting to your dashboard...") }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElIcon, ElButton } from 'element-plus';
import { Loading, CircleCloseFilled, CircleCheckFilled } from '@element-plus/icons-vue';

import { socialAuthService } from '@/services/social-auth.service';
import { useUserStoreHook } from '@/store/modules/user';
import { setToken, removeToken } from '@/utils/auth';
import { initRouter, getTopMenu } from '@/router/utils';
import { message } from '@/utils/message';
import { $t } from '@/plugins/i18n';

defineOptions({
  name: "SocialCallback"
});

const router = useRouter();
const route = useRoute();

// Reactive state
const isProcessing = ref(true);
const isSuccess = ref(false);
const error = ref<string>('');
const currentStep = ref(0);
const canRetry = ref(false);

// Process social login callback
onMounted(async () => {
  try {
    console.log('Social callback page mounted');
    console.log('URL params:', route.query);
    
    // Step 1: Verify credentials
    currentStep.value = 1;
    await delay(500); // Small delay for UX
    
    // Process callback parameters
    const urlParams = new URLSearchParams(window.location.search);
    const authResult = socialAuthService.processCallback(urlParams);
    
    if (!authResult.success) {
      throw new Error(authResult.error || 'Authentication failed');
    }
    
    if (!authResult.token) {
      throw new Error('No authentication token received');
    }
    
    // Step 2: Load user information
    currentStep.value = 2;
    await delay(500);
    
    // Set token temporarily
    setToken({ 
      token: authResult.token,
      expiresIn: 300, // 5 minutes
      tokenType: 'Bearer' 
    });
    
    // Fetch complete user information
    const userStore = useUserStoreHook();
    const userResult = await userStore.getUserInfo();
    
    if (!userResult.success) {
      throw new Error(userResult.message || 'Failed to fetch user information');
    }
    
    // Step 3: Initialize application
    currentStep.value = 3;
    await delay(500);
    
    // Initialize router with user permissions
    await initRouter();
    
    // Success state
    isProcessing.value = false;
    isSuccess.value = true;
    
    // Show success message
    message($t("Social login successful"), { type: "success" });
    
    // Restore previous state and redirect
    await handleSuccessfulLogin();
    
  } catch (err: any) {
    console.error('Social login callback error:', err);
    handleLoginError(err);
  }
});

/**
 * Handle successful login and redirect
 */
const handleSuccessfulLogin = async () => {
  try {
    // Restore saved state
    const savedState = socialAuthService.restoreState();
    
    // Determine redirect URL
    const redirectUrl = socialAuthService.getRedirectUrl(
      savedState, 
      getTopMenu(true).path
    );
    
    console.log('Redirecting to:', redirectUrl);
    
    // Small delay to show success state
    await delay(1000);
    
    // Redirect to appropriate page
    await router.replace(redirectUrl);
    
    // Restore form data if available
    if (savedState?.formData) {
      // Wait for page to load then restore form data
      setTimeout(() => {
        socialAuthService.restoreFormData(savedState.formData!);
      }, 100);
    }
    
  } catch (err) {
    console.error('Failed to handle successful login:', err);
    // Fallback to dashboard
    await router.replace(getTopMenu(true).path);
  }
};

/**
 * Handle login error
 */
const handleLoginError = (err: any) => {
  isProcessing.value = false;
  isSuccess.value = false;
  
  // Clear any invalid token
  removeToken();
  
  // Clear saved state
  socialAuthService.clearState();
  
  // Set error message
  error.value = err?.response?.data?.message || 
                err?.message || 
                $t("Social login failed. Please try again.");
  
  // Determine if retry is possible
  canRetry.value = !error.value.includes('cancelled') && 
                   !error.value.includes('denied');
  
  console.error('Social login error:', error.value);
};

/**
 * Go back to login page
 */
const goToLogin = async () => {
  await router.replace('/login');
};

/**
 * Retry social login
 */
const retryLogin = async () => {
  // Get provider from saved state or URL
  const savedState = socialAuthService.restoreState();
  const provider = savedState?.provider || 'google';
  
  try {
    // Clear current error state
    error.value = '';
    isProcessing.value = true;
    currentStep.value = 0;
    
    // Retry social login
    await socialAuthService.initiateSocialLogin(provider, {
      returnUrl: savedState?.returnUrl,
      loginType: savedState?.loginType,
      formData: savedState?.formData
    });
    
  } catch (err: any) {
    handleLoginError(err);
  }
};

/**
 * Utility delay function
 */
const delay = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};
</script>

<style scoped>
.social-callback-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.callback-container {
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 500px;
  width: 100%;
}

/* Loading State */
.loading-section {
  padding: 20px 0;
}

.loading-spinner {
  margin-bottom: 24px;
}

.rotating {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
}

.loading-description {
  font-size: 16px;
  color: #606266;
  margin-bottom: 32px;
}

.progress-steps {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: flex-start;
  max-width: 300px;
  margin: 0 auto;
}

.step {
  font-size: 14px;
  color: #909399;
  padding: 8px 16px;
  border-radius: 6px;
  background: #f5f7fa;
  width: 100%;
  text-align: left;
  transition: all 0.3s ease;
}

.step.active {
  color: #409eff;
  background: #ecf5ff;
  font-weight: 500;
}

/* Error State */
.error-section {
  padding: 20px 0;
}

.error-icon {
  margin-bottom: 24px;
}

.error-title {
  font-size: 24px;
  font-weight: 600;
  color: #f56c6c;
  margin-bottom: 12px;
}

.error-message {
  font-size: 16px;
  color: #606266;
  margin-bottom: 32px;
  line-height: 1.5;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

/* Success State */
.success-section {
  padding: 20px 0;
}

.success-icon {
  margin-bottom: 24px;
}

.success-title {
  font-size: 24px;
  font-weight: 600;
  color: #67c23a;
  margin-bottom: 12px;
}

.success-message {
  font-size: 16px;
  color: #606266;
}

/* Responsive */
@media (max-width: 768px) {
  .callback-container {
    padding: 30px 20px;
    margin: 10px;
  }
  
  .loading-title,
  .error-title,
  .success-title {
    font-size: 20px;
  }
  
  .error-actions {
    flex-direction: column;
  }
  
  .error-actions .el-button {
    width: 100%;
  }
}
</style>
