import { http } from "@/utils/http";
import type { Result } from "@/utils/response";
import type {
  SidebarListParams,
  SidebarListResponse,
  SidebarItem
} from "./type";
import { useConvertKeyToSnake } from "@/utils/helpers";

/**
 * Get sidebar list
 */
export const getSidebarList = (params?: SidebarListParams) => {
  return http.request<Result<SidebarListResponse>>(
    "get",
    "/api/v1/auth/sidebars",
    {
      params
    }
  );
};

/**
 * Get sidebar by ID
 */
export const getSidebarById = (id: number) => {
  return http.request<Result<SidebarItem>>(
    "get",
    `/api/v1/auth/sidebars/${id}`
  );
};

/**
 * Create new sidebar
 */
export const createSidebar = (data: Partial<SidebarItem>) => {
  return http.request<Result<SidebarItem>>("post", "/api/v1/auth/sidebars", {
    data
  });
};

/**
 * Update sidebar by ID
 */
export const updateSidebarById = (id: number, data: Partial<SidebarItem>) => {
  return http.request<Result<SidebarItem>>(
    "put",
    `/api/v1/auth/sidebars/${id}`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

/**
 * Delete sidebar by ID
 */
export const deleteSidebar = (id: number) => {
  return http.request<Result<null>>("delete", `/api/v1/auth/sidebars/${id}`);
};

/**
 * Bulk delete sidebars
 */
export const bulkDeleteSidebars = (ids: number[]) => {
  return http.request<Result<null>>(
    "delete",
    "/api/v1/auth/sidebars/bulk/delete",
    {
      data: { ids }
    }
  );
};

/**
 * Restore sidebar by ID
 */
export const restoreSidebar = (id: number) => {
  return http.request<Result<SidebarItem>>(
    "put",
    `/api/v1/auth/sidebars/${id}/restore`
  );
};

/**
 * Bulk restore sidebars
 */
export const bulkRestoreSidebars = (ids: number[]) => {
  return http.request<Result<null>>(
    "put",
    "/api/v1/auth/sidebars/bulk/restore",
    {
      data: { ids }
    }
  );
};

/**
 * Permanent delete sidebar by ID
 */
export const forceDeleteSidebar = (id: number) => {
  return http.request<Result<null>>(
    "delete",
    `/api/v1/auth/sidebars/${id}/force`
  );
};

/**
 * Bulk force delete sidebars
 */
export const bulkForceDeleteSidebars = (ids: number[]) => {
  return http.request<Result<null>>(
    "delete",
    "/api/v1/auth/sidebars/bulk/force",
    {
      data: { ids }
    }
  );
};
