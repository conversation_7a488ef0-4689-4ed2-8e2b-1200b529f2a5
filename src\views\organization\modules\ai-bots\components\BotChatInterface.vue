<script setup lang="ts">
import { Plus, Delete } from "@element-plus/icons-vue";
import { $t } from "@/plugins/i18n";

interface Props {
  greetingMessage?: string;
  starterMessages?: string[];
  name?: string;
  logoUrl?: string;
  loadingGreeting?: boolean;
  loadingStarters?: boolean;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (e: "update:greetingMessage", value: string): void;
  (e: "update:starterMessages", value: string[]): void;
  (e: "generateGreeting"): void;
  (e: "generateStarters"): void;
}>();

const handleGreetingUpdate = (value: string) => {
  emit("update:greetingMessage", value);
};

const updateStarter = (index: number, value: string) => {
  const newStarters = [...(props.starterMessages || [])];
  newStarters[index] = value;
  emit("update:starterMessages", newStarters);
};

const addStarter = () => {
  const newStarters = [...(props.starterMessages || []), ""];
  emit("update:starterMessages", newStarters);
};

const removeStarter = (index: number) => {
  const newStarters = [...(props.starterMessages || [])];
  newStarters.splice(index, 1);
  emit("update:starterMessages", newStarters);
};

const handleGenerateGreeting = () => {
  emit("generateGreeting");
};

const handleGenerateStarters = () => {
  emit("generateStarters");
};
</script>

<template>
  <div class="card">
    <h2 class="section-title">{{ $t("Chat Interface") }}</h2>
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <div class="space-y-6">
        <el-form-item :label="$t('Welcome Message')">
          <el-input
            :model-value="props.greetingMessage"
            :placeholder="$t('Example: Hello! How can I help you?')"
            type="textarea"
            :rows="3"
            @update:model-value="handleGreetingUpdate"
          >
            <template #append>
              <el-button
                class="gemini-button"
                :loading="props.loadingGreeting"
                @click="handleGenerateGreeting"
              >
                {{ $t("✨ Generate") }}
              </el-button>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item>
          <template #label>
            <div class="flex justify-between w-full items-center">
              <span>{{ $t("Starter Suggestions") }}</span>
              <el-button
                class="gemini-button"
                :loading="props.loadingStarters"
                round
                size="small"
                @click="handleGenerateStarters"
              >
                {{ $t("✨ Generate") }}
              </el-button>
            </div>
          </template>
          <div
            v-for="(starter, index) in props.starterMessages"
            :key="index"
            class="flex items-center mb-2 w-full"
          >
            <el-input
              :model-value="starter"
              :placeholder="$t('Example: What is the tuition fee?')"
              class="flex-1 mr-1"
              @update:model-value="val => updateStarter(index, val)"
            />
            <el-button
              type="danger"
              :icon="Delete"
              circle
              plain
              size="small"
              @click="removeStarter(index)"
            />
          </div>
          <el-button :icon="Plus" size="small" @click="addStarter">
            {{ $t("Add Suggestion") }}
          </el-button>
        </el-form-item>
      </div>

      <div>
        <h3 class="text-sm font-medium text-gray-500 mb-2 text-center">
          {{ $t("Preview") }}
        </h3>
        <div class="chat-preview-container">
          <div class="chat-preview-window">
            <div class="chat-preview-header">
              <img
                :src="
                  props.logoUrl ||
                  'https://placehold.co/40x40/E2E8F0/4A5568?text=AI'
                "
                class="chat-preview-avatar"
                alt="logo"
              />
              <span class="font-semibold text-gray-700">
                {{ props.name || $t("AI Assistant") }}
              </span>
            </div>
            <div class="chat-preview-body">
              <div class="chat-message">
                {{ props.greetingMessage || $t("Hello! How can I help you?") }}
              </div>
              <div
                v-if="props.starterMessages?.length"
                class="starter-suggestions"
              >
                <div
                  v-for="(starter, index) in props.starterMessages"
                  :key="index"
                  class="starter-item"
                >
                  {{ starter }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #333;
}

.gemini-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;

  &:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  }
}

.chat-preview-container {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  background: #f9fafb;
}

.chat-preview-window {
  background: white;
  min-height: 300px;
}

.chat-preview-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e5e7eb;
  background: #f8fafc;
}

.chat-preview-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 8px;
  object-fit: cover;
}

.chat-preview-body {
  padding: 16px;
}

.chat-message {
  background: #f3f4f6;
  padding: 12px 16px;
  border-radius: 18px;
  margin-bottom: 12px;
  max-width: 80%;
}

.starter-suggestions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.starter-item {
  background: #eff6ff;
  border: 1px solid #dbeafe;
  padding: 8px 12px;
  border-radius: 12px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: #dbeafe;
  }
}
</style>
