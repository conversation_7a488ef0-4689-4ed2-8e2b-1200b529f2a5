{
  "editor.formatOnType": false,
  "editor.formatOnSave": false,
  "[vue]": {
    "editor.defaultFormatter": "Vue.volar",
    "editor.formatOnSave": false,
    "editor.formatOnType": false,
    "editor.formatOnPaste": false
  },
  "editor.tabSize": 2,
  "editor.formatOnPaste": false,
  "editor.guides.bracketPairs": "active",
  "files.autoSave": "afterDelay",
  "git.confirmSync": false,
  "workbench.startupEditor": "newUntitledFile",
  "editor.suggestSelection": "first",
  "editor.acceptSuggestionOnCommitCharacter": false,
  "css.lint.propertyIgnoredDueToDisplay": "ignore",
  "editor.quickSuggestions": {
    "other": true,
    "comments": true,
    "strings": true
  },
  "files.associations": {
    "editor.snippetSuggestions": "top"
  },
  "[css]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "never"
  },
  "iconify.excludes": [
    "el"
  ],
  "vscodeCustomCodeColor.highlightValue": [
    "v-loading",
    "v-auth",
    "v-copy",
    "v-longpress",
    "v-optimize",
    "v-perms",
    "v-ripple"
  ],
  "vscodeCustomCodeColor.highlightValueColor": "#b392f0",
  "i18n-ally.localesPaths": [
    "locales",
    "src/locales",
    "src/views/language"
  ],
  "i18n-ally.keystyle": "nested",
}