import { http } from "@/utils/http";

export type Result<T = any> = {
  success: boolean;
  message: string;
  data: T;
};

export type Role = {
  id: number;
  name: string;
  display_name: string;
  description?: string;
  permissions?: Permission[];
  created_at?: string;
  updated_at?: string;
};

export type Permission = {
  id: number;
  name: string;
  display_name: string;
  description?: string;
  group?: string;
  created_at?: string;
  updated_at?: string;
};

export type PermissionGroup = {
  group: string;
  permissions: Permission[];
};

export type RoleOption = {
  label: string;
  value: number | string;
  key?: number | string;
};

/**
 * Get all roles as options
 */
export const getRoleOptions = () => {
  return http.request<Result<RoleOption[]>>("get", "/api/v1/roles/options");
};

/**
 * Get permission groups
 */
export const getPermissionGroups = () => {
  return http.request<Result<PermissionGroup[]>>("get", "/api/v1/permissions/groups");
};

/**
 * Get all permissions
 */
export const getPermissions = () => {
  return http.request<Result<Permission[]>>("get", "/api/v1/permissions");
};

/**
 * Get all roles
 */
export const getRoles = () => {
  return http.request<Result<Role[]>>("get", "/api/v1/roles");
};

/**
 * Get role by ID
 */
export const getRoleById = (id: number) => {
  return http.request<Result<Role>>("get", `/api/v1/roles/${id}`);
};

/**
 * Create new role
 */
export const createRole = (data: Partial<Role>) => {
  return http.request<Result<Role>>("post", "/api/v1/roles", { data });
};

/**
 * Update role by ID
 */
export const updateRoleById = (id: number, data: Partial<Role>) => {
  return http.request<Result<Role>>("put", `/api/v1/roles/${id}`, { data });
};

/**
 * Delete role by ID
 */
export const deleteRole = (id: number) => {
  return http.request<Result<null>>("delete", `/api/v1/roles/${id}`);
};
