import { ref, computed, type Ref } from "vue";
import { getOrganizationOverview } from "../../../shared/utils/auth-api";
import type { OrganizationOverviewStats } from "../../../shared/utils/type";
import { useConvertKeyToCamel } from "@/utils/helpers";

export function useOrganizationOverview(organizationId: Ref<string>) {
  const stats = ref<OrganizationOverviewStats | null>(null);
  const loading = ref(false);
  const error = ref<string | null>(null);

  // Mock data for development/testing - Enhanced for AI Bot Management
  const getMockData = (): OrganizationOverviewStats => ({
    bots: {
      total: 15,
      active: 12,
      draft: 3,
      mostUsedBot: "AI Customer Support Assistant"
    },
    conversations: {
      total: 2850,
      today: 78,
      thisWeek: 520,
      thisMonth: 2100,
      avgConversationLength: 12.3
    },
    members: {
      total: 32,
      active: 28,
      pending: 3,
      admins: 4
    },
    storage: {
      totalUsedMB: 3584,
      documentsSizeMB: 2560,
      attachmentsSizeMB: 1024,
      remainingQuotaMB: 1536,
      quotaLimitMB: 5120,
      usagePercent: 70
    },
    tokens: {
      totalUsed: 285000,
      thisMonth: 95000,
      estimatedCost: "$47.25"
    },
    activity: {
      lastActiveAt: new Date(Date.now() - 5 * 60 * 1000).toISOString(), // 5 minutes ago
      peakHours: [9, 10, 11, 14, 15, 16, 20],
      popularBots: [
        { name: "AI Customer Support Assistant", usage: 680 },
        { name: "Smart Sales Bot", usage: 520 },
        { name: "Technical Help AI", usage: 450 },
        { name: "Product Recommendation Bot", usage: 380 },
        { name: "Order Processing Assistant", usage: 320 },
        { name: "FAQ Intelligence Bot", usage: 280 },
        { name: "Billing Support AI", usage: 220 }
      ]
    }
  });

  const fetchOverviewData = async () => {
    if (!organizationId.value) {
      error.value = "Organization ID is required";
      return;
    }

    loading.value = true;
    error.value = null;

    try {
      // Try to fetch real data first
      const response = await getOrganizationOverview(organizationId.value);
      stats.value = useConvertKeyToCamel(
        response.data
      ) as OrganizationOverviewStats;
    } catch (err: any) {
      console.warn("API not available, using mock data:", err);

      // Use mock data for development/testing
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API delay
      stats.value = getMockData();
    } finally {
      loading.value = false;
    }
  };

  const refreshData = async () => {
    await fetchOverviewData();
  };

  // Computed properties for easy access
  const hasData = computed(() => stats.value !== null);
  const isEmpty = computed(() => !hasData.value && !loading.value);

  // Statistics computed properties
  const botStats = computed(() => stats.value?.bots || null);
  const conversationStats = computed(() => stats.value?.conversations || null);
  const memberStats = computed(() => stats.value?.members || null);
  const storageStats = computed(() => stats.value?.storage || null);
  const tokenStats = computed(() => stats.value?.tokens || null);
  const activityStats = computed(() => stats.value?.activity || null);

  // Utility computed properties
  const storageUsageColor = computed(() => {
    if (!storageStats.value) return "success";
    const usage = storageStats.value.usagePercent;
    if (usage > 80) return "danger";
    if (usage > 60) return "warning";
    return "success";
  });

  const isHighActivity = computed(() => {
    if (!conversationStats.value) return false;
    return conversationStats.value.today > 50; // Threshold for high activity
  });

  const totalActiveUsers = computed(() => {
    if (!memberStats.value) return 0;
    return memberStats.value.active;
  });

  const conversationGrowth = computed(() => {
    if (!conversationStats.value) return 0;
    const { today, thisWeek } = conversationStats.value;
    const avgDaily = thisWeek / 7;
    return today > avgDaily ? ((today - avgDaily) / avgDaily) * 100 : 0;
  });

  // AI Bot specific computed properties
  const botActiveRate = computed(() => {
    if (!botStats.value || botStats.value.total === 0) return 0;
    return Math.round((botStats.value.active / botStats.value.total) * 100);
  });

  const averageTokensPerConversation = computed(() => {
    if (
      !tokenStats.value ||
      !conversationStats.value ||
      conversationStats.value.thisMonth === 0
    )
      return 0;
    return Math.round(
      tokenStats.value.thisMonth / conversationStats.value.thisMonth
    );
  });

  const topPerformingBot = computed(() => {
    if (!activityStats.value?.popularBots?.length) return null;
    return activityStats.value.popularBots[0];
  });

  const botUtilizationScore = computed(() => {
    if (!botStats.value || !conversationStats.value) return 0;
    const avgConversationsPerBot =
      conversationStats.value.thisMonth / botStats.value.active;
    // Score based on conversations per active bot (normalized to 0-100)
    return Math.min(Math.round(avgConversationsPerBot / 10), 100);
  });

  const tokenEfficiency = computed(() => {
    if (
      !tokenStats.value ||
      !conversationStats.value ||
      conversationStats.value.avgConversationLength === 0
    )
      return 0;
    const tokensPerMessage =
      averageTokensPerConversation.value /
      conversationStats.value.avgConversationLength;
    // Efficiency score (lower tokens per message = higher efficiency)
    return Math.max(0, 100 - Math.round(tokensPerMessage / 10));
  });

  const peakActivityHour = computed(() => {
    if (!activityStats.value?.peakHours?.length) return null;
    // Find the most frequent hour (assuming peakHours contains multiple entries for busy hours)
    const hourCounts = activityStats.value.peakHours.reduce(
      (acc, hour) => {
        acc[hour] = (acc[hour] || 0) + 1;
        return acc;
      },
      {} as Record<number, number>
    );

    const mostActiveHour = Object.entries(hourCounts).reduce((a, b) =>
      hourCounts[a[0]] > hourCounts[b[0]] ? a : b
    )[0];

    return {
      hour: parseInt(mostActiveHour),
      formatted: `${mostActiveHour.padStart(2, "0")}:00`
    };
  });

  const memberEngagement = computed(() => {
    if (!memberStats.value || memberStats.value.total === 0) return 0;
    return Math.round(
      (memberStats.value.active / memberStats.value.total) * 100
    );
  });

  const storageOptimization = computed(() => {
    if (!storageStats.value) return null;
    const { documentsSizeMB, attachmentsSizeMB, totalUsedMB } =
      storageStats.value;
    return {
      documentsPercentage: Math.round((documentsSizeMB / totalUsedMB) * 100),
      attachmentsPercentage: Math.round(
        (attachmentsSizeMB / totalUsedMB) * 100
      ),
      recommendation:
        storageStats.value.usagePercent > 80
          ? "cleanup"
          : storageStats.value.usagePercent > 60
            ? "monitor"
            : "optimal"
    };
  });

  return {
    // State
    stats,
    loading,
    error,

    // Actions
    fetchOverviewData,
    refreshData,

    // Basic Computed
    hasData,
    isEmpty,
    botStats,
    conversationStats,
    memberStats,
    storageStats,
    tokenStats,
    activityStats,
    storageUsageColor,
    isHighActivity,
    totalActiveUsers,
    conversationGrowth,

    // AI Bot Management Specific Computed
    botActiveRate,
    averageTokensPerConversation,
    topPerformingBot,
    botUtilizationScore,
    tokenEfficiency,
    peakActivityHour,
    memberEngagement,
    storageOptimization
  };
}
