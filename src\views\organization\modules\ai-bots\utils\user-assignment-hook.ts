import { ref, reactive } from "vue";
import { $t } from "@/plugins/i18n";
import { message } from "@/utils/message";
import { useConvertKeyToCamel } from "@/utils/helpers";
import {
  getBotAssignedUsers,
  assignUsersToBot,
  unassignUsersFromBot
} from "./auth-api";

export function useBotUserAssignmentHook(
  organizationUuid: string,
  botId: string
) {
  /* ***************************
   * Data/State Management
   *************************** */

  const loading = ref(false);
  const assignedUsers = ref([]);
  const selectedUsers = ref([]);

  const pagination = reactive({
    page: 1,
    size: 20,
    total: 0
  });

  /* ***************************
   * API Operations
   *************************** */

  const fnGetAssignedUsers = async (params?: any) => {
    try {
      loading.value = true;
      const queryParams = {
        page: pagination.page,
        limit: pagination.size,
        ...params
      };

      const response = await getBotAssignedUsers(
        organizationUuid,
        botId,
        queryParams
      );

      if (response.success) {
        assignedUsers.value = useConvertKeyToCamel(response.data || []);
        pagination.total = response?.total || response?.data?.length || 0;
      } else {
        message(response.message || $t("Failed to load assigned users"), {
          type: "error"
        });
      }
    } catch (error) {
      console.error("Get assigned users error:", error);
      message(
        error.response?.data?.message || $t("Failed to load assigned users"),
        {
          type: "error"
        }
      );
    } finally {
      loading.value = false;
    }
  };

  const fnAssignUsers = async (userIds: string[]) => {
    try {
      loading.value = true;
      const response = await assignUsersToBot(organizationUuid, botId, {
        ids: userIds
      });

      if (response.success) {
        message(response.message || $t("Users assigned successfully"), {
          type: "success"
        });
        await fnGetAssignedUsers();
        return true;
      } else {
        message(response.message || $t("Failed to assign users"), {
          type: "error"
        });
        return false;
      }
    } catch (error) {
      console.error("Assign users error:", error);
      message(error.response?.data?.message || $t("Failed to assign users"), {
        type: "error"
      });
      return false;
    } finally {
      loading.value = false;
    }
  };

  const fnUnassignUsers = async (userIds: string[]) => {
    try {
      loading.value = true;
      const response = await unassignUsersFromBot(organizationUuid, botId, {
        ids: userIds
      });

      if (response.success) {
        message(response.message || $t("Users removed successfully"), {
          type: "success"
        });
        await fnGetAssignedUsers();
        return true;
      } else {
        message(response.message || $t("Failed to remove users"), {
          type: "error"
        });
        return false;
      }
    } catch (error) {
      console.error("Unassign users error:", error);
      message(error.response?.data?.message || $t("Failed to remove users"), {
        type: "error"
      });
      return false;
    } finally {
      loading.value = false;
    }
  };

  /* ***************************
   * Event Handlers
   *************************** */

  const handleSelectionChange = (selection: any[]) => {
    selectedUsers.value = selection;
  };

  const handlePageChange = (page: number) => {
    pagination.page = page;
    fnGetAssignedUsers();
  };

  const handleSizeChange = (size: number) => {
    pagination.size = size;
    pagination.page = 1;
    fnGetAssignedUsers();
  };

  /* ***************************
   * Return Hook Interface
   *************************** */

  return {
    // Data/State
    loading,
    assignedUsers,
    selectedUsers,
    pagination,

    // API Operations
    fnGetAssignedUsers,
    fnAssignUsers,
    fnUnassignUsers,

    // Event Handlers
    handleSelectionChange,
    handlePageChange,
    handleSizeChange
  };
}
