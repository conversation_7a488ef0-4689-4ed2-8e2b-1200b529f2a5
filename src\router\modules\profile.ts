const Layout = () => import("@/layout/index.vue");

export default {
  path: "/auth/profile",
  name: "Profile",
  component: Layout,
  redirect: "/auth/profile/index",
  meta: {
    icon: "ri:user-settings-line",
    title: "Profile",
    rank: 999,
    showLink: false
  },
  children: [
    {
      path: "/auth/profile/index",
      name: "ProfileIndex",
      component: () => import("@/views/auth/profile/index.vue"),
      meta: {
        icon: "ri:user-settings-line",
        title: "My Profile",
        showLink: false,
        // Cho phép tab xuất hiện ngay cả khi showLink: false
        hiddenTag: false,
        // Đánh dấu là route đặc biệt cần tự động tạo tab
        autoCreateTab: true
      }
    }
  ]
} satisfies RouteConfigsTable;
