## Field Meanings

| Field             | Description                                                                                                       |
| :---------------- | :---------------------------------------------------------------------------------------------------------------- |
| `menuType`        | Menu type (`0` for menu, `1` for iframe, `2` for external link, `3` for button)                                    |
| `parentId`        |                                                                                                                    |
| `title`           | Menu name (supports both internationalized and non-internationalized names; if using the internationalized format, it must be added to the `locales` folder in the root directory) |
| `name`            | Route name (must be unique and consistent with the `name` wrapped in `defineOptions` in the corresponding route component) |
| `path`            | Route path                                                                                                        |
| `component`       | Component path (if the `component` path is provided, `path` can be arbitrary; otherwise, the `component` path will match `path`) |
| `rank`            | Menu order (only the `home` route can have a `rank` of `0`; therefore, the backend must start returning `rank` values from non-zero) |
| `redirect`        | Route redirection                                                                                                 |
| `icon`            | Menu icon                                                                                                         |
| `extraIcon`       | Right-side icon                                                                                                   |
| `enterTransition` | Enter animation (page load animation)                                                                             |
| `leaveTransition` | Leave animation (page load animation)                                                                             |
| `activePath`      | Active menu (used to activate a menu, mainly for routes with `query` or `params`; if these routes have `showLink: false` and are not displayed in the menu, no menu will be highlighted, but by setting `activePath` to the path of the menu to be activated, it will be highlighted) |
| `auths`           | Authorization identifier (button-level permissions)                                                               |
| `frameSrc`        | Link address (URL for the embedded `iframe`)                                                                      |
| `frameLoading`    | Loading animation (whether to enable the initial load animation for the embedded `iframe`)                        |
| `keepAlive`       | Cache page (whether to cache the route page; if enabled, the overall state of the page is preserved, but the state will be cleared after refreshing) |
| `hiddenTag`       | Tab (whether to prohibit the current menu name or custom information from being added to the tab)                 |
| `fixedTag`        | Fixed tab (whether to fix the current menu name in the tab and make it unclosable)                                |
| `showLink`        | Menu (whether to display this menu)                                                                               |
| `showParent`      | Parent menu (whether to display the parent menu)                                                                  |

## Recent Updates

### Fixed Issues (2024-07-24)
1. ✅ Missing `TableButtons.vue` and `TableOperations.vue` components
2. ✅ Missing API functions in `auth-api.ts`
3. ✅ Incorrect import paths and function names
4. ✅ Missing TypeScript interfaces
5. ✅ Hook structure not following role pattern
6. ✅ Missing pagination support
7. ✅ Missing bulk operations
8. ✅ Missing restore functionality

### Improvements Made
1. ✅ Restructured hook to follow role hook pattern
2. ✅ Added comprehensive error handling
3. ✅ Added loading states for all operations
4. ✅ Added confirmation dialogs for destructive actions
5. ✅ Added proper TypeScript typing
6. ✅ Added pagination and sorting support
7. ✅ Added bulk operations support
8. ✅ Added tree structure handling for hierarchical menus

### Hook Pattern
The `useSidebarHook()` now follows the same pattern as `useRoleHook()` with these sections:
1. **Data/State Management** - Reactive state variables
2. **API Data Fetching** - Functions to fetch data from backend
3. **Table Event Handlers** - Pagination, sorting, selection handlers
4. **API CRUD Operations** - Create, update, delete operations
5. **Delete Handlers and Actions** - Confirmation dialogs and delete operations
6. **Form Handlers and Actions** - Form submission and validation
7. **Return Hook Interface** - Exposed functions and state

The module is now fully functional and follows the established patterns in the codebase.
