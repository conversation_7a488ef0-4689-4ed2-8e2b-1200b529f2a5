import { reactive, ref, computed } from "vue";
import { useRoute } from "vue-router";
import { $t } from "@/plugins/i18n";
import type { FieldValues } from "plus-pro-components";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";

import { useConvertKeyToCamel, useConvertKeyToSnake } from "@/utils/helpers";
import type { FormItemProps, InvitationFilterProps } from "./type";
import {
  getInvitations,
  sendInvitation,
  resendInvitation,
  cancelInvitation,
  bulkResendInvitations,
  bulkCancelInvitations
} from "./auth-api";

export function useInvitationHook() {
  const route = useRoute();
  const organizationUuid = computed(() => route.params.id as string);

  // State
  const loading = ref(false);
  const records = ref([]);
  const multipleSelection = ref([]);

  // Pagination
  const pagination = reactive({
    total: 0,
    pageSize: 20,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 50, 100]
  });

  // Sorting
  const sort = ref({
    sortBy: "created_at",
    sortOrder: "desc"
  });

  // Filter
  const filterRef = ref<InvitationFilterProps>({});

  // Form
  const filterVisible = ref(false);
  const drawerVisible = ref(false);
  const drawerValues = ref<FormItemProps>({
    status: "pending"
  });
  const invitationFormRef = ref();

  /* ***************************
   * API Data Fetching
   *************************** */

  const fnGetInvitations = async () => {
    try {
      loading.value = true;
      const res = await getInvitations(
        organizationUuid.value,
        useConvertKeyToSnake({
          ...filterRef.value,
          order: `${useConvertKeyToSnake(sort.value.sortBy)}:${sort.value.sortOrder}`,
          page: pagination.currentPage,
          limit: pagination.pageSize
        })
      );
      records.value = useConvertKeyToCamel(res.data);
      pagination.total = res.total;
    } catch (error) {
      console.error("Error fetching invitations:", error);
      message($t("Failed to fetch invitations"), { type: "error" });
    } finally {
      loading.value = false;
    }
  };

  // Table Event Handlers
  const fnHandleSelectionChange = (val: any[]) => {
    multipleSelection.value = val;
  };

  const fnHandlePageChange = (val: number) => {
    pagination.currentPage = val;
    fnGetInvitations();
  };

  const fnHandleSizeChange = (val: number) => {
    pagination.pageSize = val;
    pagination.currentPage = 1;
    fnGetInvitations();
  };

  const fnHandleSortChange = ({ prop, order }: any) => {
    sort.value.sortBy = prop;
    sort.value.sortOrder = order === "ascending" ? "asc" : "desc";
    fnGetInvitations();
  };

  // Form Handlers
  const handleSubmit = async (data: FieldValues) => {
    try {
      loading.value = true;
      const response = await sendInvitation(organizationUuid.value, data);
      if (response.success) {
        message(response.message || $t("Invitation sent successfully"), {
          type: "success"
        });
        drawerVisible.value = false;
        await fnGetInvitations();
      } else {
        message(response.message || $t("Failed to send invitation"), {
          type: "error"
        });
      }
    } catch (error) {
      console.error("Send invitation error:", error);
      message(
        error.response?.data?.message || $t("Failed to send invitation"),
        {
          type: "error"
        }
      );
    } finally {
      loading.value = false;
    }
  };

  const handleFilter = async (data: FieldValues) => {
    filterRef.value = { ...data };
    pagination.currentPage = 1;
    filterVisible.value = false;
    await fnGetInvitations();
  };

  // Individual Actions
  const handleResend = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to resend this invitation?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const response = await resendInvitation(organizationUuid.value, row.id);
      if (response.success) {
        message(response.message || $t("Invitation resent successfully"), {
          type: "success"
        });
        await fnGetInvitations();
      }
    } catch (error) {
      if (error !== "cancel") {
        console.error("Resend invitation error:", error);
        message(
          error.response?.data?.message || $t("Failed to resend invitation"),
          {
            type: "error"
          }
        );
      }
    }
  };

  const handleCancel = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to cancel this invitation?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const response = await cancelInvitation(organizationUuid.value, row.id);
      if (response.success) {
        message(response.message || $t("Invitation cancelled successfully"), {
          type: "success"
        });
        await fnGetInvitations();
      }
    } catch (error) {
      if (error !== "cancel") {
        console.error("Cancel invitation error:", error);
        message(
          error.response?.data?.message || $t("Failed to cancel invitation"),
          {
            type: "error"
          }
        );
      }
    }
  };

  // Bulk Actions
  const handleBulkResend = async () => {
    const ids = multipleSelection.value.map(item => item.id);
    if (ids.length === 0) {
      message($t("Please select items to resend"), {
        type: "warning"
      });
      return;
    }
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to resend selected invitations?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const response = await bulkResendInvitations(organizationUuid.value, {
        ids
      });
      if (response.success) {
        message(response.message || $t("Invitations resent successfully"), {
          type: "success"
        });
        await fnGetInvitations();
      }
    } catch (error) {
      if (error !== "cancel") {
        console.error("Bulk resend error:", error);
        message(
          error.response?.data?.message || $t("Failed to resend invitations"),
          {
            type: "error"
          }
        );
      }
    }
  };

  const handleBulkCancel = async () => {
    const ids = multipleSelection.value.map(item => item.id);
    if (ids.length === 0) {
      message($t("Please select items to cancel"), {
        type: "warning"
      });
      return;
    }
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to cancel selected invitations?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const response = await bulkCancelInvitations(organizationUuid.value, {
        ids
      });
      if (response.success) {
        message(response.message || $t("Invitations cancelled successfully"), {
          type: "success"
        });
        await fnGetInvitations();
      }
    } catch (error) {
      if (error !== "cancel") {
        console.error("Bulk cancel error:", error);
        message(
          error.response?.data?.message || $t("Failed to cancel invitations"),
          {
            type: "error"
          }
        );
      }
    }
  };

  return {
    // Data/State
    loading,
    filterRef,
    pagination,
    records,
    multipleSelection,
    // Event handlers
    fnGetInvitations,
    fnHandlePageChange,
    fnHandleSelectionChange,
    fnHandleSortChange,
    fnHandleSizeChange,
    // Form related
    filterVisible,
    drawerVisible,
    drawerValues,
    invitationFormRef,
    handleSubmit,
    handleFilter,
    handleBulkResend,
    handleBulkCancel,
    handleResend,
    handleCancel
  };
}
