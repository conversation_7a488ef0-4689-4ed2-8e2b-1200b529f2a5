export interface GeneralSettings {
  name: string;
  description?: string;
  website?: string;
  email?: string;
  phone?: string;
  address?: string;
  timezone: string;
  language: string;
  currency: string;
  organizationType: string;
  industry?: string;
  employeeCount?: number;
  foundedYear?: number;
  logoUrl?: string;
}

export interface SecuritySettings {
  ssoEnabled: boolean;
  ssoProvider?: string;
  twoFactorRequired: boolean;
  passwordPolicy: {
    minLength: number;
    requireUppercase: boolean;
    requireLowercase: boolean;
    requireNumbers: boolean;
    requireSymbols: boolean;
    expiryDays: number;
  };
  ipWhitelist: string[];
  sessionTimeout: number; // minutes
  maxConcurrentSessions: number;
  apiKeysEnabled: boolean;
  webhooksEnabled: boolean;
}

export interface MemberSettings {
  defaultRole: string;
  invitationPolicy: "open" | "admin_only" | "owner_only";
  registrationEnabled: boolean;
  emailVerificationRequired: boolean;
  maxMembers?: number;
  allowGuestAccess: boolean;
  guestPermissions: string[];
  memberNotifications: {
    newMember: boolean;
    memberLeft: boolean;
    roleChanged: boolean;
    invitationSent: boolean;
  };
}

export interface BillingSettings {
  plan: {
    name: string;
    type: "free" | "pro" | "enterprise";
    price: number;
    currency: string;
    billingCycle: "monthly" | "yearly";
    features: string[];
  };
  usage: {
    bots: { current: number; limit: number };
    conversations: { current: number; limit: number };
    storage: { current: number; limit: number; unit: string };
    apiCalls: { current: number; limit: number };
  };
  paymentMethod?: {
    type: "card" | "bank" | "paypal";
    last4?: string;
    expiryDate?: string;
    brand?: string;
  };
  billingAddress?: {
    company?: string;
    address: string;
    city: string;
    state?: string;
    postalCode: string;
    country: string;
    taxId?: string;
  };
  invoices: Array<{
    id: string;
    date: string;
    amount: number;
    status: "paid" | "pending" | "failed";
    downloadUrl?: string;
  }>;
  alerts: {
    usageThreshold: number; // percentage
    emailNotifications: boolean;
    slackNotifications: boolean;
  };
}

export interface NotificationSettings {
  email: {
    enabled: boolean;
    frequency: "immediate" | "daily" | "weekly";
    types: {
      botActivity: boolean;
      memberActivity: boolean;
      systemUpdates: boolean;
      billing: boolean;
      security: boolean;
    };
  };
  slack: {
    enabled: boolean;
    webhookUrl?: string;
    channel?: string;
    types: {
      botActivity: boolean;
      memberActivity: boolean;
      systemAlerts: boolean;
    };
  };
  inApp: {
    enabled: boolean;
    types: {
      botActivity: boolean;
      memberActivity: boolean;
      systemUpdates: boolean;
    };
  };
}

export interface ApiSettings {
  enabled: boolean;
  rateLimit: {
    requestsPerMinute: number;
    requestsPerHour: number;
    requestsPerDay: number;
  };
  allowedOrigins: string[];
  apiKeys: Array<{
    id: string;
    name: string;
    keyPreview: string;
    permissions: string[];
    lastUsed?: string;
    createdAt: string;
  }>;
  webhooks: Array<{
    id: string;
    name: string;
    url: string;
    events: string[];
    secret: string;
    isActive: boolean;
    lastTriggered?: string;
  }>;
}

export interface OrganizationSettings {
  general: GeneralSettings;
  security: SecuritySettings;
  members: MemberSettings;
  billing: BillingSettings;
  notifications: NotificationSettings;
  api: ApiSettings;
}

export interface SettingsFormProps {
  loading?: boolean;
  settings: Partial<OrganizationSettings>;
  onSave: (section: string, data: any) => Promise<void>;
  onReset?: () => Promise<void>;
}

export interface SettingsMenuItem {
  key: string;
  title: string;
  icon: string;
  description: string;
  component?: any;
}
