<template>
  <div class="bot-user-assignment">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
          {{ $t("User Assignment") }}
        </h3>
        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
          {{ $t("Manage users who can access this private bot") }}
        </p>
      </div>
      <el-button
        type="primary"
        :icon="Plus"
        :loading="loading"
        @click="handleAssignUsers"
      >
        {{ $t("Assign Users") }}
      </el-button>
    </div>

    <!-- Search and Filters -->
    <div class="mb-4">
      <el-input
        v-model="searchQuery"
        :placeholder="$t('Search users...')"
        :prefix-icon="Search"
        clearable
        class="w-80"
        @input="handleSearch"
      />
    </div>

    <!-- Assigned Users Table -->
    <el-table
      ref="tableRef"
      v-loading="loading"
      :data="assignedUsers"
      stripe
      class="w-full"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />

      <el-table-column :label="$t('User')" min-width="200">
        <template #default="{ row }">
          <div class="flex items-center space-x-3">
            <el-avatar
              :size="32"
              :src="row.avatar"
              :alt="row.firstName + ' ' + row.lastName"
            >
              {{ (row.firstName?.[0] || "") + (row.lastName?.[0] || "") }}
            </el-avatar>
            <div>
              <div class="font-medium text-gray-900 dark:text-gray-100">
                {{ row.firstName }} {{ row.lastName }}
              </div>
              <div class="text-sm text-gray-500 dark:text-gray-400">
                {{ row.email }}
              </div>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column :label="$t('Actions')" width="120" fixed="right">
        <template #default="{ row }">
          <el-button
            type="danger"
            size="small"
            text
            :loading="loading"
            @click="handleUnassignUser(row)"
          >
            {{ $t("Remove") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- Bulk Actions -->
    <div
      v-if="selectedUsers.length > 0"
      class="mt-4 flex items-center space-x-4"
    >
      <span class="text-sm text-gray-600 dark:text-gray-300">
        {{ $t("Selected") }}: {{ selectedUsers.length }}
      </span>
      <el-button
        type="danger"
        size="small"
        :loading="loading"
        @click="handleBulkUnassign"
      >
        {{ $t("Remove Selected") }}
      </el-button>
    </div>

    <!-- Pagination -->
    <div class="mt-6 flex justify-center">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>

    <!-- User Assignment Dialog -->
    <UserAssignmentDialog
      v-model:visible="assignDialogVisible"
      :bot-id="botId"
      :organization-uuid="organizationUuid"
      :assigned-user-ids="assignedUserIds"
      @assigned="handleUsersAssigned"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, defineAsyncComponent } from "vue";
import { useRoute } from "vue-router";
import { ElMessageBox } from "element-plus";
import { Plus, Search } from "@element-plus/icons-vue";
import { $t } from "@/plugins/i18n";
import { message } from "@/utils/message";
import { formatDate } from "@/utils/dateUtil";
import { useBotUserAssignmentHook } from "../utils/user-assignment-hook";

// Lazy load dialog component
const UserAssignmentDialog = defineAsyncComponent(
  () => import("./UserAssignmentDialog.vue")
);

interface Props {
  botId: string;
  organizationUuid: string;
}

const props = defineProps<Props>();

const route = useRoute();
const tableRef = ref();
const assignDialogVisible = ref(false);
const searchQuery = ref("");

// Use hook for data management
const {
  loading,
  assignedUsers,
  selectedUsers,
  pagination,
  fnGetAssignedUsers,
  fnUnassignUsers,
  handleSelectionChange,
  handlePageChange,
  handleSizeChange
} = useBotUserAssignmentHook(props.organizationUuid, props.botId);

// Computed properties
const assignedUserIds = computed(() =>
  assignedUsers.value.map(user => user.id || user.uuid)
);

// Methods
const handleSearch = () => {
  // Implement search logic
  fnGetAssignedUsers({ search: searchQuery.value });
};

const handleAssignUsers = () => {
  assignDialogVisible.value = true;
};

const handleUnassignUser = async (user: any) => {
  try {
    await ElMessageBox.confirm(
      $t("Are you sure to remove this user from bot access?"),
      $t("Warning"),
      {
        confirmButtonText: $t("OK"),
        cancelButtonText: $t("Cancel"),
        type: "warning"
      }
    );
    await fnUnassignUsers([user.id || user.uuid]);
  } catch {
    console.log("Unassign cancelled");
  }
};

const handleBulkUnassign = async () => {
  try {
    await ElMessageBox.confirm(
      $t("Are you sure to remove selected users from bot access?"),
      $t("Warning"),
      {
        confirmButtonText: $t("OK"),
        cancelButtonText: $t("Cancel"),
        type: "warning"
      }
    );
    const userIds = selectedUsers.value.map(user => user.id || user.uuid);
    await fnUnassignUsers(userIds);
    tableRef.value?.clearSelection();
  } catch {
    console.log("Bulk unassign cancelled");
  }
};

const handleUsersAssigned = () => {
  assignDialogVisible.value = false;
  fnGetAssignedUsers();
};

const getRoleTagType = (role: string) => {
  const roleTypes = {
    owner: "danger",
    admin: "warning",
    member: "info",
    guest: ""
  };
  return roleTypes[role?.toLowerCase()] || "";
};

// Lifecycle
onMounted(() => {
  fnGetAssignedUsers();
});
</script>

<style scoped>
.bot-user-assignment {
  padding: 1.5rem;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow:
    0 1px 3px 0 rgba(0, 0, 0, 0.1),
    0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.dark .bot-user-assignment {
  background-color: #1f2937;
}
</style>
