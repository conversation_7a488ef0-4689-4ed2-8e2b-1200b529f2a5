<script setup lang="ts">
import { computed } from "vue";
import { $t } from "@/plugins/i18n";
import { IconifyIconOnline } from "@/components/ReIcon";
import {
  ElCard,
  ElRow,
  ElCol,
  ElSkeleton,
  ElTag,
  ElProgress,
  ElEmpty
} from "element-plus";
import type { OrganizationOverviewStats } from "../../../shared/utils/type";

interface Props {
  stats: OrganizationOverviewStats | null;
  loading: boolean;
}

const props = defineProps<Props>();

// Computed properties
const popularBots = computed(() => {
  if (!props.stats?.activity?.popularBots) return [];
  return props.stats.activity.popularBots.slice(0, 5); // Top 5 bots
});

const peakHours = computed(() => {
  if (!props.stats?.activity?.peakHours) return [];
  return props.stats.activity.peakHours.map(hour => {
    const time = hour.toString().padStart(2, "0") + ":00";
    return {
      hour,
      time,
      label: `${time} - ${(hour + 1).toString().padStart(2, "0")}:00`
    };
  });
});

const lastActiveAt = computed(() => {
  if (!props.stats?.activity?.lastActiveAt) return null;
  const date = new Date(props.stats.activity.lastActiveAt);
  return date.toLocaleString();
});

// Utility functions
const formatUsage = (usage: number) => {
  if (usage >= 1000) return (usage / 1000).toFixed(1) + "K";
  return usage.toString();
};

const getUsagePercentage = (usage: number, maxUsage: number) => {
  return Math.round((usage / maxUsage) * 100);
};

const maxBotUsage = computed(() => {
  if (!popularBots.value.length) return 0;
  return Math.max(...popularBots.value.map(bot => bot.usage));
});

const getBotIcon = (botName: string) => {
  const iconMap: Record<string, string> = {
    "Customer Support Bot": "ri:customer-service-line",
    "Sales Assistant": "ri:money-dollar-circle-line",
    "FAQ Bot": "ri:question-answer-line",
    "Technical Support": "ri:tools-line",
    "Order Tracker": "ri:truck-line"
  };
  return iconMap[botName] || "ri:robot-line";
};

const getBotColor = (index: number) => {
  const colors = ["blue", "green", "purple", "orange", "red"];
  return colors[index % colors.length];
};

const getColorClasses = (color: string) => {
  const colorMap = {
    blue: "text-blue-600 bg-blue-50",
    green: "text-green-600 bg-green-50",
    purple: "text-purple-600 bg-purple-50",
    orange: "text-orange-600 bg-orange-50",
    red: "text-red-600 bg-red-50"
  };
  return colorMap[color] || colorMap.blue;
};
</script>

<template>
  <div class="activity-section">
    <h2 class="text-xl font-semibold text-gray-900 mb-6">
      AI Bot Activity & Performance
    </h2>

    <el-row :gutter="24" class="mb-6">
      <!-- Popular Bots -->
      <el-col :xs="24" :lg="12">
        <el-card shadow="hover" class="activity-card">
          <template #header>
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <IconifyIconOnline
                  icon="ri:trophy-line"
                  class="text-xl text-yellow-500 mr-2"
                />
                <span class="font-medium">Top Performing Bots</span>
              </div>
              <el-tag size="small" type="warning" effect="light">Top 5</el-tag>
            </div>
          </template>

          <div v-if="loading" class="space-y-4">
            <el-skeleton v-for="i in 5" :key="i" animated>
              <template #template>
                <div class="flex items-center space-x-3">
                  <div class="w-8 h-8 bg-gray-200 rounded-full" />
                  <div class="flex-1">
                    <div class="h-4 bg-gray-200 rounded w-3/4 mb-2" />
                    <div class="h-3 bg-gray-200 rounded w-1/2" />
                  </div>
                </div>
              </template>
            </el-skeleton>
          </div>

          <div v-else-if="popularBots.length === 0">
            <el-empty
              :description="$t('No bot activity data available')"
              :image-size="80"
            />
          </div>

          <div v-else class="space-y-4">
            <div
              v-for="(bot, index) in popularBots"
              :key="bot.name"
              class="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div class="flex items-center space-x-3">
                <div
                  :class="`w-8 h-8 rounded-full flex items-center justify-center ${getColorClasses(getBotColor(index))}`"
                >
                  <IconifyIconOnline
                    :icon="getBotIcon(bot.name)"
                    class="text-sm"
                  />
                </div>
                <div>
                  <div class="font-medium text-gray-900">{{ bot.name }}</div>
                  <div class="text-sm text-gray-500">
                    {{ formatUsage(bot.usage) }} {{ $t("conversations") }}
                  </div>
                </div>
              </div>
              <div class="text-right min-w-0 flex-1 ml-4">
                <div class="text-sm font-medium text-gray-900 mb-1">
                  {{ getUsagePercentage(bot.usage, maxBotUsage) }}%
                </div>
                <el-progress
                  :percentage="getUsagePercentage(bot.usage, maxBotUsage)"
                  :stroke-width="4"
                  :show-text="false"
                  :color="
                    getBotColor(index) === 'blue'
                      ? '#3b82f6'
                      : getBotColor(index) === 'green'
                        ? '#10b981'
                        : '#8b5cf6'
                  "
                />
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- Peak Hours & Activity Info -->
      <el-col :xs="24" :lg="12">
        <el-card shadow="hover" class="activity-card">
          <template #header>
            <div class="flex items-center">
              <IconifyIconOnline
                icon="ri:time-line"
                class="text-xl text-blue-500 mr-2"
              />
              <span class="font-medium">{{ $t("Peak Activity Hours") }}</span>
            </div>
          </template>

          <div v-if="loading">
            <el-skeleton animated>
              <template #template>
                <div class="space-y-4">
                  <div class="h-4 bg-gray-200 rounded w-full" />
                  <div class="grid grid-cols-3 gap-2">
                    <div
                      v-for="i in 6"
                      :key="i"
                      class="h-8 bg-gray-200 rounded"
                    />
                  </div>
                  <div class="h-4 bg-gray-200 rounded w-3/4" />
                </div>
              </template>
            </el-skeleton>
          </div>

          <div v-else class="space-y-6">
            <!-- Peak Hours -->
            <div v-if="peakHours.length > 0">
              <h4 class="text-sm font-medium text-gray-700 mb-3">
                {{ $t("Most Active Hours") }}
              </h4>
              <div class="grid grid-cols-2 gap-2">
                <el-tag
                  v-for="peak in peakHours"
                  :key="peak.hour"
                  type="primary"
                  effect="light"
                  size="small"
                >
                  {{ peak.label }}
                </el-tag>
              </div>
            </div>

            <!-- Last Activity -->
            <div v-if="lastActiveAt">
              <h4 class="text-sm font-medium text-gray-700 mb-2">
                {{ $t("Last Activity") }}
              </h4>
              <div class="flex items-center text-sm text-gray-600">
                <IconifyIconOnline
                  icon="ri:time-line"
                  class="text-green-500 mr-2"
                />
                {{ lastActiveAt }}
              </div>
            </div>

            <!-- Activity Summary -->
            <div v-if="stats">
              <h4 class="text-sm font-medium text-gray-700 mb-3">
                Activity Summary
              </h4>
              <div class="grid grid-cols-2 gap-4 text-sm">
                <div class="text-center p-3 bg-blue-50 rounded-lg">
                  <div class="text-2xl font-bold text-blue-600">
                    {{ stats.conversations?.total || 0 }}
                  </div>
                  <div class="text-gray-600">Total Conversations</div>
                </div>
                <div class="text-center p-3 bg-green-50 rounded-lg">
                  <div class="text-2xl font-bold text-green-600">
                    {{
                      stats.conversations?.avgConversationLength?.toFixed(1) ||
                      0
                    }}
                  </div>
                  <div class="text-gray-600">Avg Length</div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- Bot Performance Metrics -->
    <el-row :gutter="24">
      <el-col :xs="24">
        <el-card shadow="hover" class="activity-card">
          <template #header>
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <IconifyIconOnline
                  icon="ri:dashboard-line"
                  class="text-xl text-purple-500 mr-2"
                />
                <span class="font-medium">Bot Performance Dashboard</span>
              </div>
              <div class="flex items-center space-x-2">
                <el-tag size="small" type="success" effect="light">
                  {{ stats?.bots?.active || 0 }} Active
                </el-tag>
                <el-tag size="small" type="info" effect="light">
                  {{ stats?.bots?.total || 0 }} Total
                </el-tag>
              </div>
            </div>
          </template>

          <div v-if="loading">
            <el-skeleton animated>
              <template #template>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div
                    v-for="i in 4"
                    :key="i"
                    class="h-24 bg-gray-200 rounded-lg"
                  />
                </div>
              </template>
            </el-skeleton>
          </div>

          <div
            v-else
            class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
          >
            <!-- Bot Status -->
            <div class="performance-metric">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-gray-700">
                  Bot Status
                </span>
                <IconifyIconOnline
                  icon="ri:robot-2-line"
                  class="text-blue-500"
                />
              </div>
              <div class="space-y-2">
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">Active</span>
                  <span class="font-medium text-green-600">{{
                    stats?.bots?.active || 0
                  }}</span>
                </div>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">Draft</span>
                  <span class="font-medium text-orange-600">{{
                    stats?.bots?.draft || 0
                  }}</span>
                </div>
                <el-progress
                  :percentage="
                    stats?.bots?.total
                      ? Math.round((stats.bots.active / stats.bots.total) * 100)
                      : 0
                  "
                  :stroke-width="6"
                  :show-text="false"
                  color="#10b981"
                />
              </div>
            </div>

            <!-- Conversation Metrics -->
            <div class="performance-metric">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-gray-700"
                  >Conversations</span
                >
                <IconifyIconOnline
                  icon="ri:chat-3-line"
                  class="text-green-500"
                />
              </div>
              <div class="space-y-2">
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">Today</span>
                  <span class="font-medium text-blue-600">{{
                    stats?.conversations?.today || 0
                  }}</span>
                </div>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">This Week</span>
                  <span class="font-medium text-purple-600">{{
                    stats?.conversations?.thisWeek || 0
                  }}</span>
                </div>
                <div class="text-xs text-gray-500">
                  Avg:
                  {{
                    stats?.conversations?.avgConversationLength?.toFixed(1) || 0
                  }}
                  messages
                </div>
              </div>
            </div>

            <!-- Token Usage -->
            <div class="performance-metric">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-gray-700">
                  {{ $t("Token Usage") }}
                </span>
                <IconifyIconOnline
                  icon="ri:coin-line"
                  class="text-yellow-500"
                />
              </div>
              <div class="space-y-2">
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">This Month</span>
                  <span class="font-medium text-orange-600">{{
                    formatUsage(stats?.tokens?.thisMonth || 0)
                  }}</span>
                </div>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">Cost</span>
                  <span class="font-medium text-red-600">{{
                    stats?.tokens?.estimatedCost || "$0"
                  }}</span>
                </div>
                <div class="text-xs text-gray-500">
                  Total: {{ formatUsage(stats?.tokens?.totalUsed || 0) }} tokens
                </div>
              </div>
            </div>

            <!-- Storage Usage -->
            <div class="performance-metric">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-gray-700">Storage</span>
                <IconifyIconOnline
                  icon="ri:database-2-line"
                  class="text-indigo-500"
                />
              </div>
              <div class="space-y-2">
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">Used</span>
                  <span class="font-medium text-blue-600"
                    >{{ formatUsage(stats?.storage?.totalUsedMB || 0) }}MB</span
                  >
                </div>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">Limit</span>
                  <span class="font-medium text-gray-600"
                    >{{
                      formatUsage(stats?.storage?.quotaLimitMB || 0)
                    }}MB</span
                  >
                </div>
                <el-progress
                  :percentage="stats?.storage?.usagePercent || 0"
                  :stroke-width="6"
                  :show-text="false"
                  :color="
                    stats?.storage?.usagePercent > 80
                      ? '#ef4444'
                      : stats?.storage?.usagePercent > 60
                        ? '#f59e0b'
                        : '#10b981'
                  "
                />
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<style scoped>
.activity-section {
  padding: 0;
}

.activity-card {
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
  height: 100%;
}

.activity-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border-color: #d1d5db;
}

.performance-metric {
  padding: 1rem;
  background: #f9fafb;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.performance-metric:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.gap-2 {
  gap: 0.5rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-6 {
  gap: 1.5rem;
}

/* Responsive grid adjustments */
@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .activity-section h2 {
    font-size: 1.25rem;
    margin-bottom: 1rem;
  }

  .performance-metric {
    padding: 0.75rem;
  }

  .grid-cols-2 {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
}

/* Card header styling */
:deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
}

:deep(.el-card__body) {
  padding: 20px;
}
</style>
