import { ElMessageBox } from "element-plus";
import { $t } from "@/plugins/i18n";

/**
 * Show popup blocker instructions to user
 */
export const showPopupInstructions = async (): Promise<void> => {
  const browserName = getBrowserName();
  const instructions = getPopupInstructions(browserName);

  try {
    await ElMessageBox.alert(instructions, $t("Enable Popups"), {
      confirmButtonText: $t("Got it"),
      type: "info",
      dangerouslyUseHTMLString: true,
      customClass: "popup-instructions-dialog"
    });
  } catch (e) {
    // User closed dialog
  }
};

/**
 * Get browser name for specific instructions
 */
function getBrowserName(): string {
  const userAgent = navigator.userAgent.toLowerCase();
  
  if (userAgent.includes('chrome') && !userAgent.includes('edg')) {
    return 'chrome';
  } else if (userAgent.includes('firefox')) {
    return 'firefox';
  } else if (userAgent.includes('safari') && !userAgent.includes('chrome')) {
    return 'safari';
  } else if (userAgent.includes('edg')) {
    return 'edge';
  } else {
    return 'generic';
  }
}

/**
 * Get popup instructions for specific browser
 */
function getPopupInstructions(browser: string): string {
  const baseInstructions = `
    <div style="text-align: left; line-height: 1.6;">
      <p><strong>${$t("Social login requires popups to work properly.")}</strong></p>
      <p>${$t("Please follow these steps to enable popups:")}</p>
  `;

  const browserInstructions = {
    chrome: `
      <ol>
        <li>${$t("Click the popup blocked icon")} <strong>🚫</strong> ${$t("in the address bar")}</li>
        <li>${$t("Select 'Always allow popups from this site'")}</li>
        <li>${$t("Click 'Done' and try social login again")}</li>
      </ol>
      <p><em>${$t("Alternative: Go to Settings > Privacy and security > Site Settings > Pop-ups and redirects")}</em></p>
    `,
    firefox: `
      <ol>
        <li>${$t("Click the shield icon in the address bar")}</li>
        <li>${$t("Click 'Turn off Blocking for This Site'")}</li>
        <li>${$t("Refresh the page and try social login again")}</li>
      </ol>
      <p><em>${$t("Alternative: Go to Preferences > Privacy & Security > Permissions > Block pop-up windows")}</em></p>
    `,
    safari: `
      <ol>
        <li>${$t("Go to Safari menu > Preferences")}</li>
        <li>${$t("Click the 'Websites' tab")}</li>
        <li>${$t("Select 'Pop-up Windows' from the left sidebar")}</li>
        <li>${$t("Set this website to 'Allow'")}</li>
      </ol>
    `,
    edge: `
      <ol>
        <li>${$t("Click the popup blocked icon in the address bar")}</li>
        <li>${$t("Select 'Always allow'")}</li>
        <li>${$t("Try social login again")}</li>
      </ol>
      <p><em>${$t("Alternative: Go to Settings > Cookies and site permissions > Pop-ups and redirects")}</em></p>
    `,
    generic: `
      <ol>
        <li>${$t("Look for a popup blocked notification in your browser")}</li>
        <li>${$t("Click on it and allow popups for this site")}</li>
        <li>${$t("Refresh the page and try again")}</li>
      </ol>
    `
  };

  return baseInstructions + (browserInstructions[browser] || browserInstructions.generic) + `
    </div>
  `;
}

/**
 * Check if user has been shown popup instructions before
 */
export const hasShownPopupInstructions = (): boolean => {
  return localStorage.getItem('popup-instructions-shown') === 'true';
};

/**
 * Mark that user has been shown popup instructions
 */
export const markPopupInstructionsShown = (): void => {
  localStorage.setItem('popup-instructions-shown', 'true');
};

/**
 * Reset popup instructions flag (for testing)
 */
export const resetPopupInstructionsFlag = (): void => {
  localStorage.removeItem('popup-instructions-shown');
};
