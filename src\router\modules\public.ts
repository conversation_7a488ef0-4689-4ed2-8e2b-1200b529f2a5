const Layout = () => import("@/layout/index.vue");

export default {
  path: "/public",
  name: "Public",
  component: Layout,
  redirect: "/",
  meta: {
    title: "Public Pages",
    rank: 999,
    showLink: false,
    hiddenTag: true
  },
  children: [
    {
      path: "/invitations/accept/:token",
      name: "InvitationAccept",
      component: () =>
        import(
          "@/views/organization/modules/invitations/components/InvitationAcceptPage.vue"
        ),
      meta: {
        title: "Accept Invitation",
        showLink: false,
        hiddenTag: true,
        // This is a public page, no authentication required
        requiresAuth: false
      }
    }
  ]
} satisfies RouteConfigsTable;
