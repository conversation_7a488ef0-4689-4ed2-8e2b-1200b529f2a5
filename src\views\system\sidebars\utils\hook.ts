import { reactive, ref } from "vue";
import { $t } from "@/plugins/i18n";
import type { FieldValues } from "plus-pro-components";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";

import { useConvertKeyToCamel, useConvertKeyToSnake } from "@/utils/helpers";
import { handleTree } from "@/utils/tree";
import { cloneDeep } from "@pureadmin/utils";
import type { FilterProps, FormItemProps } from "./type";
import {
  getSidebarList,
  createSidebar,
  updateSidebarById,
  deleteSidebar,
  bulkDeleteSidebars,
  restoreSidebar,
  bulkRestoreSidebars,
  forceDeleteSidebar,
  bulkForceDeleteSidebars
} from "./auth-api";

export function useSidebarHook() {
  /* ***************************
   * Data/State Management
   *************************** */

  const loading = ref(false);
  const filterRef = ref<FilterProps>({ isTrashed: "no" });
  const pagination = reactive({
    total: 0,
    pageSize: 20,
    currentPage: 1,
    pageSizes: [10, 20, 50, 100, 200],
    background: true,
    layout: "total, sizes, prev, pager, next, jumper"
  });
  const multipleSelection = ref([]);
  const records = ref([]);
  const sort = ref({ sortBy: "rank", sortOrder: "asc" });

  // Form refs
  const sidebarFormRef = ref();
  const filterVisible = ref(false);
  const drawerVisible = ref(false);
  const drawerValues = ref<FormItemProps>({
    menuType: 0
  });

  /* ***************************
   * API Data Fetching
   *************************** */

  const fnGetSidebars = async () => {
    try {
      loading.value = true;
      const res = await getSidebarList(
        useConvertKeyToSnake({
          ...filterRef.value,
          order: `${sort.value.sortBy}:${sort.value.sortOrder}`,
          page: pagination.currentPage,
          limit: pagination.pageSize
        })
      );
      let newData: any[] = useConvertKeyToCamel(res.data);

      if (filterRef.value.title) {
        newData = newData.filter((item: any) =>
          $t(item.title)
            .toLowerCase()
            .includes(filterRef.value.title.toLowerCase())
        );
      }

      records.value = handleTree(newData);
      pagination.total = res.total || 0;
    } catch (error) {
      console.error("Error fetching sidebars:", error);
      message($t("Failed to fetch data"), { type: "error" });
    } finally {
      loading.value = false;
    }
  };

  /* ***************************
   * Table Event Handlers
   *************************** */

  const fnHandleSelectionChange = (val: any[]) => {
    multipleSelection.value = val;
  };

  const fnHandlePageChange = () => {
    fnGetSidebars();
  };

  const fnHandleSizeChange = (val: number) => {
    pagination.pageSize = val;
    pagination.currentPage = 1;
    fnGetSidebars();
  };

  const fnHandleSortChange = async (val: Record<string, any>) => {
    sort.value = {
      sortBy: val.prop,
      sortOrder: val.order == "ascending" ? "asc" : "desc"
    };
    await fnGetSidebars();
  };

  /* ***************************
   * API CRUD Operations
   *************************** */

  const fnHandleCreateSidebar = async (data: any) => {
    try {
      loading.value = true;
      const response = await createSidebar(data);
      if (response.success) {
        message(response.message || $t("Create successful"), {
          type: "success"
        });
        await fnGetSidebars();
        drawerVisible.value = false;
        return true;
      }
      message(response.message || $t("Create failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Create failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  const fnHandleUpdateSidebar = async (id: number, data: any) => {
    try {
      loading.value = true;
      const response = await updateSidebarById(id, data);
      if (response.success) {
        message(response.message || $t("Update successful"), {
          type: "success"
        });
        await fnGetSidebars();
        return true;
      }
      message(response.message || $t("Update failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Update failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  /*
   ***************************
   *   Delete handlers and actions
   ***************************
   */

  const handleDelete = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to delete this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      await deleteSidebar(row.id);
      message($t("Deleted successfully"), { type: "success" });
      fnGetSidebars();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error deleting sidebar:", error);
        message($t("Delete failed"), { type: "error" });
      }
    }
  };

  const handleBulkDelete = async (ids?: number[]) => {
    const selectedIds = ids || multipleSelection.value.map(item => item.id);
    if (selectedIds.length === 0) {
      message($t("Please select items to delete"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to delete selected items?"),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      await bulkDeleteSidebars(selectedIds);
      message($t("Deleted successfully"), { type: "success" });
      fnGetSidebars();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error bulk deleting sidebars:", error);
        message($t("Delete failed"), { type: "error" });
      }
    }
  };

  const handlePermanentDelete = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t(
          "Are you sure you want to permanently delete this item? This action cannot be undone."
        ),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      await forceDeleteSidebar(row.id);
      message($t("Permanently deleted successfully"), { type: "success" });
      fnGetSidebars();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error permanently deleting sidebar:", error);
        message($t("Permanent delete failed"), { type: "error" });
      }
    }
  };

  const handleBulkPermanentDelete = async (ids?: number[]) => {
    const selectedIds = ids || multipleSelection.value.map(item => item.id);
    if (selectedIds.length === 0) {
      message($t("Please select items to delete"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t(
          "Are you sure you want to permanently delete selected items? This action cannot be undone."
        ),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      await bulkForceDeleteSidebars(selectedIds);
      message($t("Permanently deleted successfully"), { type: "success" });
      fnGetSidebars();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error bulk permanently deleting sidebars:", error);
        message($t("Permanent delete failed"), { type: "error" });
      }
    }
  };

  const handleRestore = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to restore this item?"),
        $t("Confirm"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "info"
        }
      );

      await restoreSidebar(row.id);
      message($t("Restored successfully"), { type: "success" });
      fnGetSidebars();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error restoring sidebar:", error);
        message($t("Restore failed"), { type: "error" });
      }
    }
  };

  const handleBulkRestore = async (ids?: number[]) => {
    const selectedIds = ids || multipleSelection.value.map(item => item.id);
    if (selectedIds.length === 0) {
      message($t("Please select items to restore"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to restore selected items?"),
        $t("Confirm"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "info"
        }
      );

      await bulkRestoreSidebars(selectedIds);
      message($t("Restored successfully"), { type: "success" });
      await fnGetSidebars();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error bulk restoring sidebars:", error);
        message($t("Restore failed"), { type: "error" });
      }
    }
  };

  /*
   ***************************
   *   Form handlers and actions
   ***************************
   */

  const handleEdit = (row: any) => {
    drawerValues.value = { ...row };
    drawerVisible.value = true;
  };

  const handleFilter = async (values: FilterProps) => {
    filterRef.value = values;
    await fnGetSidebars();
  };

  const handleSubmit = async (values: FieldValues) => {
    const cleanValues = cloneDeep(values);
    delete cleanValues.higherMenuOptions;
    delete cleanValues.roleList;
    delete cleanValues.permissionList;

    let success = false;
    if (cleanValues.id != null) {
      success = await fnHandleUpdateSidebar(
        Number(cleanValues.id),
        cleanValues
      );
    } else {
      success = await fnHandleCreateSidebar(cleanValues);
      if (success) {
        drawerValues.value = { menuType: 0 };
        sidebarFormRef.value?.resetForm();
      }
    }
  };

  /* ***************************
   * Return Hook Interface
   *************************** */

  return {
    loading,
    filterRef,
    pagination,
    records,
    multipleSelection,
    sort,
    handleBulkDelete,
    handleDelete,
    handlePermanentDelete,
    handleBulkPermanentDelete,
    handleRestore,
    handleBulkRestore,
    fnGetSidebars,
    fnHandlePageChange,
    fnHandleSelectionChange,
    fnHandleSortChange,
    fnHandleSizeChange,
    filterVisible,
    drawerVisible,
    drawerValues,
    sidebarFormRef,
    handleSubmit,
    handleFilter,
    handleEdit
  };
}
