<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, ref } from "vue";

const props = defineProps<{
  visible: boolean;
  values: FieldValues;
}>();

const emit = defineEmits<{
  (e: "update:visible", visible: boolean): void;
  (e: "update:values", values: FieldValues): void;
  (e: "submit", values: FieldValues): void;
  (e: "close"): void;
}>();



const formRef = ref();
const loading = ref(false);

const columns = computed<PlusColumn[]>(() => [
  {
    label: $t("Email"),
    prop: "email",
    valueType: "text",
    fieldProps: {
      placeholder: $t("Enter email address")
    },
    formItemProps: {
      rules: [
        {
          required: true,
          message: $t("Please enter email address"),
          trigger: "blur"
        },
        {
          type: "email",
          message: $t("Please enter a valid email address"),
          trigger: "blur"
        }
      ]
    }
  },
  {
    label: $t("Role"),
    prop: "role",
    valueType: "select",
    options: [
      { label: "Admin", value: "admin" },
      { label: "Member", value: "member" },
      { label: "Guest", value: "guest" }
    ],
    fieldProps: {
      placeholder: $t("Select role")
    },
    formItemProps: {
      rules: [
        {
          required: true,
          message: $t("Please select a role"),
          trigger: "change"
        }
      ]
    }
  },
  {
    label: $t("Personal Message"),
    prop: "message",
    valueType: "textarea",
    fieldProps: {
      placeholder: $t("Optional personal message to include in the invitation"),
      rows: 3,
      maxlength: 500,
      showWordLimit: true
    }
  },
  {
    label: $t("Expires In (Days)"),
    prop: "expiresIn",
    valueType: "input-number",
    fieldProps: {
      min: 1,
      max: 30,
      placeholder: $t("Number of days until invitation expires")
    },
    tooltip: $t("Invitation will expire after this many days (default: 7 days)")
  }
]);

const handleSubmit = async () => {
  if (!formRef.value?.formInstance) return;

  loading.value = true;
  try {
    // Set default values
    const submitData = {
      ...props.values,
      role: props.values.role || "member",
      expiresIn: props.values.expiresIn || 7
    };

    await emit("submit", submitData);
  } catch (error) {
    console.error("Submit error:", error);
  } finally {
    loading.value = false;
  }
};

const resetForm = () => {
  formRef.value?.formInstance?.resetFields();
};

defineExpose({
  resetForm
});
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :visible="visible"
    :model-value="values"
    :form="{
      columns,
      labelWidth: '120px',
      labelPosition: 'left',
      hasFooter: true
    }"
    :drawer="{
      title: $t('Send Invitation'),
      size: '500px',
      destroyOnClose: true
    }"
    :loading="loading"
    @update:visible="val => emit('update:visible', val)"
    @update:model-value="val => emit('update:values', val)"
    @confirm="handleSubmit"
    @cancel="emit('close')"
  />
</template>
