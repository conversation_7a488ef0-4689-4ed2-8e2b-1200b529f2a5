import { reactive, ref, computed } from "vue";
import { useRoute } from "vue-router";
import { $t } from "@/plugins/i18n";
import type { FieldValues } from "plus-pro-components";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";

import { useConvertKeyToCamel, useConvertKeyToSnake } from "@/utils/helpers";
import { clone } from "@pureadmin/utils";
import type { FormItemProps, KnowledgeBaseFilterProps } from "./type";
import {
  getKnowledgeBases,
  getKnowledgeBasesDropdown,
  createKnowledgeBase,
  updateKnowledgeBaseById,
  bulkDeleteKnowledgeBases,
  deleteKnowledgeBase,
  deleteKnowledgeBasePermanent,
  bulkDeleteKnowledgeBasesPermanent,
  restoreKnowledgeBase,
  bulkRestoreKnowledgeBases,
  bulkRetrainKnowledgeBases
} from "./auth-api";

export function useKnowledgeBaseHook() {
  /* ***************************
   * Data/State Management
   *************************** */

  const route = useRoute();
  const organizationUuid = computed(() => route.params.id as string);

  const loading = ref(false);
  const filterRef = ref<KnowledgeBaseFilterProps>({ isTrashed: "no" });
  const pagination = reactive({
    total: 0,
    pageSize: 20,
    currentPage: 1,
    pageSizes: [10, 20, 50, 100, 200],
    background: true,
    layout: "total, sizes, prev, pager, next, jumper"
  });
  const multipleSelection = ref([]);
  const records = ref([]);
  const sort = ref({ sortBy: "createdAt", sortOrder: "asc" });
  const knowledgeBasesDropdown = ref([]);

  // Form refs
  const knowledgeBaseFormRef = ref();
  const filterVisible = ref(false);
  const drawerVisible = ref(false);
  const drawerValues = ref<FormItemProps>({
    type: "text"
  });

  /* ***************************
   * API Data Fetching
   *************************** */

  const fnGetKnowledgeBases = async () => {
    loading.value = true;
    try {
      const response = await getKnowledgeBases(
        organizationUuid.value,
        useConvertKeyToSnake({
          ...filterRef.value,
          order: `${useConvertKeyToSnake(sort.value.sortBy)}:${sort.value.sortOrder}`,
          page: pagination.currentPage,
          limit: pagination.pageSize
        })
      );
      records.value = useConvertKeyToCamel(response.data);
      pagination.total = response.total;
    } catch (e) {
      console.error("Get KnowledgeBases error:", e);
      message(e.response?.data?.message || e?.message || $t("Get failed"), {
        type: "error"
      });
    } finally {
      loading.value = false;
    }
  };

  const fnGetKnowledgeBasesDropdown = async () => {
    try {
      const response = await getKnowledgeBasesDropdown(organizationUuid.value);
      knowledgeBasesDropdown.value = useConvertKeyToCamel(response.data);
    } catch (e) {
      console.error("Get KnowledgeBases dropdown error:", e);
    }
  };

  /* ***************************
   * CRUD Operations
   *************************** */

  const fnHandleCreateKnowledgeBase = async (data: any) => {
    try {
      loading.value = true;
      const response = await createKnowledgeBase(organizationUuid.value, data);
      if (response.success) {
        message(response.message || $t("Create successful"), {
          type: "success"
        });
        await fnGetKnowledgeBases();
        return true;
      }
      message(response.message || $t("Create failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Create failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  const fnHandleUpdateKnowledgeBase = async (id: string, data: any) => {
    try {
      loading.value = true;
      const response = await updateKnowledgeBaseById(organizationUuid.value, id, data);
      if (response.success) {
        message(response.message || $t("Update successful"), {
          type: "success"
        });
        await fnGetKnowledgeBases();
        return true;
      }
      message(response.message || $t("Update failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Update failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  /* ***************************
   * Table Event Handlers
   *************************** */

  const fnHandleSelectionChange = (val: any) => {
    multipleSelection.value = val;
  };

  const fnHandleSortChange = async ({ prop, order }) => {
    sort.value.sortBy = prop;
    sort.value.sortOrder = order === "ascending" ? "asc" : "desc";
    await fnGetKnowledgeBases();
  };

  const fnHandlePageChange = async (val: number) => {
    pagination.currentPage = val;
    await fnGetKnowledgeBases();
  };

  const fnHandleSizeChange = async (val: number) => {
    pagination.pageSize = val;
    pagination.currentPage = 1;
    await fnGetKnowledgeBases();
  };

  /* ***************************
   * Delete Operations
   *************************** */

  const handleDelete = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to delete this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      await fnHandleDelete(row.uuid);
    } catch {
      console.log("Delete cancelled");
    }
  };

  const fnHandleDelete = async (id: string) => {
    try {
      loading.value = true;
      const response = await deleteKnowledgeBase(organizationUuid.value, id);
      if (response.success) {
        message(response.message || $t("Delete successful"), {
          type: "success"
        });
        await fnGetKnowledgeBases();
        return true;
      }
      message(response.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Delete failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  const handleBulkDelete = async (tableRef?: any) => {
    const ids = multipleSelection.value.map(item => item.uuid);
    if (ids.length === 0) {
      message($t("Please select items to delete"), {
        type: "warning"
      });
      return;
    }
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to delete selected items?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const success = await fnHandleBulkDelete(ids);
      if (success && tableRef?.clearSelection) {
        tableRef.clearSelection();
      }
    } catch {
      console.log("Delete cancelled");
    }
  };

  const fnHandleBulkDelete = async (ids: string[]) => {
    try {
      loading.value = true;
      const response = await bulkDeleteKnowledgeBases(organizationUuid.value, {
        ids
      });
      if (response.success) {
        message(response.message || $t("Delete successful"), {
          type: "success"
        });
        await fnGetKnowledgeBases();
        return true;
      }
      message(response.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      console.error("Bulk delete KnowledgeBases error:", error);
      message(error.response?.data?.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } finally {
      loading.value = false;
    }
  };

  /* ***************************
   * Permanent Delete Operations
   *************************** */

  const handlePermanentDelete = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to permanently delete this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      await fnHandlePermanentDelete(row.uuid);
    } catch {
      console.log("Delete cancelled");
    }
  };

  const fnHandlePermanentDelete = async (id: string) => {
    try {
      loading.value = true;
      const response = await deleteKnowledgeBasePermanent(organizationUuid.value, id);
      if (response.success) {
        message(response.message || $t("Delete successful"), {
          type: "success"
        });
        await fnGetKnowledgeBases();
        return true;
      }
      message(response.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Delete failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  const handleBulkPermanentDelete = async (tableRef?: any) => {
    const ids = multipleSelection.value.map(item => item.uuid);
    if (ids.length === 0) {
      message($t("Please select items to delete"), {
        type: "warning"
      });
      return;
    }
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to permanently delete selected items?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const success = await fnHandleBulkPermanentDelete(ids);
      if (success && tableRef?.clearSelection) {
        tableRef.clearSelection();
      }
    } catch {
      console.log("Delete cancelled");
    }
  };

  const fnHandleBulkPermanentDelete = async (ids: string[]) => {
    try {
      loading.value = true;
      const response = await bulkDeleteKnowledgeBasesPermanent(organizationUuid.value, {
        ids
      });
      if (response.success) {
        message(response.message || $t("Delete successful"), {
          type: "success"
        });
        await fnGetKnowledgeBases();
        return true;
      }
      message(response.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      console.error("Bulk permanent delete KnowledgeBases error:", error);
      message(error.response?.data?.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } finally {
      loading.value = false;
    }
  };

  /* ***************************
   * Restore Operations
   *************************** */

  const handleRestore = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to restore this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      await fnHandleRestore(row.uuid);
    } catch {
      console.log("Restore cancelled");
    }
  };

  const fnHandleRestore = async (id: string) => {
    try {
      loading.value = true;
      const response = await restoreKnowledgeBase(organizationUuid.value, id);
      if (response.success) {
        message(response.message || $t("Restore successful"), {
          type: "success"
        });
        await fnGetKnowledgeBases();
        return true;
      }
      message(response.message || $t("Restore failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Restore failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  const handleBulkRestore = async (tableRef?: any) => {
    const ids = multipleSelection.value.map(item => item.uuid);
    if (ids.length === 0) {
      message($t("Please select items to restore"), {
        type: "warning"
      });
      return;
    }
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to restore selected items?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const success = await fnHandleBulkRestore(ids);
      if (success && tableRef?.clearSelection) {
        tableRef.clearSelection();
      }
    } catch {
      console.log("Restore cancelled");
    }
  };

  const fnHandleBulkRestore = async (ids: string[]) => {
    try {
      loading.value = true;
      const response = await bulkRestoreKnowledgeBases(organizationUuid.value, {
        ids
      });
      if (response.success) {
        message(response.message || $t("Restore successful"), {
          type: "success"
        });
        await fnGetKnowledgeBases();
        return true;
      }
      message(response.message || $t("Restore failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      console.error("Bulk restore KnowledgeBases error:", error);
      message(error.response?.data?.message || $t("Restore failed"), {
        type: "error"
      });
      return false;
    } finally {
      loading.value = false;
    }
  };

  /* ***************************
   * Form handlers and actions
   *************************** */

  const handleEdit = (row: any) => {
    drawerValues.value = { ...clone(row, true) };
    drawerVisible.value = true;
  };

  const handleFilter = async (values: KnowledgeBaseFilterProps) => {
    filterRef.value = values;
    await fnGetKnowledgeBases();
  };

  const handleSubmit = async (values: FieldValues) => {
    let success = false;
    if (values.uuid != null) {
      success = await fnHandleUpdateKnowledgeBase(
        values.uuid.toString(),
        values
      );
    } else {
      success = await fnHandleCreateKnowledgeBase(values);
      if (success) {
        drawerValues.value = { status: "active" };
        knowledgeBaseFormRef.value?.resetForm();
      }
    }
  };

  const handleRetrain = async (tableRef?: any) => {
    const ids = multipleSelection.value.map(item => item.uuid);
    if (ids.length === 0) {
      message($t("Please select items to retrain"), {
        type: "warning"
      });
      return;
    }
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to retrain selected items? This may take a while."),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const success = await fnBulkRetrain(ids);
      if (success && tableRef?.clearSelection) {
        tableRef.clearSelection();
      }
    } catch {
      console.log("Retrain cancelled");
    }
  };

  const fnBulkRetrain = async (ids: string[]) => {
    try {
      loading.value = true;
      const response = await bulkRetrainKnowledgeBases(organizationUuid.value, {
        ids
      });
      if (response.success) {
        message(response.message || $t("Retraining queued successfully"), {
          type: "success"
        });
        return true;
      }
      message(response.message || $t("Retrain failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      console.error("Bulk retrain KnowledgeBases error:", error);
      message(error.response?.data?.message || $t("Retrain failed"), {
        type: "error"
      });
      return false;
    } finally {
      loading.value = false;
    }
  };

  /* ***************************
   * Return Hook Interface
   *************************** */

  return {
    loading,
    filterRef,
    pagination,
    records,
    multipleSelection,
    sort,
    knowledgeBasesDropdown,
    handleBulkDelete,
    handleDelete,
    handlePermanentDelete,
    handleBulkPermanentDelete,
    handleRestore,
    handleBulkRestore,
    fnGetKnowledgeBases,
    fnGetKnowledgeBasesDropdown,
    fnHandlePageChange,
    fnHandleSelectionChange,
    fnHandleSortChange,
    fnHandleSizeChange,
    filterVisible,
    drawerVisible,
    drawerValues,
    knowledgeBaseFormRef,
    handleSubmit,
    handleFilter,
    handleEdit,
    handleRetrain
  };
}
