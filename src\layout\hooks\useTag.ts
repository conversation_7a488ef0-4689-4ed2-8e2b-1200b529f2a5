import {
  ref,
  unref,
  computed,
  reactive,
  onMounted,
  type CSSProperties,
  getCurrentInstance
} from "vue";
import type { tagsViewsType } from "../types";
import { useRoute, useRouter } from "vue-router";
import { responsiveStorageNameSpace } from "@/config";
import { useSettingStoreHook } from "@/store/modules/settings";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import {
  isEqual,
  isBoolean,
  storageLocal,
  toggleClass,
  hasClass
} from "@pureadmin/utils";

import Fullscreen from "@iconify-icons/ri/fullscreen-fill";
import CloseAllTags from "@iconify-icons/ri/subtract-line";
import CloseOtherTags from "@iconify-icons/ri/text-spacing";
import CloseRightTags from "@iconify-icons/ri/text-direction-l";
import CloseLeftTags from "@iconify-icons/ri/text-direction-r";
import RefreshRight from "@iconify-icons/ep/refresh-right";
import Close from "@iconify-icons/ep/close";

// A placeholder for your i18n function
const $ = (key: string) => key;

export function useTags() {
  const route = useRoute();
  const router = useRouter();
  const instance = getCurrentInstance();
  const pureSetting = useSettingStoreHook();

  const buttonTop = ref(0);
  const buttonLeft = ref(0);
  const translateX = ref(0);
  const visible = ref(false);
  const activeIndex = ref(-1);
  // Information of the currently right-clicked route
  const currentSelect = ref({});
  const isScrolling = ref(false);

  /** Display mode, default is smart mode */
  const showModel = ref(
    storageLocal().getItem<StorageConfigs>(
      `${responsiveStorageNameSpace()}configure`
    )?.showModel || "smart"
  );
  /** Whether to hide the tab bar, displayed by default */
  const showTags =
    ref(
      storageLocal().getItem<StorageConfigs>(
        `${responsiveStorageNameSpace()}configure`
      ).hideTabs
    ) ?? ref("false");
  const multiTags: any = computed(() => {
    return useMultiTagsStoreHook().multiTags;
  });

  const tagsViews = reactive<Array<tagsViewsType>>([
    {
      icon: RefreshRight,
      text: $("Reload"),
      divided: false,
      disabled: false,
      show: true
    },
    {
      icon: Close,
      text: $("Close Current Tab"),
      divided: false,
      disabled: multiTags.value.length <= 1,
      show: true
    },
    {
      icon: CloseLeftTags,
      text: $("Close Left Tabs"),
      divided: true,
      disabled: multiTags.value.length <= 1,
      show: true
    },
    {
      icon: CloseRightTags,
      text: $("Close Right Tabs"),
      divided: false,
      disabled: multiTags.value.length <= 1,
      show: true
    },
    {
      icon: CloseOtherTags,
      text: $("Close Other Tabs"),
      divided: true,
      disabled: multiTags.value.length <= 2,
      show: true
    },
    {
      icon: CloseAllTags,
      text: $("Close All Tabs"),
      divided: false,
      disabled: multiTags.value.length <= 1,
      show: true
    },
    {
      icon: Fullscreen,
      text: $("Content Area Fullscreen"),
      divided: true,
      disabled: false,
      show: true
    }
  ]);

  function conditionHandle(item, previous, next) {
    if (!route) return next;
    if (isBoolean(route?.meta?.showLink) && route?.meta?.showLink === false) {
      // Đối với routes có autoCreateTab, cần so sánh cả path và query/params
      if (route?.meta?.autoCreateTab) {
        const pathMatch = route.path === item.path;
        if (Object.keys(route.query || {}).length > 0) {
          return pathMatch && isEqual(route.query, item.query) ? previous : next;
        } else if (Object.keys(route.params || {}).length > 0) {
          return pathMatch && isEqual(route.params, item.params) ? previous : next;
        } else {
          return pathMatch ? previous : next;
        }
      } else {
        // Logic cũ cho routes thông thường có showLink: false
        if (Object.keys(route.query || {}).length > 0) {
          return isEqual(route.query, item.query) ? previous : next;
        } else {
          return isEqual(route.params, item.params) ? previous : next;
        }
      }
    } else {
      return route.path === item.path ? previous : next;
    }
  }

  const isFixedTag = computed(() => {
    return (item: any) => {
      return isBoolean(item?.meta?.fixedTag) && item?.meta?.fixedTag === true;
    };
  });

  const iconIsActive = computed(() => {
    return (item: any, index: number) => {
      if (index === 0) return;
      return conditionHandle(item, true, false);
    };
  });

  const linkIsActive = computed(() => {
    return (item: any) => {
      return conditionHandle(item, "is-active", "");
    };
  });

  const scheduleIsActive = computed(() => {
    return (item: any) => {
      return conditionHandle(item, "schedule-active", "");
    };
  });

  const getTabStyle = computed((): CSSProperties => {
    return {
      transform: `translateX(${translateX.value}px)`,
      transition: isScrolling.value ? "none" : "transform 0.5s ease-in-out"
    };
  });

  const getContextMenuStyle = computed((): CSSProperties => {
    return { left: buttonLeft.value + "px", top: buttonTop.value + "px" };
  });

  const closeMenu = () => {
    visible.value = false;
  };

  /** Add active style on mouse enter */
  function onMouseenter(index) {
    if (index) activeIndex.value = index;
    if (unref(showModel) === "smart") {
      if (hasClass(instance.refs["schedule" + index][0], "schedule-active"))
        return;
      toggleClass(true, "schedule-in", instance.refs["schedule" + index][0]);
      toggleClass(false, "schedule-out", instance.refs["schedule" + index][0]);
    } else {
      if (hasClass(instance.refs["dynamic" + index][0], "is-active")) return;
      toggleClass(true, "card-in", instance.refs["dynamic" + index][0]);
      toggleClass(false, "card-out", instance.refs["dynamic" + index][0]);
    }
  }

  /** Restore default style on mouse leave */
  function onMouseleave(index) {
    activeIndex.value = -1;
    if (unref(showModel) === "smart") {
      if (hasClass(instance.refs["schedule" + index][0], "schedule-active"))
        return;
      toggleClass(false, "schedule-in", instance.refs["schedule" + index][0]);
      toggleClass(true, "schedule-out", instance.refs["schedule" + index][0]);
    } else {
      if (hasClass(instance.refs["dynamic" + index][0], "is-active")) return;
      toggleClass(false, "card-in", instance.refs["dynamic" + index][0]);
      toggleClass(true, "card-out", instance.refs["dynamic" + index][0]);
    }
  }

  function onContentFullScreen() {
    pureSetting.changeValueByKey({
      key: "hiddenSideBar",
      value: !pureSetting.hiddenSideBar
    });
  }

  onMounted(() => {
    if (!showModel.value) {
      const configure = storageLocal().getItem<StorageConfigs>(
        `${responsiveStorageNameSpace()}configure`
      );
      configure.showModel = "card";
      storageLocal().setItem(
        `${responsiveStorageNameSpace()}configure`,
        configure
      );
    }
  });

  return {
    Close,
    route,
    router,
    visible,
    showTags,
    instance,
    multiTags,
    showModel,
    tagsViews,
    buttonTop,
    buttonLeft,
    translateX,
    isFixedTag,
    pureSetting,
    activeIndex,
    getTabStyle,
    isScrolling,
    iconIsActive,
    linkIsActive,
    currentSelect,
    scheduleIsActive,
    getContextMenuStyle,
    closeMenu,
    onMounted,
    onMouseenter,
    onMouseleave,
    onContentFullScreen
  };
}
