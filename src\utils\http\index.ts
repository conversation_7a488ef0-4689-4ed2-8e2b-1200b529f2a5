import Axios, {
  type AxiosInstance,
  type AxiosRequestConfig,
  type CustomParamsSerializer
} from "axios";
import type {
  PureHttpError,
  RequestMethods,
  PureHttpResponse,
  PureHttpRequestConfig
} from "./types.d";
import { stringify } from "qs";
import NProgress from "../progress";
import { getToken, formatToken } from "@/utils/auth";
import { useUserStoreHook } from "@/store/modules/user";

// Related configuration please refer to: www.axios-js.com/docs/#axios-request-config-1
const defaultConfig: AxiosRequestConfig = {
  // Request timeout time
  timeout: 120000,
  headers: {
    Accept: "application/json, text/plain, */*",
    "Content-Type": "application/json",
    "X-Requested-With": "XMLHttpRequest"
  },
  // Array format parameter serialization (https://github.com/axios/axios/issues/5142)
  paramsSerializer: {
    serialize: stringify as unknown as CustomParamsSerializer
  }
};

class PureHttp {
  constructor() {
    this.httpInterceptorsRequest();
    this.httpInterceptorsResponse();
  }

  /** Temporarily store pending requests after `token` expires */
  private static requests = [];

  /** Prevent duplicate refresh `token` */
  private static isRefreshing = false;

  /** Prevent duplicate logout */
  private static isLoggingOut = false;

  /** Initialize configuration object */
  private static initConfig: PureHttpRequestConfig = {};

  /** Save current `Axios` instance object */
  private static axiosInstance: AxiosInstance = Axios.create(defaultConfig);

  /** Reconnect original request */
  private static retryOriginalRequest(config: PureHttpRequestConfig) {
    return new Promise(resolve => {
      PureHttp.requests.push((token: string) => {
        config.headers["Authorization"] = formatToken(token);
        resolve(config);
      });
    });
  }

  /** Request interceptor */
  private httpInterceptorsRequest(): void {
    PureHttp.axiosInstance.interceptors.request.use(
      async (config: PureHttpRequestConfig): Promise<any> => {
        // Start progress bar animation
        // NProgress.start();
        // First check if post/get methods pass callback, otherwise execute initialization settings callback
        if (typeof config.beforeRequestCallback === "function") {
          config.beforeRequestCallback(config);
          return config;
        }
        if (PureHttp.initConfig.beforeRequestCallback) {
          PureHttp.initConfig.beforeRequestCallback(config);
          return config;
        }
        /** Request whitelist, place some interfaces that don't need `token` (by setting request whitelist, prevent infinite loop caused by requests after `token` expires) */
        const whiteList = [
          "/refresh-token",
          "/login",
          "/logout",
          "/api/v1/auth/refresh",
          "/api/v1/auth/login",
          "/api/v1/auth/logout"
        ];
        return whiteList.some(url => config.url.endsWith(url))
          ? config
          : new Promise(resolve => {
              const data = getToken();
              if (data) {
                const now = new Date().getTime();
                const expired =
                  (typeof data.expires === "number"
                    ? data.expires
                    : parseInt(data.expires)) -
                    now <=
                  0;
                if (expired) {
                  if (!PureHttp.isRefreshing) {
                    PureHttp.isRefreshing = true;
                    // Token expired, refresh
                    useUserStoreHook()
                      .handRefreshToken({ refreshToken: data.refreshToken })
                      .then(res => {
                        const token = res.data.accessToken;
                        config.headers["Authorization"] = formatToken(token);
                        PureHttp.requests.forEach(cb => cb(token));
                        PureHttp.requests = [];
                      })
                      .catch(() => {
                        // Refresh token failed, logout user
                        console.log(
                          "🔄 Refresh token failed, triggering logout"
                        );
                        if (!PureHttp.isLoggingOut) {
                          PureHttp.isLoggingOut = true;
                          useUserStoreHook()
                            .logOut()
                            .finally(() => {
                              PureHttp.isLoggingOut = false;
                            });
                        }
                      })
                      .finally(() => {
                        PureHttp.isRefreshing = false;
                      });
                  }
                  resolve(PureHttp.retryOriginalRequest(config));
                } else {
                  config.headers["Authorization"] = formatToken(
                    data.accessToken
                  );
                  resolve(config);
                }
              } else {
                resolve(config);
              }
            });
      },
      error => {
        return Promise.reject(error);
      }
    );
  }

  /** Response interceptor */
  private httpInterceptorsResponse(): void {
    const instance = PureHttp.axiosInstance;
    instance.interceptors.response.use(
      (response: PureHttpResponse) => {
        const $config = response.config;
        // Close progress bar animation
        NProgress.done();
        // First check if post/get methods pass callback, otherwise execute initialization settings callback
        if (typeof $config.beforeResponseCallback === "function") {
          $config.beforeResponseCallback(response);
          return response.data;
        }
        if (PureHttp.initConfig.beforeResponseCallback) {
          PureHttp.initConfig.beforeResponseCallback(response);
          return response.data;
        }
        return response.data;
      },
      (error: PureHttpError) => {
        const $error = error;
        $error.isCancelRequest = Axios.isCancel($error);
        // Close progress bar animation
        NProgress.done();

        // Handle 401 Unauthorized errors
        if (error.response?.status === 401) {
          // Check if this is a logout request to avoid infinite loop
          const isLogoutRequest = error.config?.url?.endsWith("/logout");
          if (!isLogoutRequest && !PureHttp.isLoggingOut) {
            PureHttp.isLoggingOut = true;
            useUserStoreHook()
              .logOut()
              .finally(() => {
                PureHttp.isLoggingOut = false;
              });
          }
        }

        // All response exceptions distinguish between cancel request/non-cancel request sources
        return Promise.reject($error);
      }
    );
  }

  /** Common request tool function */
  public request<T>(
    method: RequestMethods,
    url: string,
    param?: AxiosRequestConfig,
    axiosConfig?: PureHttpRequestConfig
  ): Promise<T> {
    const config = {
      method,
      url,
      ...param,
      ...axiosConfig
    } as PureHttpRequestConfig;

    // Handle custom request/response callbacks separately
    return new Promise((resolve, reject) => {
      PureHttp.axiosInstance
        .request(config)
        .then((response: undefined) => {
          resolve(response);
        })
        .catch(error => {
          reject(error);
        });
    });
  }

  /** Separately extracted `post` tool function */
  public post<T, P>(
    url: string,
    params?: AxiosRequestConfig<P>,
    config?: PureHttpRequestConfig
  ): Promise<T> {
    return this.request<T>("post", url, params, config);
  }

  /** Separately extracted `get` tool function */
  public get<T, P>(
    url: string,
    params?: AxiosRequestConfig<P>,
    config?: PureHttpRequestConfig
  ): Promise<T> {
    return this.request<T>("get", url, params, config);
  }
}

export const http = new PureHttp();
