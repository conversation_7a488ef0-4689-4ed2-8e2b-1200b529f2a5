import { $t } from "@/plugins/i18n";
import { ElTag } from "element-plus";
import { h } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

export const columns: TableColumnList = [
  {
    headerRenderer: () => $t("Title"),
    prop: "title",
    align: "left",
    minWidth: 200,
    cellRenderer: ({ row }) => (
      <>
        <span class="inline-block mr-1">
          {h(useRenderIcon(row.icon), {
            style: { paddingTop: "1px" }
          })}
        </span>
        <span>{$t(row.title)}</span>
      </>
    )
  },
  {
    prop: "path",
    align: "left",
    minWidth: 210,
    headerRenderer: () => $t("Path"),
    showOverflowTooltip: true,
    formatter: ({ path }) => path || "-"
  },
  {
    prop: "menuType",
    align: "center",
    width: 100,
    headerRenderer: () => $t("Type"),
    cellRenderer: ({ row }) => {
      const menuTypes = {
        0: { label: "Menu", type: "primary" },
        1: { label: "Iframe", type: "warning" },
        2: { label: "External", type: "danger" },
        3: { label: "Button", type: "info" }
      };

      const { label, type } = menuTypes[row.menuType] || menuTypes[0];

      return h(
        ElTag,
        {
          type,
          size: "small",
          effect: "plain"
        },
        () => $t(label)
      );
    }
  },
  {
    headerRenderer: () => $t("Order"),
    prop: "rank",
    align: "center",
    width: 100
  },
  {
    headerRenderer: () => $t("Hidden"),
    prop: "showLink",
    align: "center",
    formatter: ({ showLink }) => (showLink ? $t("No") : $t("Yes")),
    width: 100
  },
  {
    label: "",
    fixed: "right",
    width: 140,
    slot: "operation",
    sortable: false
  }
];
