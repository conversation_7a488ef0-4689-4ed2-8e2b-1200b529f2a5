import { ref, reactive, computed, nextTick, watch, onUnmounted } from "vue";
import { ElMessageBox } from "element-plus";
import { useChatBotStoreHook } from "@/store/modules/chat";
import {
  sendAndRespond,
  createConversation,
  deleteConversation,
  getConversations,
  getMessages,
  updateConversation,
  sendGeneralTitleConversation
} from "@/views/chat/utils/auth-api";
import { useSoketi } from "@/services/soketi.service";
import { useConvertKeyToCamel } from "@/utils/helpers";
import { message } from "@/utils/message";
import { $t } from "@/plugins/i18n";
import { clone, uuid } from "@pureadmin/utils";

export function useChatBot() {
  // ===== STORES & SERVICES =====
  const chatBotStore = useChatBotStoreHook();
  const { subscribe, unsubscribe } = useSoketi();

  // ===== STATE MANAGEMENT =====
  const loading = ref(false);
  const loadingMessages = ref(false);
  const messages = reactive<{ [conversationId: string]: any[] }>({});
  const currentConversationId = ref<string | null>(null);
  const newMessage = ref("");
  const isTyping = ref(false);
  const attachedFiles = ref([]);
  const conversations = ref([]);
  const showCurrentAgentHistory = ref(false);
  const chatMessagesContainer = ref<HTMLElement | null>(null);
  const selectedAgent = ref(null);

  // ===== COMPUTED PROPERTIES =====
  const selectedConversation = computed(
    () => chatBotStore.selectedConversation
  );

  const agents = computed(() => [...chatBotStore.bots]);

  const currentMessages = computed(() => {
    if (!currentConversationId.value) return [];
    return messages[currentConversationId.value] || [];
  });

  // Load messages của conversation
  const fetchConversationMessages = async (
    conversationId: string,
    page: number = 1
  ) => {
    try {
      const response = await getMessages(conversationId, { page });
      if (response.success && response.data) {
        // Convert API messages to local format
        const formattedMessages = response.data.map((msg: any) => ({
          id: msg.id,
          role: msg.role,
          content: msg.content
        }));
        messages[conversationId] = formattedMessages;
        return formattedMessages;
      }
      return [];
    } catch (error) {
      console.error("Error loading conversation messages:", error.messages);
      return [];
    } finally {
      scrollToBottom();
    }
  };

  // ===== UTILITY FUNCTIONS =====
  // Xóa attachment
  const removeAttachment = (index: number) => {
    attachedFiles.value.splice(index, 1);
  };

  // ===== WATCHERS =====
  watch(
    selectedConversation,
    newConversation => {
      if (newConversation && newConversation?.uuid) {
        if (currentConversationId.value !== newConversation.uuid) {
          currentConversationId.value = newConversation.uuid;
          fetchConversationMessages(newConversation.uuid);
        }
      } else {
        currentConversationId.value = null;
      }
    },
    { immediate: true, deep: true }
  );

  // Scroll to bottom function
  const scrollToBottom = () => {
    nextTick(() => {
      if (chatMessagesContainer.value) {
        chatMessagesContainer.value.scrollTo({
          top: chatMessagesContainer.value.scrollHeight,
          behavior: "smooth"
        });
      }
    });
  };

  watch(
    currentConversationId,
    (newId, oldId) => {
      if (currentConversationId.value?.includes("temp-")) {
        return;
      }
      if (oldId) {
        const oldChannel = `private-conversation.${oldId}`;
        unsubscribe(oldChannel);
      }

      if (newId) {
        if (!newId) return;
        const newChannel = `private-conversation.${newId}`;
        subscribe(newChannel, "message.received", (data: any) => {
          messages[newId]
            ? messages[newId].push(data)
            : (messages[newId] = [data]);
          isTyping.value = false;
          scrollToBottom();
        });
      }
    },
    {
      immediate: true
    }
  );

  watch(selectedAgent, value => {
    const bot = agents.value.find(item => item.uuid == value);
    if (bot) {
      currentConversationId.value = `temp-${bot.uuid}`;
      messages[currentConversationId.value] = [
        {
          id: bot.uuid,
          role: "assistant",
          content: bot.greetingMessage
        }
      ];
    }
  });

  onUnmounted(() => {
    if (currentConversationId.value) {
      const lastChannel = `private-conversation.${currentConversationId.value}`;
      unsubscribe(lastChannel);
    }
  });

  // ===== CONVERSATION MANAGEMENT =====
  // Lấy tất cả conversations
  const fetchAllConversations = async () => {
    try {
      const response = await getConversations();
      if (response.success && response.data) {
        conversations.value = useConvertKeyToCamel(response.data);
        return response.data;
      }
      return [];
    } catch (error) {
      console.error("Error fetching conversations for bot:", error);
      return [];
    }
  };

  // Xóa conversation với confirmation dialog
  const confirmDeleteConversation = async (index: number, row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to delete this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      await deleteConversationById(index, row.uuid);
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error deleting conversation:", error);
        message($t("Delete failed"), { type: "error" });
      }
    }
  };

  // Thực hiện xóa conversation
  const deleteConversationById = async (idx: number, id: string) => {
    try {
      loading.value = true;
      const response = await deleteConversation(id.toString());
      if (response.success) {
        message(response.message || $t("Delete successful"), {
          type: "success"
        });
        conversations.value?.splice(idx, 1);
        return true;
      }
      message(response.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Delete failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  // Cập nhật conversation với prompt dialog
  const promptUpdateConversation = async (idx: number, row: any) => {
    console.log("promptUpdateConversation called - showing prompt dialog", row);
    try {
      const { value } = await ElMessageBox.prompt(
        $t("Please enter the new title for the conversation."),
        $t("Update Conversation Title"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          inputValue: row.title
        }
      );
      if (value) {
        await updateConversationById(idx, row.uuid, value);
      }
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error updating conversation:", error);
        message($t("Update failed"), { type: "error" });
      }
    }
  };

  // Thực hiện cập nhật conversation
  const updateConversationById = async (
    idx: number,
    id: string,
    value: string
  ) => {
    try {
      loading.value = true;
      const response = await updateConversation(id, { title: value });
      if (response.success) {
        message(response.message || $t("Update successful"), {
          type: "success"
        });
        conversations.value[idx].title = response.data.title;
        return true;
      }
      message(response.message || $t("Update failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Update failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  // ===== CONVERSATION ACTIONS =====
  const setConversation = (conversation: any) => {
    chatBotStore.setConversation(conversation);
  };

  const setNewConversation = () => {
    chatBotStore.setConversation({});
    console.log("Setting new conversation");
  };

  const sendMessage = async () => {
    const textToSend = newMessage.value.trim();
    if (!textToSend && attachedFiles.value.length === 0) {
      return;
    }
    newMessage.value = "";
    attachedFiles.value = [];

    messages[currentConversationId.value].push({
      id: uuid(),
      role: "user",
      content: textToSend
    });

    if (currentConversationId.value?.includes("temp-")) {
      const response = await createConversation({
        bot: selectedAgent.value
      });
      if (response.success) {
        messages[response.data.uuid] = clone(
          messages[currentConversationId.value]
        );
        delete messages[currentConversationId.value];
        conversations.value.unshift(response.data);
        currentConversationId.value = response.data.uuid;
        setTimeout(() => {
          generalTitleConversation(response.data.uuid).catch();
        }, 8000);
      }
    }
    await triggerAgentResponse(textToSend);
  };

  // Trigger agent response
  const triggerAgentResponse = async (text: string) => {
    if (isTyping.value || !currentConversationId.value) return;
    scrollToBottom();
    await sendMessageToServer(text);
  };

  // ===== API FUNCTIONS =====
  const generalTitleConversation = async (uuid: any) => {
    await sendGeneralTitleConversation(uuid)
      .then((res: any) => {
        if (res.success) {
          const conversation = conversations.value.find(
            (value: any) => value.uuid == uuid
          );
          if (conversation) {
            if (!res.data) return;
            conversation.title = res.data;
          }
        }
      })
      .catch();
  };

  // API call để gửi tin nhắn
  const sendMessageToServer = async (prompt: string) => {
    isTyping.value = true;
    await sendAndRespond({
      conversationUuid: currentConversationId.value,
      content: prompt
    }).catch(e => {
      message(e?.response?.data?.message || e?.message || $t("Send failed"), {
        type: "error"
      });
    });
  };

  // Send starter prompt as a message
  const sendStarterPrompt = async (prompt: string) => {
    if (!prompt.trim()) return;

    // Set the prompt as new message and send it
    newMessage.value = prompt;
    await sendMessage();
  };

  return {
    // ===== STATE =====
    messages,
    conversations,
    currentConversationId,
    newMessage,
    isTyping,
    attachedFiles,
    chatMessagesContainer,
    showCurrentAgentHistory,
    loadingMessages,
    selectedAgent,

    // ===== COMPUTED =====
    agents,
    currentMessages,
    selectedConversation,

    // ===== API FUNCTIONS =====
    fetchConversationMessages,
    fetchAllConversations,

    // ===== UTILITY FUNCTIONS =====
    removeAttachment,
    scrollToBottom,

    // ===== CONVERSATION MANAGEMENT =====
    confirmDeleteConversation,
    promptUpdateConversation,
    setConversation,
    setNewConversation,
    sendMessage,
    sendStarterPrompt
  };
}
