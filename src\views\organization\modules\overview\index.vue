<script setup lang="ts">
import { onMounted, computed, onUnmounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElSkeleton, ElAlert, ElRow, ElCol, ElButton } from "element-plus";
import { IconifyIconOnline } from "@/components/ReIcon";

// Import components
import StatsCards from "./components/StatsCards.vue";
import ChartsSection from "./components/ChartsSection.vue";
import ActivitySection from "./components/ActivitySection.vue";

// Import composables
import { useOrganizationOverview } from "./composables/useOrganizationOverview";

const route = useRoute();
const router = useRouter();
const organizationId = computed(() => route.params.id as string);

// Use composable for data management
const { stats, loading, error, fetchOverviewData, refreshData } =
  useOrganizationOverview(organizationId);

// Lifecycle hooks
onMounted(async () => {
  await fetchOverviewData();
});

onUnmounted(() => {
  // Cleanup if needed
});
</script>

<template>
  <div class="organization-overview">
    <!-- Loading State -->
    <div v-if="loading && !stats" class="loading-container">
      <el-skeleton animated>
        <template #template>
          <div class="space-y-6">
            <!-- Stats Cards Skeleton -->
            <el-row :gutter="24">
              <el-col
                v-for="i in 4"
                :key="`stats-${i}`"
                :xs="24"
                :sm="12"
                :md="6"
              >
                <div class="skeleton-card h-32" />
              </el-col>
            </el-row>
            <!-- Charts Skeleton -->
            <el-row :gutter="24">
              <el-col v-for="i in 2" :key="`chart-${i}`" :xs="24" :lg="12">
                <div class="skeleton-card h-80" />
              </el-col>
            </el-row>
            <!-- Activity Skeleton -->
            <div class="skeleton-card h-64" />
          </div>
        </template>
      </el-skeleton>
    </div>

    <!-- Main Content -->
    <div v-else class="main-content flex flex-col gap-4">
      <!-- Statistics Cards -->
      <div class="section-container">
        <StatsCards :stats="stats" :loading="loading" @refresh="refreshData" />
      </div>

      <!-- Charts Section -->
      <div class="section-container">
        <ChartsSection :stats="stats" :loading="loading" />
      </div>
    </div>
  </div>
</template>

<style scoped>
.overview-header {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.error-alert {
  margin-bottom: 2rem;
  border-radius: 8px;
}

.loading-container {
  margin-bottom: 2rem;
}

.skeleton-card {
  background-color: #f1f5f9;
  border-radius: 8px;
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.skeleton-card.h-32 {
  height: 8rem;
}

.skeleton-card.h-64 {
  height: 16rem;
}

.skeleton-card.h-80 {
  height: 20rem;
}

.quick-actions {
  padding: 0;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}

/* Animation for skeleton loading */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .organization-overview {
    padding: 1rem;
  }

  .overview-header {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .section-container {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .overview-header h1 {
    font-size: 1.5rem;
  }

  .overview-header .flex {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
}

/* Button styling improvements */
:deep(.el-button) {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
}

:deep(.el-button:hover) {
  transform: translateY(-1px);
}

/* Card improvements */
:deep(.el-card) {
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

:deep(.el-card__header) {
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

/* Alert styling */
:deep(.el-alert) {
  border-radius: 8px;
}
</style>
