import { reactive, ref, computed } from "vue";
import { useRoute } from "vue-router";
import { $t } from "@/plugins/i18n";
import type { FieldValues } from "plus-pro-components";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";

import { useConvertKeyToCamel, useConvertKeyToSnake } from "@/utils/helpers";
import type { FormItemProps, UserFilterProps } from "./type";
import {
  getMembers,
  createMember,
  bulkUpdateMemberRole,
  deleteMemberPermanent,
  bulkDeleteMembersPermanent
} from "./auth-api";

export function useMemberHook() {
  const route = useRoute();
  const organizationUuid = computed(() => route.params.id as string);

  const loading = ref(false);
  const filterRef = ref<UserFilterProps>({ isTrashed: "no" });
  const pagination = reactive({
    total: 0,
    pageSize: 20,
    currentPage: 1,
    pageSizes: [10, 20, 50, 100, 200],
    background: true,
    layout: "total, sizes, prev, pager, next, jumper"
  });
  const multipleSelection = ref([]);
  const records = ref([]);
  const sort = ref({ sortBy: "createdAt", sortOrder: "asc" });

  // Form refs
  const memberFormRef = ref();
  const filterVisible = ref(false);
  const drawerVisible = ref(false);
  const drawerValues = ref<FormItemProps>({
    status: "active"
  });

  /* ***************************
   * API Data Fetching
   *************************** */

  const fnGetMembers = async () => {
    try {
      loading.value = true;
      const res = await getMembers(
        organizationUuid.value,
        useConvertKeyToSnake({
          ...filterRef.value,
          order: `${useConvertKeyToSnake(sort.value.sortBy)}:${sort.value.sortOrder}`,
          page: pagination.currentPage,
          limit: pagination.pageSize
        })
      );
      records.value = useConvertKeyToCamel(res.data);
      pagination.total = res.total;
    } catch (error) {
      console.error("Error fetching members:", error);
      message($t("Failed to fetch members"), { type: "error" });
    } finally {
      loading.value = false;
    }
  };

  // Table Event Handlers

  const fnHandleSelectionChange = (val: any[]) => {
    multipleSelection.value = val;
  };

  const fnHandlePageChange = () => {
    fnGetMembers();
  };

  const fnHandleSizeChange = (val: number) => {
    pagination.pageSize = val;
    pagination.currentPage = 1;
    fnGetMembers();
  };

  const fnHandleSortChange = async (sortBy: string, sortOrder: string) => {
    sort.value = { sortBy, sortOrder };
    await fnGetMembers();
  };

  const fnHandleCreateMember = async (data: any) => {
    try {
      loading.value = true;
      const response = await createMember(organizationUuid.value, data);
      if (response.success) {
        message(response.message || $t("Create successful"), {
          type: "success"
        });
        await fnGetMembers();
        drawerVisible.value = false;
        return true;
      }
      message(response.message || $t("Create failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Create failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  // Delete handlers
  const handlePermanentDelete = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t(
          "Are you sure you want to permanently delete this item? This action cannot be undone."
        ),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      await deleteMemberPermanent(organizationUuid.value, row.uuid);
      message($t("Permanently deleted successfully"), { type: "success" });
      await fnGetMembers();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error permanently deleting user:", error);
        message($t("Permanent delete failed"), { type: "error" });
      }
    }
  };

  const handleBulkPermanentDelete = async () => {
    const selectedIds =
      multipleSelection.value?.map(item => item.pivot.id) || [];
    if (selectedIds.length === 0) {
      message($t("Please select items to delete"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t(
          "Are you sure you want to permanently delete selected items? This action cannot be undone."
        ),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      await bulkDeleteMembersPermanent(organizationUuid.value, {
        ids: selectedIds
      });
      message($t("Permanently deleted successfully"), { type: "success" });
      await fnGetMembers();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error bulk permanently deleting users:", error);
        message($t("Permanent delete failed"), { type: "error" });
      }
    }
  };

  // Form handlers
  const handleFilter = async (values: UserFilterProps) => {
    filterRef.value = values;
    await fnGetMembers();
  };

  const handleSubmit = async (values: FieldValues) => {
    const success = await fnHandleCreateMember(values);
    if (success) {
      drawerValues.value = { status: "active" };
      memberFormRef.value?.resetForm();
    }
  };

  // Bulk Role Update Operations
  const handleBulkUpdateRole = async (role: string, tableRef?: any) => {
    const ids = multipleSelection.value.map(item => item.pivot.id);
    console.log("ids", ids, multipleSelection.value);
    if (ids.length === 0) {
      message($t("Please select items to update"), {
        type: "warning"
      });
      return;
    }
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to update role for selected members?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const success = await fnBulkUpdateRole(ids, role);
      if (success && tableRef?.clearSelection) {
        tableRef.clearSelection();
      }
    } catch {
      console.log("Bulk role update cancelled");
    }
  };

  const fnBulkUpdateRole = async (ids: string[], role: string) => {
    try {
      loading.value = true;
      const response = await bulkUpdateMemberRole(organizationUuid.value, {
        ids,
        role
      });
      if (response.success) {
        message(response.message || $t("Role updated successfully"), {
          type: "success"
        });
        await fnGetMembers();
        return true;
      }
      message(response.message || $t("Role update failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      console.error("Bulk update role error:", error);
      message(error.response?.data?.message || $t("Role update failed"), {
        type: "error"
      });
      return false;
    } finally {
      loading.value = false;
    }
  };

  return {
    loading,
    filterRef,
    pagination,
    records,
    multipleSelection,
    sort,
    handlePermanentDelete,
    handleBulkPermanentDelete,
    handleBulkUpdateRole,
    fnGetMembers,
    fnHandlePageChange,
    fnHandleSelectionChange,
    fnHandleSortChange,
    fnHandleSizeChange,
    filterVisible,
    drawerVisible,
    drawerValues,
    memberFormRef,
    handleSubmit,
    handleFilter
  };
}
