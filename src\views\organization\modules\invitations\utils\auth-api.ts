import { http } from "@/utils/http";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { Result } from "@/utils/response";

type r = Result;

/*
 ***************************
 *   Read Operations (GET)
 ***************************
 */
export const getInvitations = (uuid: string, params?: any) => {
  return http.request<r>(
    "get",
    `/api/v1/auth/organizations/${uuid}/invitations`,
    {
      params
    }
  );
};

export const getInvitationById = (uuid: string, id: string) => {
  return http.request<r>(
    "get",
    `/api/v1/auth/organizations/${uuid}/invitations/${id}`
  );
};

export const getInvitationsDropdown = (uuid: string) => {
  return http.request<r>(
    "get",
    `/api/v1/auth/organizations/${uuid}/invitations/dropdown`
  );
};

/*
 ***************************
 *   Create & Update Operations
 ***************************
 */
export const createInvitation = (uuid: string, data: any) => {
  return http.request<r>(
    "post",
    `/api/v1/auth/organizations/${uuid}/invitations`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

export const updateInvitationById = (uuid: string, id: string, data: any) => {
  return http.request<r>(
    "put",
    `/api/v1/auth/organizations/${uuid}/invitations/${id}`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

export const partialUpdateInvitationById = (
  uuid: string,
  id: string,
  data: any
) => {
  return http.request<r>(
    "patch",
    `/api/v1/auth/organizations/${uuid}/invitations/${id}`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

/*
 ***************************
 *   Invitation Specific Operations
 ***************************
 */
export const sendInvitation = (uuid: string, data: any) => {
  return http.request<r>(
    "post",
    `/api/v1/auth/organizations/${uuid}/invitations/send`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

export const resendInvitation = (uuid: string, id: string) => {
  return http.request<r>(
    "post",
    `/api/v1/auth/organizations/${uuid}/invitations/${id}/resend`
  );
};

export const cancelInvitation = (uuid: string, id: string) => {
  return http.request<r>(
    "post",
    `/api/v1/auth/organizations/${uuid}/invitations/${id}/cancel`
  );
};

export const acceptInvitation = (uuid: string, token: string, data?: any) => {
  return http.request<r>(
    "post",
    `/api/v1/auth/organizations/${uuid}/invitations/accept/${token}`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

/*
 ***************************
 *   Bulk Operations
 ***************************
 */
export const bulkResendInvitations = (
  uuid: string,
  data: { ids: number[] }
) => {
  return http.request<r>(
    "post",
    `/api/v1/auth/organizations/${uuid}/invitations/bulk/resend`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

export const bulkCancelInvitations = (
  uuid: string,
  data: { ids: number[] }
) => {
  return http.request<r>(
    "post",
    `/api/v1/auth/organizations/${uuid}/invitations/bulk/cancel`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

/*
 ***************************
 *   Soft Delete Operations
 ***************************
 */
export const deleteInvitation = (uuid: string, id: string) => {
  return http.request<r>(
    "delete",
    `/api/v1/auth/organizations/${uuid}/invitations/${id}/delete`
  );
};

export const bulkDeleteInvitations = (
  uuid: string,
  data: { ids: string[] }
) => {
  return http.request<r>(
    "delete",
    `/api/v1/auth/organizations/${uuid}/invitations/bulk/delete`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

/*
 ***************************
 *   Permanent Delete Operations
 ***************************
 */
export const deleteInvitationPermanent = (uuid: string, id: string) => {
  return http.request<r>(
    "delete",
    `/api/v1/auth/organizations/${uuid}/invitations/${id}/force`
  );
};

export const bulkDeleteInvitationsPermanent = (
  uuid: string,
  data: { ids: string[] }
) => {
  return http.request<r>(
    "delete",
    `/api/v1/auth/organizations/${uuid}/invitations/bulk/force`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

/*
 ***************************
 *   Restore Operations
 ***************************
 */
export const restoreInvitation = (uuid: string, id: string) => {
  return http.request<r>(
    "put",
    `/api/v1/auth/organizations/${uuid}/invitations/${id}/restore`
  );
};

export const bulkRestoreInvitations = (
  uuid: string,
  data: { ids: string[] }
) => {
  return http.request<r>(
    "put",
    `/api/v1/auth/organizations/${uuid}/invitations/bulk/restore`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};
