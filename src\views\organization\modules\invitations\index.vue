<script setup lang="ts">
import { ref, nextTick, onMounted, defineAsyncComponent, computed } from "vue";
import { useInvitationHook } from "./utils/hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { columns } from "./utils/columns";
import { deviceDetection } from "@pureadmin/utils";
import { $t } from "@/plugins/i18n";
import PureTable from "@pureadmin/table";
import { IconifyIconOnline } from "@/components/ReIcon";

// Lazy load components
const InvitationDrawerForm = defineAsyncComponent(
  () => import("./components/InvitationDrawerForm.vue")
);

const InvitationFilterForm = defineAsyncComponent(
  () => import("./components/InvitationFilterForm.vue")
);

const tableRef = ref();
const contentRef = ref();

const {
  // Data/State
  loading,
  filterRef,
  pagination,
  records,
  multipleSelection,
  // Event handlers
  fnGetInvitations,
  fnHandlePageChange,
  fnHandleSelectionChange,
  fnHandleSortChange,
  fnHandleSizeChange,
  // Form related
  filterVisible,
  drawerVisible,
  drawerValues,
  invitationFormRef,
  handleSubmit,
  handleFilter,
  handleBulkCancel,
  handleBulkResend,
  handleCancel,
  handleResend
} = useInvitationHook();

onMounted(() => {
  nextTick(() => {
    fnGetInvitations();
  });
});
</script>

<template>
  <div class="main">
    <div
      ref="contentRef"
      :class="['flex', deviceDetection() ? 'flex-wrap' : '']"
    >
      <PureTableBar
        class="!w-full"
        style="transition: width 220ms cubic-bezier(0.4, 0, 0.2, 1)"
        :title="$t('Invitation Management')"
        :columns="columns"
        @refresh="fnGetInvitations"
        @filter="filterVisible = true"
      >
        <template #buttons>
          <el-tooltip :content="$t('Bulk Resend')" placement="top">
            <el-button
              type="text"
              class="font-bold text-[16px]"
              :disabled="multipleSelection.length === 0"
              @click="() => handleBulkResend()"
            >
              <IconifyIconOnline
                icon="tabler:mail-forward"
                width="18px"
                :class="{
                  'text-blue-800': multipleSelection.length > 0
                }"
              />
            </el-button>
          </el-tooltip>

          <el-tooltip :content="$t('Bulk Cancel')" placement="top">
            <el-button
              type="text"
              class="font-bold text-[16px]"
              :disabled="multipleSelection.length === 0"
              @click="() => handleBulkCancel()"
            >
              <IconifyIconOnline
                icon="tabler:mail-x"
                width="18px"
                :class="{
                  'text-red-800': multipleSelection.length > 0
                }"
              />
            </el-button>
          </el-tooltip>
        </template>

        <template v-slot="{ size, dynamicColumns }">
          <PureTable
            ref="tableRef"
            align-whole="center"
            table-layout="auto"
            :loading="loading"
            :size="size"
            adaptive
            :adaptiveConfig="{ offsetBottom: 108 }"
            :data="records"
            :columns="dynamicColumns"
            :pagination="pagination"
            :header-cell-style="{
              background: 'var(--el-fill-color-light)',
              color: 'var(--el-text-color-primary)'
            }"
            @sort-change="fnHandleSortChange"
            @page-size-change="fnHandleSizeChange"
            @page-current-change="fnHandlePageChange"
            @selection-change="fnHandleSelectionChange"
          >
            <template #operation="{ row }">
              <el-dropdown
                split-button
                trigger="click"
                size="small"
                style="min-width: 110px"
              >
                {{ $t("Action") }}
                <template #dropdown>
                  <el-dropdown-menu class="min-w-[130px]">
                    <el-dropdown-item disabled>
                      {{ $t("Action") }}
                    </el-dropdown-item>
                    <el-dropdown-item
                      v-if="row.status === 'pending'"
                      @click="handleResend(row)"
                    >
                      <IconifyIconOnline
                        icon="tabler:mail-forward"
                        class="text-blue-800"
                      />
                      <span class="ml-2">
                        {{ $t("Resend") }}
                      </span>
                    </el-dropdown-item>
                    <el-dropdown-item
                      v-if="row.status === 'pending'"
                      @click="handleCancel(row)"
                    >
                      <IconifyIconOnline
                        icon="tabler:mail-x"
                        class="text-red-800"
                      />
                      <span class="ml-2">
                        {{ $t("Cancel") }}
                      </span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </PureTable>
        </template>
      </PureTableBar>
    </div>

    <InvitationDrawerForm
      ref="invitationFormRef"
      v-model:visible="drawerVisible"
      v-model:values="drawerValues"
      @submit="handleSubmit"
      @close="
        () => {
          invitationFormRef?.resetForm();
          drawerValues = {
            status: 'pending'
          };
        }
      "
    />

    <InvitationFilterForm
      ref="filterFormRef"
      v-model:visible="filterVisible"
      v-model:values="filterRef"
      @submit="handleFilter"
    />
  </div>
</template>
