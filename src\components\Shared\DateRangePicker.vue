<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { $t } from "@/plugins/i18n";
import dayjs from "dayjs";

interface Props {
  modelValue?: [string, string] | null;
  placeholder?: string;
  format?: string;
  valueFormat?: string;
  shortcuts?: boolean;
  clearable?: boolean;
  disabled?: boolean;
  size?: "large" | "default" | "small";
}

interface Emits {
  (e: "update:modelValue", value: [string, string] | null): void;
  (e: "change", value: [string, string] | null): void;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: "",
  format: "YYYY-MM-DD",
  valueFormat: "YYYY-MM-DD",
  shortcuts: true,
  clearable: true,
  disabled: false,
  size: "default"
});

const emit = defineEmits<Emits>();

const dateRange = ref<[Date, Date] | null>(null);

// Convert string dates to Date objects for el-date-picker
const internalValue = computed({
  get() {
    if (!props.modelValue) return null;
    return [new Date(props.modelValue[0]), new Date(props.modelValue[1])] as [
      Date,
      Date
    ];
  },
  set(value: [Date, Date] | null) {
    if (!value) {
      emit("update:modelValue", null);
      emit("change", null);
      return;
    }

    const formattedValue: [string, string] = [
      dayjs(value[0]).format(props.valueFormat),
      dayjs(value[1]).format(props.valueFormat)
    ];

    emit("update:modelValue", formattedValue);
    emit("change", formattedValue);
  }
});

// Predefined shortcuts
const finalShortcuts = computed(() => [
  {
    text: $t("Today"),
    value: () => {
      const today = new Date();
      return [today, today];
    }
  },
  {
    text: $t("Yesterday"),
    value: () => {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      return [yesterday, yesterday];
    }
  },
  {
    text: $t("Last 7 days"),
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setDate(start.getDate() - 6);
      return [start, end];
    }
  },
  {
    text: $t("Last 30 days"),
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setDate(start.getDate() - 29);
      return [start, end];
    }
  },
  {
    text: $t("This month"),
    value: () => {
      const start = new Date();
      start.setDate(1);
      const end = new Date();
      end.setMonth(end.getMonth() + 1);
      end.setDate(0);
      return [start, end];
    }
  },
  {
    text: $t("Last month"),
    value: () => {
      const start = new Date();
      start.setMonth(start.getMonth() - 1);
      start.setDate(1);
      const end = new Date();
      end.setDate(0);
      return [start, end];
    }
  }
]);

const placeholderText = computed(() => {
  if (props.placeholder) return props.placeholder;
  return $t("Select date range");
});
</script>

<template>
  <el-date-picker
    v-model="internalValue"
    type="daterange"
    :range-separator="$t('to')"
    :start-placeholder="$t('Start date')"
    :end-placeholder="$t('End date')"
    :placeholder="placeholderText"
    :format="format"
    :value-format="valueFormat"
    :shortcuts="finalShortcuts ? finalShortcuts : undefined"
    :clearable="clearable"
    :disabled="disabled"
    :size="size"
    class="date-range-picker"
  />
</template>

<style scoped>
.date-range-picker {
  width: 100%;
}

:deep(.el-input__wrapper) {
  transition: all 0.3s ease;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px var(--el-color-primary) inset;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px var(--el-color-primary) inset;
}
</style>
