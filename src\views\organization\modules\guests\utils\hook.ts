import { reactive, ref, computed } from "vue";
import { useRoute } from "vue-router";
import { $t } from "@/plugins/i18n";
import type { FieldValues } from "plus-pro-components";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";

import { useConvertKeyToCamel, useConvertKeyToSnake } from "@/utils/helpers";
import type { FormItemProps, UserFilterProps } from "./type";
import {
  getGuests,
  createGuest,
  deleteGuestPermanent,
  bulkDeleteGuestsPermanent
} from "./auth-api";

export function useGuestHook() {
  const route = useRoute();
  const organizationUuid = computed(() => route.params.id as string);

  const loading = ref(false);
  const filterRef = ref<UserFilterProps>({ isTrashed: "no" });
  const pagination = reactive({
    total: 0,
    pageSize: 20,
    currentPage: 1,
    pageSizes: [10, 20, 50, 100, 200],
    background: true,
    layout: "total, sizes, prev, pager, next, jumper"
  });
  const multipleSelection = ref([]);
  const records = ref([]);
  const sort = ref({ sortBy: "createdAt", sortOrder: "asc" });

  // Form refs
  const guestFormRef = ref();
  const filterVisible = ref(false);
  const drawerVisible = ref(false);
  const drawerValues = ref<FormItemProps>({
    status: "active"
  });

  /* ***************************
   * API Data Fetching
   *************************** */

  const fnGetGuests = async () => {
    try {
      loading.value = true;
      const res = await getGuests(
        organizationUuid.value,
        useConvertKeyToSnake({
          ...filterRef.value,
          order: `${useConvertKeyToSnake(sort.value.sortBy)}:${sort.value.sortOrder}`,
          page: pagination.currentPage,
          limit: pagination.pageSize
        })
      );
      records.value = useConvertKeyToCamel(res.data);
      pagination.total = res.total;
    } catch (error) {
      console.error("Error fetching guests:", error);
      message($t("Failed to fetch guests"), { type: "error" });
    } finally {
      loading.value = false;
    }
  };

  // Table Event Handlers

  const fnHandleSelectionChange = (val: any[]) => {
    multipleSelection.value = val;
  };

  const fnHandlePageChange = () => {
    fnGetGuests();
  };

  const fnHandleSizeChange = (val: number) => {
    pagination.pageSize = val;
    pagination.currentPage = 1;
    fnGetGuests();
  };

  const fnHandleSortChange = async (sortBy: string, sortOrder: string) => {
    sort.value = { sortBy, sortOrder };
    await fnGetGuests();
  };

  const fnHandleCreateGuest = async (data: any) => {
    try {
      loading.value = true;
      const response = await createGuest(organizationUuid.value, data);
      if (response.success) {
        message(response.message || $t("Create successful"), {
          type: "success"
        });
        await fnGetGuests();
        drawerVisible.value = false;
        return true;
      }
      message(response.message || $t("Create failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Create failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  // Delete handlers
  const handlePermanentDelete = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t(
          "Are you sure you want to permanently delete this item? This action cannot be undone."
        ),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      await deleteGuestPermanent(organizationUuid.value, row.uuid);
      message($t("Permanently deleted successfully"), { type: "success" });
      await fnGetGuests();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error permanently deleting user:", error);
        message($t("Permanent delete failed"), { type: "error" });
      }
    }
  };

  const handleBulkPermanentDelete = async () => {
    const selectedIds = multipleSelection.value?.map(item => item.uuid) || [];
    if (selectedIds.length === 0) {
      message($t("Please select items to delete"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t(
          "Are you sure you want to permanently delete selected items? This action cannot be undone."
        ),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      await bulkDeleteGuestsPermanent(organizationUuid.value, {
        ids: selectedIds
      });
      message($t("Permanently deleted successfully"), { type: "success" });
      await fnGetGuests();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error bulk permanently deleting users:", error);
        message($t("Permanent delete failed"), { type: "error" });
      }
    }
  };

  // Form handlers
  const handleFilter = async (values: UserFilterProps) => {
    filterRef.value = values;
    await fnGetGuests();
  };

  const handleSubmit = async (values: FieldValues) => {
    const success = await fnHandleCreateGuest(values);
    if (success) {
      drawerValues.value = { status: "active" };
      guestFormRef.value?.resetForm();
    }
  };

  return {
    loading,
    filterRef,
    pagination,
    records,
    multipleSelection,
    sort,
    handlePermanentDelete,
    handleBulkPermanentDelete,
    fnGetGuests,
    fnHandlePageChange,
    fnHandleSelectionChange,
    fnHandleSortChange,
    fnHandleSizeChange,
    filterVisible,
    drawerVisible,
    drawerValues,
    guestFormRef,
    handleSubmit,
    handleFilter
  };
}
