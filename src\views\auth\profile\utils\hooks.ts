import { useRouter } from "vue-router";

export function useProfileTab() {
  const router = useRouter();

  /**
   * Navigate to profile page - tab sẽ được tự động tạo bởi hệ thống
   * <PERSON> nguyên tắc của tabs system, <PERSON><PERSON><PERSON><PERSON> c<PERSON><PERSON> manually push tab
   */
  function goToProfile() {
    router.push({ name: "ProfileIndex" });
  }

  /**
   * Navigate to current user profile
   */
  function pushMyProfile() {
    goToProfile();
  }

  /**
   * Navigate to other user profile with query params
   * @param userId - User ID to view profile
   */
  function pushUserProfile(userId: string | number) {
    router.push({
      name: "ProfileIndex",
      query: { userId: userId.toString() }
    });
  }

  return {
    goToProfile,
    pushMyProfile,
    pushUserProfile
  };
}
