<script setup lang="ts">
import { ref, onMounted, defineAsyncComponent, nextTick, computed } from "vue";
import { useRoute } from "vue-router";
import { useBotHook } from "./utils/hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { columns } from "./utils/columns";
import { clone, deviceDetection } from "@pureadmin/utils";
import { $t } from "@/plugins/i18n";
import PureTable from "@pureadmin/table";
import { hasAuth } from "@/router/utils";
import { IconifyIconOnline } from "@/components/ReIcon";
import { PlusDialog } from "plus-pro-components";

// Props definition
const props = defineProps<{
  userRole: string; // 'admin' | 'owner' | 'member' | 'guest'
}>();

const route = useRoute();

// Role-based permissions
const canCreateBot = computed(() => {
  return ["admin", "owner"].includes(props.userRole);
});

const canEditBot = computed(() => {
  return ["admin", "owner"].includes(props.userRole);
});

const canDeleteBot = computed(() => {
  return ["admin", "owner"].includes(props.userRole);
});

const canBulkAction = computed(() => {
  return ["admin", "owner"].includes(props.userRole);
});

const canUseBot = computed(() => {
  return ["admin", "owner", "member"].includes(props.userRole);
});

const canViewBot = computed(() => {
  return true; // All roles can view bots
});

const BotForm = defineAsyncComponent(() => import("./components/BotForm.vue"));

const BotFilterForm = defineAsyncComponent(
  () => import("./components/BotFilterForm.vue")
);

const BotUserAssignment = defineAsyncComponent(
  () => import("./components/BotUserAssignment.vue")
);

// Refs for form components
const tableRef = ref();
const contentRef = ref();
const dialogFormRef = ref();

// Dialog states
const userAssignmentVisible = ref(false);
const selectedBot = ref(null);

const {
  // Data/State
  loading,
  filterRef,
  pagination,
  records,
  multipleSelection,
  // Event handlers
  handleBulkDelete,
  handleDelete,
  fnGetBots,
  fnHandlePageChange,
  fnHandleSelectionChange,
  fnHandleSortChange,
  fnHandleSizeChange,
  // Form related
  filterVisible,
  drawerVisible,
  drawerValues,
  botFormRef,
  handleSubmit,
  handleFilter,
  handleBulkPermanentDelete,
  handleBulkRestore,
  handlePermanentDelete,
  handleRestore,
  handleReset
} = useBotHook();

const handleEdit = (row: any) => {
  drawerValues.value = {
    ...clone(row, true),
    logoUrl: row.logoUrl || row.logo || null, // Set logoUrl from existing logo field
    model: row.aiModel.key,
    knowledge: {
      enabled: row.knowledge?.enabled ?? true,
      text: row.knowledge?.text ?? "",
      newUploads: [],
      libraryFiles: row.knowledge?.libraryFiles ?? [],
      botFiles: row.knowledge?.botFiles ?? []
    }
  };
  drawerVisible.value = true;
};

const handleUseBot = (row: any) => {
  selectedBot.value = row;
  userAssignmentVisible.value = true;
};

onMounted(() => {
  nextTick(() => {
    fnGetBots();
  });
});
</script>

<template>
  <div class="main">
    <div
      ref="contentRef"
      :class="['flex', deviceDetection() ? 'flex-wrap' : '']"
    >
      <PureTableBar
        class="!w-full"
        style="transition: width 220ms cubic-bezier(0.4, 0, 0.2, 1)"
        :title="$t('Bot Management')"
        :columns="columns"
        @refresh="fnGetBots"
        @filter="filterVisible = true"
      >
        <template #buttons>
          <el-tooltip :content="$t('Create new')" placement="top">
            <el-button
              type="text"
              class="font-bold text-[16px]"
              :disabled="!canCreateBot"
              @click="
                () => {
                  handleReset();
                  drawerVisible = true;
                }
              "
            >
              <IconifyIconOnline
                :icon="canCreateBot ? 'flat-color-icons:plus' : 'icons8:plus'"
                width="18px"
              />
            </el-button>
          </el-tooltip>
          <template v-if="filterRef.isTrashed === 'yes'">
            <el-tooltip :content="$t('Restore')" placement="top">
              <el-button
                type="text"
                class="font-bold text-[16px]"
                :disabled="
                  multipleSelection.length === 0 ||
                  (multipleSelection.length > 0 && !canBulkAction)
                "
                @click="() => handleBulkRestore()"
              >
                <IconifyIconOnline
                  icon="tabler:restore"
                  width="18px"
                  :class="{
                    'text-amber-800':
                      multipleSelection.length > 0 && canBulkAction
                  }"
                />
              </el-button>
            </el-tooltip>
            <el-tooltip :content="$t('Bulk Destroy')" placement="top">
              <el-button
                type="text"
                class="font-bold text-[16px]"
                :disabled="
                  multipleSelection.length === 0 ||
                  (multipleSelection.length > 0 && !canBulkAction)
                "
                @click="() => handleBulkPermanentDelete()"
              >
                <IconifyIconOnline
                  icon="tabler:trash-x-filled"
                  width="18px"
                  :class="{
                    'text-red-800':
                      multipleSelection.length > 0 && canBulkAction
                  }"
                />
              </el-button>
            </el-tooltip>
          </template>
          <template v-else>
            <el-tooltip
              v-if="filterRef.isTrashed === 'no'"
              :content="$t('Bulk Delete')"
              placement="top"
            >
              <el-button
                type="text"
                class="font-bold text-[16px]"
                :disabled="
                  multipleSelection.length == 0 ||
                  (multipleSelection.length > 0 && !canBulkAction)
                "
                @click="() => handleBulkDelete()"
              >
                <IconifyIconOnline
                  icon="tabler:trash"
                  width="18px"
                  :class="{
                    'text-red-800':
                      multipleSelection.length > 0 && canBulkAction
                  }"
                />
              </el-button>
            </el-tooltip>
          </template>
        </template>
        <template v-slot="{ size, dynamicColumns }">
          <PureTable
            ref="tableRef"
            align-whole="center"
            table-layout="auto"
            :loading="loading"
            :size="size"
            adaptive
            :adaptiveConfig="{ offsetBottom: 108 }"
            :data="records"
            :columns="dynamicColumns"
            :pagination="pagination"
            :header-cell-style="{
              background: 'var(--el-fill-color-light)',
              color: 'var(--el-text-color-primary)'
            }"
            @sort-change="fnHandleSortChange"
            @page-size-change="fnHandleSizeChange"
            @page-current-change="fnHandlePageChange"
            @selection-change="fnHandleSelectionChange"
          >
            <template #operation="{ row }">
              <el-dropdown
                split-button
                trigger="click"
                size="small"
                style="min-width: 110px"
              >
                {{ $t("Action") }}
                <template #dropdown>
                  <el-dropdown-menu class="min-w-[130px]">
                    <el-dropdown-item disabled>
                      {{ $t("Utilities") }}
                    </el-dropdown-item>
                    <el-dropdown-item
                      divided
                      :disabled="!canUseBot"
                      @click="() => handleUseBot(row)"
                    >
                      <IconifyIconOnline
                        icon="mdi:cogs"
                        class="text-green-700"
                      />
                      <span class="ml-2">{{ $t("Use") }}</span>
                    </el-dropdown-item>

                    <el-dropdown-item disabled>
                      {{ $t("Action") }}
                    </el-dropdown-item>
                    <template v-if="filterRef.isTrashed == 'no'">
                      <el-dropdown-item
                        :disabled="!canEditBot"
                        @click="handleEdit(row)"
                      >
                        <IconifyIconOnline
                          icon="material-symbols:edit"
                          class="text-blue-800"
                        />
                        <span class="ml-2">
                          {{ $t("Edit") }}
                        </span>
                      </el-dropdown-item>
                      <el-dropdown-item
                        :disabled="!canDeleteBot"
                        @click="handleDelete(row)"
                      >
                        <IconifyIconOnline
                          icon="tabler:trash"
                          class="text-red-800"
                        />
                        <span class="ml-2">
                          {{ $t("Delete") }}
                        </span>
                      </el-dropdown-item>
                    </template>
                    <template v-else>
                      <el-dropdown-item
                        :disabled="!canDeleteBot"
                        @click="handleRestore(row)"
                      >
                        <IconifyIconOnline
                          icon="tabler:restore"
                          class="text-amber-800"
                        />
                        <span class="ml-2">
                          {{ $t("Restore") }}
                        </span>
                      </el-dropdown-item>
                      <el-dropdown-item
                        :disabled="!canDeleteBot"
                        @click="handlePermanentDelete(row)"
                      >
                        <IconifyIconOnline
                          icon="tabler:trash-x"
                          class="text-red-800"
                        />
                        <span class="ml-2">
                          {{ $t("Destroy") }}
                        </span>
                      </el-dropdown-item>
                    </template>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </PureTable>
        </template>
      </PureTableBar>
    </div>
    <BotFilterForm
      ref="filterFormRef"
      v-model:visible="filterVisible"
      v-model:values="filterRef"
      @submit="handleFilter"
      @reset="
        () => {
          filterRef = { isTrashed: 'no' };
          fnGetBots();
        }
      "
    />
    <PlusDialog
      ref="dialogFormRef"
      v-model="drawerVisible"
      width="90%"
      top="10px"
      :title="$t('Bot Management')"
      :has-footer="false"
      :closeOnClickModal="true"
      :closeOnPressEscape="true"
      :draggable="false"
      :destroyOnClose="true"
      @close="handleReset"
    >
      <BotForm
        ref="botFormRef"
        :useBot="{ drawerValues, handleSubmit, handleReset }"
      />
    </PlusDialog>

    <!-- Bot User Assignment Dialog -->
    <el-dialog
      v-model="userAssignmentVisible"
      :title="$t('Bot User Assignment')"
      width="80%"
      :close-on-click-modal="false"
      :close-on-press-escape="true"
      destroy-on-close
    >
      <BotUserAssignment
        v-if="selectedBot && userAssignmentVisible"
        :bot-id="selectedBot.uuid || selectedBot.id"
        :organization-uuid="route.params.id as string"
      />
    </el-dialog>
  </div>
</template>
