{
  "Reload": "Reload",
  "Close Current Tab": "Close Current Tab",
  "Close Left Tabs": "Close Left Tabs",
  "Close Right Tabs": "Close Right Tabs",
  "Close Other Tabs": "Close Other Tabs",
  "Close All Tabs": "Close All Tabs",
  "Content Fullscreen": "Content Fullscreen",
  "Email is required": "Email is required",
  "Invalid email format": "Invalid email format",
  "Username is required": "Username is required",
  "Username too short (min {count})": "Username too short (min {count})",
  "Username too long (max {count})": "Username too long (max {count})",
  "Invalid username format": "Invalid username format",
  "Password is required": "Password is required",
  "Password too short (min {count})": "Password too short (min {count})",
  "Password needs numbers": "Password needs numbers",
  "Password needs symbols": "Password needs symbols",
  "Password needs uppercase": "Password needs uppercase",
  "Password needs lowercase": "Password needs lowercase",
  "Confirm password required": "Confirm password required",
  "Passwords don't match": "Passwords do not match",
  "OTP required": "OTP required",
  "OTP must be numbers only": "OTP must be numbers only",
  "OTP must be {count} digits": "OTP must be {count} digits",
  "{field} is required": "{field} is required",
  "{field} too short (min {count})": "{field} too short (min {count})",
  "{field} too long (max {count})": "{field} too long (max {count})",
  "{field} invalid format": "{field} invalid format",
  "Accept terms required": "Accept terms required",
  "Username/email required": "Username/email required",
  "Verification code required": "Verification code required",
  "Verification code incorrect": "Verification code incorrect",
  "Login": "Login",
  "Register": "Register",
  "Password": "Password",
  "Email": "Email",
  "Username": "Username",
  "Username/Email": "Username/Email",
  "VerifyCode": "Verify Code",
  "Confirm Password": "Confirm Password",
  "Last Name": "Last Name",
  "OTP Code": "OTP Code",
  "Verification Code": "Verification Code",
  "Create Account": "Create Account",
  "I accept the": "I accept the",
  "Terms and Conditions": "Terms and Conditions",
  "Phone Number": "Phone Number",
  "Birthday": "Birthday",
  "Gender": "Gender",
  "Address": "Address",
  "Save Changes": "Save Changes",
  "Update Password": "Update Password",
  "Appearance Settings": "Appearance Settings",
  "Dark Mode": "Dark Mode",
  "Enable/disable dark theme": "Enable/disable dark theme",
  "Select display language": "Select display language",
  "Social Media": "Social Media",
  "Change": "Change",
  "Forgot Password": "Forgot Password",
  "Forgot password?": "Forgot password?",
  "Send OTP Code": "Send OTP Code",
  "Reset Password": "Reset Password",
  "Verify Email": "Verify Email",
  "Change Email": "Change Email",
  "Back to Login": "Back to Login",
  "Sign In": "Sign In",
  "Sign Up": "Sign Up",
  "Enter your email address and we'll send you an OTP code to reset your password": "Enter your email address and we'll send you an OTP code to reset your password",
  "Enter the OTP code sent to": "Enter the OTP code sent to",
  "We've sent a verification code to": "We've sent a verification code to",
  "Didn't receive the code?": "Didn't receive the code?",
  "Resend OTP": "Resend OTP",
  "Resend Code": "Resend Code",
  "Resend in {seconds}s": "Resend in {seconds}s",
  "Or continue with": "Or continue with",
  "Continue with {provider}": "Continue with {provider}",
  "Please enter email": "Please enter email",
  "Please enter the password": "Please enter the password",
  "Use 8-18 chars with uppercase, lowercase, numbers & symbols.": "Use 8-18 chars with uppercase, lowercase, numbers & symbols.",
  "Router Error": "Router Error",
  "Some information is incorrect. Please review and try again.": "Some information is incorrect. Please review and try again.",
  "Social login failed": "Social login failed",
  "Language Management": "Language Management",
  "Translation Management": "Translation Management",
  "Page Management": "Page Management",
  "Selection": "Selection",
  "ID": "ID",
  "Name": "Name",
  "Code": "Code",
  "Language Code": "Language Code",
  "Language Name": "Language Name",
  "Native Name": "Native Name",
  "Direction": "Direction",
  "Flag": "Flag",
  "Sort Order": "Sort Order",
  "Is Active": "Is Active",
  "Is Default": "Is Default",
  "Default": "Default",
  "Active": "Active",
  "Inactive": "Inactive",
  "Yes": "Yes",
  "No": "No",
  "All": "All",
  "Left to Right": "Left to Right",
  "Right to Left": "Right to Left",
  "Status": "Status",
  "Trashed": "Trashed",
  "Created At": "Created At",
  "Operations": "Operations",
  "Translation Key": "Translation Key",
  "Translation Value": "Translation Value",
  "Group": "Group",
  "Plural": "Plural",
  "Is Plural": "Is Plural",
  "Description": "Description",
  "Title": "Title",
  "Page Title": "Page Title",
  "Page Slug": "Page Slug",
  "Page Content": "Page Content",
  "Excerpt": "Excerpt",
  "Featured Image": "Featured Image",
  "Categories": "Categories",
  "Tags": "Tags",
  "Published At": "Published At",
  "Is Sticky": "Is Sticky",
  "Allow Comments": "Allow Comments",
  "Sticky": "Sticky",
  "Author": "Author",
  "Visibility": "Visibility",
  "Draft": "Draft",
  "Published": "Published",
  "Pending": "Pending",
  "Private": "Private",
  "Public": "Public",
  "Password Protected": "Password Protected",
  "Default Template": "Default Template",
  "Please enter the language code": "Please enter the language code",
  "Please enter the language name": "Please enter the language name",
  "Please enter the native name": "Please enter the native name",
  "Please enter the translation key": "Please enter the translation key",
  "Please enter the translation value": "Please enter the translation value",
  "Please select the language": "Please select the language",
  "Please input language code": "Please input language code",
  "Please input language name": "Please input language name",
  "Please input native name": "Please input native name",
  "Please input translation key": "Please input translation key",
  "Please input translation value": "Please input translation value",
  "Please select language": "Please select language",
  "Please input page title": "Please input page title",
  "Please input page slug": "Please input page slug",
  "Please input page content": "Please input page content",
  "Please select the page status": "Please select the page status",
  "Please select the page visibility": "Please select the page visibility",
  "Length must be between 2 and 10 characters": "Length must be between 2 and 10 characters",
  "Length must be between 2 and 100 characters": "Length must be between 2 and 100 characters",
  "Length must be between 2 and 255 characters": "Length must be between 2 and 255 characters",
  "Translation value cannot exceed 5000 characters": "Translation value cannot exceed 5000 characters",
  "Key can only contain letters, numbers, dots, underscores and hyphens": "Key can only contain letters, numbers, dots, underscores and hyphens",
  "Group name cannot exceed 100 characters": "Group name cannot exceed 100 characters",
  "Slug can only contain lowercase letters, numbers and hyphens": "Slug can only contain lowercase letters, numbers and hyphens",
  "Meta title should not exceed 60 characters": "Meta title should not exceed 60 characters",
  "Meta description should not exceed 160 characters": "Meta description should not exceed 160 characters",
  "Meta keywords should not exceed 255 characters": "Meta keywords should not exceed 255 characters",
  "e.g., en, vi, fr": "e.g., en, vi, fr",
  "e.g., English, Vietnamese": "e.g., English, Vietnamese",
  "e.g., English, Tiếng Việt": "e.g., English, Tiếng Việt",
  "e.g., 🇺🇸, 🇻🇳": "e.g., 🇺🇸, 🇻🇳",
  "e.g., user.name, common.save": "e.g., user.name, common.save",
  "Select text direction": "Select text direction",
  "Select status": "Select status",
  "Select active status": "Select active status",
  "Select default status": "Select default status",
  "Select trashed status": "Select trashed status",
  "Select plural status": "Select plural status",
  "Select page status": "Select page status",
  "Select page visibility": "Select page visibility",
  "Select page template": "Select page template",
  "Select categories": "Select categories",
  "Select tags": "Select tags",
  "Select publish date": "Select publish date",
  "Select template": "Select template",
  "Select category": "Select category",
  "Select tag": "Select tag",
  "Select sticky status": "Select sticky status",
  "Select comment status": "Select comment status",
  "Select visibility": "Select visibility",
  "Select or enter group": "Select or enter group",
  "Select group": "Select group",
  "Enter language code": "Enter language code",
  "Enter language name": "Enter language name",
  "Enter native name": "Enter native name",
  "Enter translation key": "Enter translation key",
  "Enter translation value": "Enter translation value",
  "Enter description (optional)": "Enter description (optional)",
  "Enter page title": "Enter page title",
  "Enter page slug": "Enter page slug",
  "Enter page slug (auto-generated from title)": "Enter page slug (auto-generated from title)",
  "Enter page content": "Enter page content",
  "Enter page excerpt (optional)": "Enter page excerpt (optional)",
  "Enter image URL or upload": "Enter image URL or upload",
  "Add Language": "Add Language",
  "Edit Language": "Edit Language",
  "Filter Languages": "Filter Languages",
  "Add Translation": "Add Translation",
  "Edit Translation": "Edit Translation",
  "Filter Translations": "Filter Translations",
  "Add Page": "Add Page",
  "Edit Page": "Edit Page",
  "Filter Pages": "Filter Pages",
  "Published Date Range": "Published Date Range",
  "To": "To",
  "Start date": "Start date",
  "End date": "End date",
  "Create new": "Create new",
  "Action": "Action",
  "Edit": "Edit",
  "Destroy": "Destroy",
  "Restore": "Restore",
  "Force Delete": "Force Delete",
  "Bulk Delete": "Bulk Delete",
  "Bulk Destroy": "Bulk Destroy",
  "Reset": "Reset",
  "Search": "Search",
  "Import": "Import",
  "Export": "Export",
  "Sync": "Sync",
  "Publish": "Publish",
  "Unpublish": "Unpublish",
  "Duplicate": "Duplicate",
  "Bulk Publish": "Bulk Publish",
  "Bulk Unpublish": "Bulk Unpublish",
  "Import Translations": "Import Translations",
  "Download Template": "Download Template",
  "Sync English": "Sync English",
  "Sync Vietnamese": "Sync Vietnamese",
  "Sync All Languages": "Sync All Languages",
  "Please follow these steps to import translations:": "Please follow these steps to import translations:",
  "Download the template file": "Download the template file",
  "Fill in your translation data": "Fill in your translation data",
  "Upload the completed file": "Upload the completed file",
  "Drop file here or": "Drop file here or",
  "click to upload": "click to upload",
  "Only Excel files (.xlsx, .xls) are supported, max size 10MB": "Only Excel files (.xlsx, .xls) are supported, max size 10MB",
  "Please upload Excel file (.xlsx or .xls)": "Please upload Excel file (.xlsx or .xls)",
  "File size cannot exceed 10MB": "File size cannot exceed 10MB",
  "Are you sure you want to delete this language?": "Are you sure you want to delete this language?",
  "Are you sure you want to delete selected languages?": "Are you sure you want to delete selected languages?",
  "Are you sure you want to permanently delete this language?": "Are you sure you want to permanently delete this language?",
  "Are you sure you want to permanently delete selected languages?": "Are you sure you want to permanently delete selected languages?",
  "Are you sure you want to restore this language?": "Are you sure you want to restore this language?",
  "Are you sure you want to restore selected languages?": "Are you sure you want to restore selected languages?",
  "Are you sure you want to delete this translation?": "Are you sure you want to delete this translation?",
  "Are you sure you want to delete selected translations?": "Are you sure you want to delete selected translations?",
  "Are you sure you want to permanently delete this translation?": "Are you sure you want to permanently delete this translation?",
  "Are you sure you want to permanently delete selected translations?": "Are you sure you want to permanently delete selected translations?",
  "Are you sure you want to restore this translation?": "Are you sure you want to restore this translation?",
  "Are you sure you want to restore selected translations?": "Are you sure you want to restore selected translations?",
  "Are you sure you want to delete this page?": "Are you sure you want to delete this page?",
  "Are you sure you want to delete selected pages?": "Are you sure you want to delete selected pages?",
  "Are you sure you want to permanently delete this page?": "Are you sure you want to permanently delete this page?",
  "Are you sure you want to permanently delete selected pages?": "Are you sure you want to permanently delete selected pages?",
  "Are you sure you want to restore this page?": "Are you sure you want to restore this page?",
  "Are you sure you want to restore selected pages?": "Are you sure you want to restore selected pages?",
  "Warning": "Warning",
  "Confirm": "Confirm",
  "OK": "OK",
  "Cancel": "Cancel",
  "Get failed": "Get failed",
  "Create successful": "Create successful",
  "Update successful": "Update successful",
  "Delete successful": "Delete successful",
  "Destroy successful": "Destroy successful",
  "Restore successful": "Restore successful",
  "Operation failed": "Operation failed",
  "Delete failed": "Delete failed",
  "Destroy failed": "Destroy failed",
  "Restore failed": "Restore failed",
  "Sync successful": "Sync successful",
  "Sync failed": "Sync failed",
  "Export successful": "Export successful",
  "Export failed": "Export failed",
  "Import successful": "Import successful",
  "Import failed": "Import failed",
  "Publish successful": "Publish successful",
  "Publish failed": "Publish failed",
  "Unpublish successful": "Unpublish successful",
  "Unpublish failed": "Unpublish failed",
  "Bulk publish successful": "Bulk publish successful",
  "Bulk publish failed": "Bulk publish failed",
  "Bulk unpublish successful": "Bulk unpublish successful",
  "Bulk unpublish failed": "Bulk unpublish failed",
  "Duplicate successful": "Duplicate successful",
  "Duplicate failed": "Duplicate failed",
  "Upload successful": "Upload successful",
  "Upload failed": "Upload failed",
  "Please select items to delete": "Please select items to delete",
  "Please select items to destroy": "Please select items to destroy",
  "Please select items to restore": "Please select items to restore",
  "Please select items to publish": "Please select items to publish",
  "Please select items to unpublish": "Please select items to unpublish",
  "Bot Management": "Bot Management",
  "My Bots": "My Bots",
  "Bot Store": "Bot Store",
  "Bot": "Bot",
  "Hello! How can I help you?": "Hello! How can I help you?",
  "Please enter AI Assistant Name": "Please enter AI Assistant Name",
  "Length from 3 to 50 characters": "Length from 3 to 50 characters",
  "Please select LLM model": "Please select LLM model",
  "Please enter System Prompt": "Please enter System Prompt",
  "Prompt needs at least 20 characters to be effective": "Prompt needs at least 20 characters to be effective",
  "File \"{fileName}\" is already in the list.": "File \"{fileName}\" is already in the list.",
  "Are you sure you want to clear all current configuration?": "Are you sure you want to clear all current configuration?",
  "Form has been reset.": "Form has been reset.",
  "Briefly describe the role of the Agent:": "Briefly describe the role of the Agent:",
  "✨ Prompt Generator Assistant": "✨ Prompt Generator Assistant",
  "Generate": "Generate",
  "Example: Vietnamese literature lesson planning assistant": "Example: Vietnamese literature lesson planning assistant",
  "Prompt generated successfully!": "Prompt generated successfully!",
  "Please enter AI Assistant Name first.": "Please enter AI Assistant Name first.",
  "Please create System Prompt for best suggestions.": "Please create System Prompt for best suggestions.",
  "AI returned data not in string array format.": "AI returned data not in string array format.",
  "Cannot parse suggestions from AI.": "Cannot parse suggestions from AI.",
  "Please fill in all required fields.": "Please fill in all required fields.",
  "AI Agent Studio": "AI Agent Studio",
  "New Agent": "New Agent",
  "Save & Deploy": "Save & Deploy",
  "Avatar": "Avatar",
  "Upload JPG, PNG, JPEG images. Size under 5MB.": "Upload JPG, PNG, JPEG images. Size under 5MB.",
  "Advanced Settings": "Advanced Settings",
  "Use Knowledge Base": "Use Knowledge Base",
  "Enable this feature to allow Agent access to your private knowledge sources.": "Enable this feature to allow Agent access to your private knowledge sources.",
  "Basic Information": "Basic Information",
  "AI Assistant Name": "AI Assistant Name",
  "Example: Administrative Procedure Consultant": "Example: Administrative Procedure Consultant",
  "Brief description of AI Assistant functions.": "Brief description of AI Assistant functions.",
  "AI Brain": "AI Brain",
  "Suggestion - Prompt": "Suggestion - Prompt",
  "This is the most important part...": "This is the most important part...",
  "Chat Interface": "Chat Interface",
  "Welcome Message": "Welcome Message",
  "Example: Hello! How can I help you?": "Example: Hello! How can I help you?",
  "✨ Generate": "✨ Generate",
  "Starter Suggestions": "Starter Suggestions",
  "Example: What is the tuition fee?": "Example: What is the tuition fee?",
  "Add Suggestion": "Add Suggestion",
  "Preview": "Preview",
  "AI Assistant": "AI Assistant",
  "Knowledge Base": "Knowledge Base",
  "Knowledge Base Management": "Knowledge Base Management",
  "Knowledge Base Name": "Knowledge Base Name",
  "Owner Type": "Owner Type",
  "Owner ID": "Owner ID",
  "Storage Path": "Storage Path",
  "Knowledge Base Information": "Knowledge Base Information",
  "Upload New Files": "Upload New Files",
  "Drag files here or": "Drag files here or",
  "Text": "Text",
  "Paste text content here.": "Paste text content here.",
  "Document Library": "Document Library",
  "Select previously uploaded documents to add to the knowledge for this Agent.": "Select previously uploaded documents to add to the knowledge for this Agent.",
  "File Name": "File Name",
  "Type": "Type",
  "Upload Date": "Upload Date",
  "Add to Bot Knowledge": "Add to Bot Knowledge",
  "Clear Selection": "Clear Selection",
  "In Bot": "In Bot",
  "Add Files": "Add Files",
  "Add Files from Library": "Add Files from Library",
  "Files currently attached to this bot's knowledge base.": "Files currently attached to this bot's knowledge base.",
  "Click 'Add Files' to select files from your library.": "Click 'Add Files' to select files from your library.",
  "Select files from your library to add to this bot's knowledge.": "Select files from your library to add to this bot's knowledge.",
  "Remove this file from bot knowledge?": "Remove this file from bot knowledge?",
  "File removed from bot knowledge": "File removed from bot knowledge",
  "Files added successfully": "Files added successfully",
  "Add Selected Files": "Add Selected Files",
  "Already Added": "Already Added",
  "Selected files are already added to this bot": "Selected files are already added to this bot",
  "Added {count} files to bot knowledge": "Added {count} files to bot knowledge",
  "Manage your personal AI Bots": "Manage your personal AI Bots",
  "Total Bots": "Total Bots",
  "Search Bots...": "Search Bots...",
  "Sort": "Sort",
  "Recently Updated": "Recently Updated",
  "Newest Created": "Newest Created",
  "Name A-Z": "Name A-Z",
  "No Bots Found": "No Bots Found",
  "Try changing filters or create a new Bot": "Try changing filters or create a new Bot",
  "Create First Bot": "Create First Bot",
  "Unknown": "Unknown",
  "Paused": "Paused",
  "Enter name for new Bot:": "Enter name for new Bot:",
  "Duplicate Bot": "Duplicate Bot",
  "Please enter Bot name": "Please enter Bot name",
  "Are you sure you want to delete Bot \"{botName}\"?": "Are you sure you want to delete Bot \"{botName}\"?",
  "activate": "activate",
  "pause": "pause",
  "Are you sure you want to {action} Bot \"{botName}\"?": "Are you sure you want to {action} Bot \"{botName}\"?",
  "Confirm {action}": "Confirm {action}",
  "Pause": "Pause",
  "Length must be between 1 and 120 characters": "Length must be between 1 and 120 characters",
  "Logo": "Logo",
  "Enter logo URL or upload image": "Enter logo URL or upload image",
  "Enter logo URL": "Enter logo URL",
  "or": "or",
  "Please select an AI model": "Please select an AI model",
  "Select AI model": "Select AI model",
  "System Prompt": "System Prompt",
  "Please input system prompt": "Please input system prompt",
  "Enter system prompt that shapes the bot's behavior": "Enter system prompt that shapes the bot's behavior",
  "Greeting Message": "Greeting Message",
  "Enter greeting message": "Enter greeting message",
  "Closing Message": "Closing Message",
  "Enter closing message": "Enter closing message",
  "Tool Calling Mode": "Tool Calling Mode",
  "Please select tool calling mode": "Please select tool calling mode",
  "Select tool calling mode": "Select tool calling mode",
  "Auto": "Auto",
  "None": "None",
  "Required": "Required",
  "Please select visibility": "Please select visibility",
  "Bot Type": "Bot Type",
  "Please select bot type": "Please select bot type",
  "Select bot type": "Select bot type",
  "Personal": "Personal",
  "Review": "Review",
  "Banned": "Banned",
  "Bot Information Form": "Bot Information Form",
  "Submit": "Submit",
  "Search by bot name": "Search by bot name",
  "Filter by AI model": "Filter by AI model",
  "Filter by status": "Filter by status",
  "Filter by visibility": "Filter by visibility",
  "Filter by bot type": "Filter by bot type",
  "Filter by tool calling mode": "Filter by tool calling mode",
  "Filter by trash status": "Filter by trash status",
  "Filter": "Filter",
  "Apply Filter": "Apply Filter",
  "No.": "No.",
  "Tool Mode": "Tool Mode",
  "Operation": "Operation",
  "Please enter the bot name": "Please enter the bot name",
  "Please enter the system prompt": "Please enter the system prompt",
  "Failed to fetch bots": "Failed to fetch bots",
  "Failed to create bot": "Failed to create bot",
  "Failed to update bot": "Failed to update bot",
  "Failed to delete bot": "Failed to delete bot",
  "Bots deleted successfully": "Bots deleted successfully",
  "Failed to delete bots": "Failed to delete bots",
  "Are you sure you want to delete selected bots?": "Are you sure you want to delete selected bots?",
  "Are you sure you want to permanently delete this bot?": "Are you sure you want to permanently delete this bot?",
  "Bot permanently deleted": "Bot permanently deleted",
  "Failed to permanently delete bot": "Failed to permanently delete bot",
  "Are you sure you want to permanently delete selected bots?": "Are you sure you want to permanently delete selected bots?",
  "Bots permanently deleted": "Bots permanently deleted",
  "Failed to permanently delete bots": "Failed to permanently delete bots",
  "Bot restored successfully": "Bot restored successfully",
  "Failed to restore bot": "Failed to restore bot",
  "Bots restored successfully": "Bots restored successfully",
  "Failed to restore bots": "Failed to restore bots",
  "User Profile": "User Profile",
  "Manage your profile and settings": "Manage your profile and settings",
  "Security": "Security",
  "Full Name": "Full Name",
  "Enter your full name": "Enter your full name",
  "Enter your email": "Enter your email",
  "Phone": "Phone",
  "Enter your phone number": "Enter your phone number",
  "Location": "Location",
  "Enter your location": "Enter your location",
  "Company": "Company",
  "Enter your company": "Enter your company",
  "Job Title": "Job Title",
  "Enter your job title": "Enter your job title",
  "Website": "Website",
  "Enter your website URL": "Enter your website URL",
  "Bio": "Bio",
  "Tell us about yourself": "Tell us about yourself",
  "Update Profile": "Update Profile",
  "User ID": "User ID",
  "UUID": "UUID",
  "Email Verified": "Email Verified",
  "Not Verified": "Not Verified",
  "Last Login": "Last Login",
  "Member Since": "Member Since",
  "Roles & Permissions": "Roles & Permissions",
  "No roles assigned": "No roles assigned",
  "No permissions assigned": "No permissions assigned",
  "Preferences": "Preferences",
  "Language & Region": "Language & Region",
  "Timezone": "Timezone",
  "Date Format": "Date Format",
  "Time Format": "Time Format",
  "Theme": "Theme",
  "Light": "Light",
  "Dark": "Dark",
  "Notifications": "Notifications",
  "Email Notifications": "Email Notifications",
  "Receive notifications via email": "Receive notifications via email",
  "Push Notifications": "Push Notifications",
  "Receive push notifications in browser": "Receive push notifications in browser",
  "Two-Factor Authentication": "Two-Factor Authentication",
  "Add an extra layer of security to your account": "Add an extra layer of security to your account",
  "Save Settings": "Save Settings",
  "Enter current password": "Enter current password",
  "Enter new password": "Enter new password",
  "Confirm new password": "Confirm new password",
  "Avatar must be an image!": "Avatar must be an image!",
  "Avatar size must be less than 2MB!": "Avatar size must be less than 2MB!",
  "Avatar updated successfully!": "Avatar updated successfully!",
  "Failed to update avatar": "Failed to update avatar",
  "Failed to upload avatar": "Failed to upload avatar",
  "Profile updated successfully!": "Profile updated successfully!",
  "Failed to update profile": "Failed to update profile",
  "Settings updated successfully!": "Settings updated successfully!",
  "Failed to update settings": "Failed to update settings",
  "Password updated successfully!": "Password updated successfully!",
  "Failed to update password": "Failed to update password",
  "Passwords do not match": "Passwords do not match",
  "Please fill in all password fields": "Please fill in all password fields",
  "Loading user profile...": "Loading user profile...",
  "User not found": "User not found",
  "Go Back": "Go Back",
  "View Profile": "View Profile",
  "Logout": "Logout",
  "My Profile": "My Profile",
  "Open My Profile Tab": "Open My Profile Tab",
  "Open User 123 Profile": "Open User 123 Profile",
  "Open User 456 Profile": "Open User 456 Profile",
  "profile.tabs.profile": "Profile",
  "profile.tabs.account": "Account",
  "profile.tabs.chat": "Chat",
  "profile.tabs.voice": "Voice & Video",
  "profile.tabs.appearance": "Appearance",
  "profile.tabs.notification": "Notification",
  "profile.profilePicture": "Profile Picture",
  "profile.changePicture": "Change Picture",
  "profile.deletePicture": "Delete Picture",
  "profile.profileName": "Profile Name",
  "profile.statusRecently": "Status Recently",
  "profile.aboutMe": "About Me",
  "profile.accountInfo": "Account Information",
  "profile.changePassword": "Change Password",
  "profile.comingSoon": "Coming Soon",
  "Add User": "Add User",
  "Edit User": "Edit User",
  "Please enter name": "Please enter name",
  "Please enter phone number": "Please enter phone number",
  "Please select role": "Please select role",
  "Please enter password": "Please enter password",
  "Please confirm password": "Please confirm password",
  "Name is required": "Name is required",
  "Please enter a valid email": "Please enter a valid email",
  "Role is required": "Role is required",
  "Password must be at least 6 characters": "Password must be at least 6 characters",
  "Password confirmation is required": "Password confirmation is required",
  "User Management": "User Management",
  "Search by name": "Search by name",
  "Search by email": "Search by email",
  "All Status": "All Status",
  "Suspended": "Suspended",
  "Send Password Reset": "Send Password Reset",
  "Send Email Verification": "Send Email Verification",
  "Unban": "Unban",
  "Status is required": "Status is required",
  "Has Avatar": "Has Avatar",
  "No Avatar": "No Avatar",
  "Select avatar status": "Select avatar status",
  "Model Name": "Model Name",
  "Model Key": "Model Key",
  "Provider": "Provider",
  "Input Types": "Input Types",
  "Output Types": "Output Types",
  "Image": "Image",
  "Audio": "Audio",
  "Video": "Video",
  "Document": "Document",
  "Supported Sources": "Supported Sources",
  "AI Model Parameters": "AI Model Parameters",
  "Context Window (Token)": "Context Window (Token)",
  "Rate Limit RPM": "Rate Limit RPM",
  "Max Tokens": "Max Tokens",
  "Timeout Seconds": "Timeout Seconds",
  "Billing Type": "Billing Type",
  "Request": "Request",
  "Token": "Token",
  "Hybrid": "Hybrid",
  "Cost Per Request": "Cost Per Request",
  "Cost Per 1K Tokens": "Cost Per 1K Tokens",
  "Cost Per 1K Input": "Cost Per 1K Input",
  "Cost Per 1K Output": "Cost Per 1K Output",
  "Note": "Note",
  "Information Form": "Information Form",
  "Service": "Service",
  "Please select at least one input type": "Please select at least one input type",
  "Please select at least one output type": "Please select at least one output type",
  "e.g., [{\"type\":\"upload\"}, {\"type\":\"oauth\", \"provider\":\"google_drive\"}]": "e.g., [{\"type\":\"upload\"}, {\"type\":\"oauth\", \"provider\":\"google_drive\"}]",
  "Please input context window": "Please input context window",
  "Please input rate limit RPM": "Please input rate limit RPM",
  "Please input max tokens": "Please input max tokens",
  "Please input timeout seconds": "Please input timeout seconds",
  "Please select billing type": "Please select billing type",
  "Please input cost per request": "Please input cost per request",
  "Please input cost per 1K tokens": "Please input cost per 1K tokens",
  "Please input cost per 1K input": "Please input cost per 1K input",
  "Please input cost per 1K output": "Please input cost per 1K output",
  "Service created successfully": "Service created successfully",
  "Service creation failed": "Service creation failed",
  "Service updated successfully": "Service updated successfully",
  "Service update failed": "Service update failed",
  "Service save failed": "Service save failed",
  "Model key": "Model key",
  "Model name": "Model name",
  "Model Provider": "Model Provider",
  "Model Category": "Model Category",
  "API Endpoint": "API Endpoint",
  "Streaming": "Streaming",
  "Vision": "Vision",
  "Function Calling (Tools)": "Function Calling (Tools)",
  "Set Default": "Set Default",
  "Please input model key": "Please input model key",
  "Please input model name": "Please input model name",
  "Please input API endpoint": "Please input API endpoint",
  "Length should be between 2 and 100 characters": "Length should be between 2 and 100 characters",
  "Key can only contain lowercase letters, numbers, hyphens and underscores": "Key can only contain lowercase letters, numbers, hyphens and underscores",
  "API endpoint must start with /": "API endpoint must start with /",
  "Failed to fetch modelAi": "Failed to fetch model AI",
  "Create failed": "Create failed",
  "Update failed": "Update failed",
  "Deleted successfully": "Deleted successfully",
  "Permanently deleted successfully": "Permanently deleted successfully",
  "Permanent delete failed": "Permanent delete failed",
  "Restored successfully": "Restored successfully",
  "Are you sure you want to delete this item?": "Are you sure you want to delete this item?",
  "Are you sure you want to delete selected items?": "Are you sure you want to delete selected items?",
  "Are you sure you want to permanently delete this item? This action cannot be undone.": "Are you sure you want to permanently delete this item? This action cannot be undone.",
  "Are you sure you want to permanently delete selected items? This action cannot be undone.": "Are you sure you want to permanently delete selected items? This action cannot be undone.",
  "Are you sure you want to restore this item?": "Are you sure you want to restore this item?",
  "Are you sure you want to restore selected items?": "Are you sure you want to restore selected items?",
  "Function Calling": "Function Calling",
  "Profile Information": "Profile Information",
  "Enter email": "Enter email",
  "Select gender": "Select gender",
  "This field is required": "This field is required",
  "Name must be at least 2 characters": "Name must be at least 2 characters",
  "Name must not exceed 50 characters": "Name must not exceed 50 characters",
  "Name can only contain letters, spaces, hyphens, and apostrophes": "Name can only contain letters, spaces, hyphens, and apostrophes",
  "Address must not exceed 255 characters": "Address must not exceed 255 characters",
  "Birthday cannot be in the future": "Birthday cannot be in the future",
  "Please enter a valid birthday": "Please enter a valid birthday",
  "Current password is required": "Current password is required",
  "Avatar must be an image": "Avatar must be an image",
  "Avatar size must be less than 2MB": "Avatar size must be less than 2MB",
  "Avatar updated successfully": "Avatar updated successfully",
  "Back to Dashboard": "Back to Dashboard",
  "Enter message...": "Enter message...",
  "Are you sure you want to delete?": "Are you sure you want to delete?",
  "Currently using": "Currently using",
  "Choose a suggested question below or enter your message to start the conversation.": "Choose a suggested question below or enter your message to start the conversation.",
  "Welcome": "Welcome",
  "Welcome to AI Assistant": "Welcome to AI Assistant",
  "Welcome back": "Welcome back",
  "Welcome to the platform": "Welcome to the platform",
  "Welcome message": "Welcome message",
  "Welcome to our chatbot": "Welcome to our chatbot",
  "Welcome! How can I help you today?": "Welcome! How can I help you today?",
  "Welcome to the dashboard": "Welcome to the dashboard",
  "Loading dashboard data...": "Loading dashboard data...",
  "Analytics Dashboard": "Analytics Dashboard",
  "Chatbot Performance": "Chatbot Performance",
  "Insights": "Insights",
  "Comprehensive analytics and performance metrics for your AI chatbots": "Comprehensive analytics and performance metrics for your AI chatbots",
  "Time Period": "Time Period",
  "Export Report": "Export Report",
  "Active Chatbots": "Active Chatbots",
  "Tokens Used": "Tokens Used",
  "Storage Used": "Storage Used",
  "Documents": "Documents",
  "7 Days": "7 Days",
  "30 Days": "30 Days",
  "3 Months": "3 Months",
  "Document type distribution": "Document type distribution",
  "Most Queried": "Most Queried",
  "Storage Usage": "Storage Usage",
  "Space utilization breakdown": "Space utilization breakdown",
  "today": "today",
  "cost": "cost",
  "Please select an assistant from the left list to start a new conversation.": "Please select an assistant from the left list to start a new conversation.",
  "Last message": "Last message",
  "Sorry, the page you are visiting does not exist": "Sorry, the page you are visiting does not exist",
  "Sorry, you do not have permission to access this page": "Sorry, you do not have permission to access this page",
  "Back Home": "Back Home",
  "Delete": "Delete",
  "Rename": "Rename",
  "Attachment": "Attachment",
  "Collapse": "",
  "Expand": "",
  "Are you sure to delete this item?": "Are you sure to delete this item?",
  "New Conversation": "New Conversation",
  "Organization Management": "Organization Management",
  "View Details": "View Details",
  "Back to Organizations": "Back to Organizations",
  "Verified": "Verified",
  "employees": "employees",
  "Created": "Created",
  "Settings": "Settings",
  "Organization Information": "Organization Information",
  "Recent Activities": "Recent Activities",
  "Organization Settings": "Organization Settings",
  "AI Bots": "AI Bots",
  "Manage AI bots for your organization": "Manage AI bots for your organization",
  "Search bots...": "Search bots...",
  "Model": "Model",
  "Deactivate": "Deactivate",
  "Create New Bot": "Create New Bot",
  "Enter bot name": "Enter bot name",
  "Enter bot description": "Enter bot description",
  "Select model": "Select model",
  "Please fill in all required fields": "Please fill in all required fields",
  "Failed to save bot": "Failed to save bot",
  "Confirm Delete": "Confirm Delete",
  "Bot activated successfully": "Bot activated successfully",
  "Bot deactivated successfully": "Bot deactivated successfully",
  "Failed to update bot status": "Failed to update bot status",
  "Organization Members": "Organization Members",
  "Manage members and their roles in your organization": "Manage members and their roles in your organization",
  "Invite Member": "Invite Member",
  "Search members...": "Search members...",
  "Member": "Member",
  "Joined Date": "Joined Date",
  "Last Active": "Last Active",
  "Edit Role": "Edit Role",
  "Remove": "Remove",
  "Are you sure you want to remove this member from the organization?": "Are you sure you want to remove this member from the organization?",
  "Confirm Remove": "Confirm Remove",
  "Member removed successfully": "Member removed successfully",
  "Member activated successfully": "Member activated successfully",
  "Member deactivated successfully": "Member deactivated successfully",
  "Failed to update member status": "Failed to update member status",
  "Edit Member Role": "Edit Member Role",
  "Role Permissions": "Role Permissions",
  "Full administrative access": "Full administrative access",
  "Manage bots and members": "Manage bots and members",
  "Create and manage own bots": "Create and manage own bots",
  "View only access": "View only access",
  "Member role updated successfully": "Member role updated successfully",
  "Failed to update member role": "Failed to update member role",
  "Organization Invitations": "Organization Invitations",
  "Manage invitations to join your organization": "Manage invitations to join your organization",
  "Send Invitation": "Send Invitation",
  "Search invitations...": "Search invitations...",
  "Invited By": "Invited By",
  "Invited Date": "Invited Date",
  "Expires": "Expires",
  "Resend": "Resend",
  "Joined": "Joined",
  "Declined": "Declined",
  "Expired": "Expired",
  "Email Address": "Email Address",
  "Enter email address": "Enter email address",
  "Personal Message": "Personal Message",
  "Optional personal message to include with the invitation": "Optional personal message to include with the invitation",
  "Invitation Preview": "Invitation Preview",
  "Message": "Message",
  "Please enter email address": "Please enter email address",
  "Invitation sent successfully": "Invitation sent successfully",
  "Failed to send invitation": "Failed to send invitation",
  "Invitation resent successfully": "Invitation resent successfully",
  "Failed to resend invitation": "Failed to resend invitation",
  "Are you sure you want to cancel this invitation?": "Are you sure you want to cancel this invitation?",
  "Confirm Cancel": "Confirm Cancel",
  "Invitation cancelled successfully": "Invitation cancelled successfully",
  "Failed to load organization data": "Failed to load organization data",
  "Organization ID is required": "Organization ID is required",
  "Failed to load bots": "Failed to load bots",
  "Failed to load members": "Failed to load members",
  "Failed to load invitations": "Failed to load invitations",
  "Actions": "Actions",
  "Save": "Save",
  "Menu Type": "Menu Type",
  "Roles": "Roles",
  "Parent Menu": "Parent Menu",
  "Menu Name": "Menu Name",
  "Please enter menu name": "Please enter menu name",
  "Route Name": "Route Name",
  "Please enter route name": "Please enter route name",
  "Route Path": "Route Path",
  "Please enter route path": "Please enter route path",
  "Component Path": "Component Path",
  "Menu Order": "Menu Order",
  "Route Redirect": "Route Redirect",
  "Menu Icon": "Menu Icon",
  "Extra Icon": "Extra Icon",
  "Enter Animation": "Enter Animation",
  "Leave Animation": "Leave Animation",
  "Active Menu": "Active Menu",
  "Frame URL": "Frame URL",
  "Frame Loading": "Frame Loading",
  "Show Menu": "Show Menu",
  "Show Parent": "Show Parent",
  "Keep Alive": "Keep Alive",
  "Hidden Tag": "Hidden Tag",
  "Fixed Tag": "Fixed Tag",
  "Sidebar Information": "Sidebar Information",
  "Failed to fetch members": "Failed to fetch members",
  "Failed to fetch users": "Failed to fetch users",
  "Failed to fetch guests": "Failed to fetch guests",
  "Search Users": "Search Users",
  "Search by name or email...": "Search by name or email...",
  "Selected Members": "Selected Members",
  "No members selected": "No members selected",

  "Social login successful": "Social login successful",
  "Social registration successful": "Social registration successful",
  "Social login failed. Please try again.": "Social login failed. Please try again.",
  "Social registration failed. Please try again.": "Social registration failed. Please try again.",
  "Social login was cancelled": "Social login was cancelled",
  "Social login timed out. Please try again.": "Social login timed out. Please try again.",
  "Authentication failed. Please try again.": "Authentication failed. Please try again.",

  "Please allow popups for social login to work": "Please allow popups for social login to work",
  "Your browser doesn't support popups. Please enable popups or try a different browser.": "Your browser doesn't support popups. Please enable popups or try a different browser.",
  "Please allow popups for social login to work. Check your browser's popup blocker settings.": "Please allow popups for social login to work. Check your browser's popup blocker settings.",

  "Enable Popups": "Enable Popups",
  "Got it": "Got it",
  "Social login requires popups to work properly.": "Social login requires popups to work properly.",
  "Please follow these steps to enable popups:": "Please follow these steps to enable popups:",

  "Click the popup blocked icon": "Click the popup blocked icon",
  "in the address bar": "in the address bar",
  "Select 'Always allow popups from this site'": "Select 'Always allow popups from this site'",
  "Click 'Done' and try social login again": "Click 'Done' and try social login again",
  "Alternative: Go to Settings > Privacy and security > Site Settings > Pop-ups and redirects": "Alternative: Go to Settings > Privacy and security > Site Settings > Pop-ups and redirects",

  "Click the shield icon in the address bar": "Click the shield icon in the address bar",
  "Click 'Turn off Blocking for This Site'": "Click 'Turn off Blocking for This Site'",
  "Refresh the page and try social login again": "Refresh the page and try social login again",
  "Alternative: Go to Preferences > Privacy & Security > Permissions > Block pop-up windows": "Alternative: Go to Preferences > Privacy & Security > Permissions > Block pop-up windows",

  "Go to Safari menu > Preferences": "Go to Safari menu > Preferences",
  "Click the 'Websites' tab": "Click the 'Websites' tab",
  "Select 'Pop-up Windows' from the left sidebar": "Select 'Pop-up Windows' from the left sidebar",
  "Set this website to 'Allow'": "Set this website to 'Allow'",

  "Select 'Always allow'": "Select 'Always allow'",
  "Try social login again": "Try social login again",
  "Alternative: Go to Settings > Cookies and site permissions > Pop-ups and redirects": "Alternative: Go to Settings > Cookies and site permissions > Pop-ups and redirects",

  "Look for a popup blocked notification in your browser": "Look for a popup blocked notification in your browser",
  "Click on it and allow popups for this site": "Click on it and allow popups for this site",
  "Refresh the page and try again": "Refresh the page and try again",

  "Processing social login...": "Processing social login...",
  "Please wait while we complete your authentication.": "Please wait while we complete your authentication.",
  "Verifying credentials": "Verifying credentials",
  "Loading user information": "Loading user information",
  "Initializing application": "Initializing application",
  "Social Login Failed": "Social Login Failed",
  "Back to Login": "Back to Login",
  "Try Again": "Try Again",
  "Redirecting to your dashboard...": "Redirecting to your dashboard...",
  "Failed to initiate social login": "Failed to initiate social login",
  "Access denied by social provider": "Access denied by social provider",
  "Invalid authentication request": "Invalid authentication request",
  "Social provider server error": "Social provider server error",
  "Social provider temporarily unavailable": "Social provider temporarily unavailable",
  "Invalid authentication token": "Invalid authentication token",
  "User not found": "User not found",
  "Email not verified with social provider": "Email not verified with social provider"
}

  "No members selected": "No members selected",
  "Utilities": "Utilities",
  "Use": "Use",
  "Bot User Assignment": "Bot User Assignment",
  "User Assignment": "User Assignment",
  "Manage users who can access this private bot": "Manage users who can access this private bot",
  "Assign Users": "Assign Users",
  "Search users...": "Search users...",
  "User": "User",
  "Assigned Date": "Assigned Date",
  "Remove Selected": "Remove Selected",
  "Are you sure to remove this user from bot access?": "Are you sure to remove this user from bot access?",
  "Are you sure to remove selected users from bot access?": "Are you sure to remove selected users from bot access?",
  "Assign Users to Bot": "Assign Users to Bot",
  "Selected": "Selected",
  "Assign Selected": "Assign Selected",
  "Show already assigned users (for debugging)": "Show already assigned users (for debugging)",
  "No available users": "No available users",
  "All organization members are already assigned to this bot": "All organization members are already assigned to this bot",
  "Failed to load users": "Failed to load users",
  "Failed to load assigned users": "Failed to load assigned users",
  "Failed to assign users": "Failed to assign users",
  "Failed to remove users": "Failed to remove users",
  "Users assigned successfully": "Users assigned successfully",
  "Users removed successfully": "Users removed successfully",
  "Avatar must be JPG, PNG or JPEG format!": "Avatar must be JPG, PNG or JPEG format!",
  "Avatar size must be less than 5MB!": "Avatar size must be less than 5MB!",
  "Avatar uploaded successfully!": "Avatar uploaded successfully!",
  "Avatar upload failed!": "Avatar upload failed!",
  "Please select items to retrain": "Please select items to retrain",
  "Retraining queued successfully": "Retraining queued successfully",
  "Retrain failed": "Retrain failed",
  "Restores failed": "Restores failed",
  "Organization Name": "Organization Name",
  "Filter by email address": "Filter by email address",
  "All Roles": "All Roles",
  "Filter by role": "Filter by role",
  "Accepted": "Accepted",
  "Cancelled": "Cancelled",
  "Filter by who sent the invitation": "Filter by who sent the invitation",
  "Date Range": "Date Range",
  "Start Date": "Start Date",
  "End Date": "End Date",
  "Filter Invitations": "Filter Invitations",
  "Member Management": "Member Management",
  "Bulk Update Role": "Bulk Update Role",
  "Set as Admin": "Set as Admin",
  "Set as Member": "Set as Member",
  "Please select items to update": "Please select items to update",
  "Role updated successfully": "Role updated successfully",
  "Role update failed": "Role update failed",
  "File": "File",
  "Processing": "Processing",
  "Failed": "Failed",
  "File uploaded successfully": "File uploaded successfully",
  "File removed successfully": "File removed successfully",
  "Failed to remove file from server": "Failed to remove file from server",
  "Files currently attached to this bot": "Files currently attached to this bot",
  "Click ": "Click ",
  "Size": "Size",
  "Select files from your library to add to this bot": "Select files from your library to add to this bot",
  "From Date": "From Date",
  "Select start date": "Select start date",
  "To Date": "To Date",
  "Select end date": "Select end date",
  "Clear Filters": "Clear Filters",
  "Please select items to permanently delete": "Please select items to permanently delete",
  "File deleted successfully": "File deleted successfully",
  "Invitation Management": "Invitation Management",
  "Bulk Resend": "Bulk Resend",
  "Bulk Cancel": "Bulk Cancel",
  "Failed to fetch invitations": "Failed to fetch invitations",
  "Failed to cancel invitation": "Failed to cancel invitation",
  "Please select items to resend": "Please select items to resend",
  "Invitations resent successfully": "Invitations resent successfully",
  "Failed to resend invitations": "Failed to resend invitations",
  "Please select items to cancel": "Please select items to cancel",
  "Invitations cancelled successfully": "Invitations cancelled successfully",
  "Failed to cancel invitations": "Failed to cancel invitations",
  "Please input username": "Please input username",
  "Please input password": "Please input password",
  "Please input first name": "Please input first name",
  "Please input last name": "Please input last name",
  "Please input valid email": "Please input valid email",
  "Please select at least one role": "Please select at least one role",
  "Male": "Male",
  "Female": "Female",
  "Other": "Other",
  "Please input email": "Please input email",
  "First name": "First name",
  "Last name": "Last name",
  "Guest Management": "Guest Management",
  "Tag": "Tag",
  "Category": "Category",
  "Account": "Account",
  "Appearance": "Appearance",
  "Create Bot": "Create Bot",
  "Edit Bot": "Edit Bot",
  "Bot Name": "Bot Name",
  "AI Model": "AI Model",
  "Bot updated successfully": "Bot updated successfully",
  "Bot created successfully": "Bot created successfully",
  "Are you sure you want to delete this bot?": "Are you sure you want to delete this bot?",
  "Bot deleted successfully": "Bot deleted successfully",
  "Conversations": "Conversations",
  "Organization": "Organization",
  "Activate": "Activate",
  "Role": "Role",
  "Select role": "Select role",
  "Account Information": "Account Information",
  "Conversation Trends": "Conversation Trends",
  "Mon": "Mon",
  "Tue": "Tue",
  "Wed": "Wed",
  "Thu": "Thu",
  "Fri": "Fri",
  "Sat": "Sat",
  "Sun": "Sun",
  "Daily Activity": "Daily Activity",
  "Activity": "Activity",
  "Unnamed": "Unnamed",
  "Expires At": "Expires At",
  "Organization description": "Organization description",
  "Avg Conversation Length": "Avg Conversation Length",
  "Most Used Bot": "Most Used Bot",
  "Peak Activity": "Peak Activity",
  "this week": "this week",
  "messages per conversation": "messages per conversation",
  "Most active hour": "Most active hour",
  "Active Rate": "Active Rate",
  "Bot Usage Distribution": "Bot Usage Distribution",
  "Token Usage Trend": "Token Usage Trend",
  "Peak Activity Hours": "Peak Activity Hours",
  "Week 1": "Week 1",
  "Week 2": "Week 2",
  "Week 3": "Week 3",
  "Week 4": "Week 4",
  "used": "used",
  "Overview": "Overview",
  "AI Bot": "AI Bot",
  "Members": "Members",
  "Guests": "Guests",
  "Invitations": "Invitations"
}