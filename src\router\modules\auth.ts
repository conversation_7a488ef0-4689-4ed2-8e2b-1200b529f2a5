export default {
  path: "/auth",
  name: "Auth",
  redirect: "/login",
  meta: {
    title: "Authentication",
    showLink: false,
    hiddenTag: true
  },
  children: [
    {
      path: "/auth/social/callback",
      name: "SocialCallback",
      component: () => import("@/views/auth/social/callback/index.vue"),
      meta: {
        title: "Social Login Callback",
        showLink: false,
        hiddenTag: true,
        requiresAuth: false // This is a public route
      }
    }
  ]
} satisfies RouteConfigsTable;
