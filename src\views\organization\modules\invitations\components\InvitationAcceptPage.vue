<template>
  <div class="invitation-accept-page">
    <div class="container">
      <!-- Loading State -->
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="error-container">
        <div class="error-icon">
          <el-icon :size="64" color="#f56565">
            <WarningFilled />
          </el-icon>
        </div>
        <h2 class="error-title">{{ $t("Invalid Invitation") }}</h2>
        <p class="error-message">{{ error }}</p>
        <el-button type="primary" @click="goToLogin">
          {{ $t("Go to Login") }}
        </el-button>
      </div>

      <!-- Success State -->
      <div v-else-if="processed" class="success-container">
        <div class="success-icon">
          <el-icon :size="64" color="#48bb78">
            <SuccessFilled />
          </el-icon>
        </div>
        <h2 class="success-title">
          {{
            action === "accept"
              ? $t("Invitation Accepted")
              : $t("Invitation Declined")
          }}
        </h2>
        <p class="success-message">
          {{ successMessage }}
        </p>
        <div class="action-buttons">
          <el-button type="primary" @click="goToLogin">
            {{ $t("Go to Login") }}
          </el-button>
          <el-button @click="goToHome">
            {{ $t("Go to Home") }}
          </el-button>
        </div>
      </div>

      <!-- Invitation Details -->
      <div v-else class="invitation-container">
        <!-- Organization Info -->
        <div class="organization-info">
          <div class="org-logo">
            <el-avatar
              :size="80"
              :src="invitation.organization?.logoUrl"
              :alt="invitation.organization?.name"
            >
              {{ invitation.organization?.name?.[0] }}
            </el-avatar>
          </div>
          <h1 class="org-name">{{ invitation.organization?.name }}</h1>
          <p class="org-description">
            {{ invitation.organization?.description }}
          </p>
        </div>

        <!-- Invitation Details -->
        <div class="invitation-details">
          <h2 class="invitation-title">{{ $t("You're Invited!") }}</h2>
          <div class="invitation-info">
            <div class="info-item">
              <span class="label">{{ $t("Invited by") }}:</span>
              <span class="value"
                >{{ invitation.invitedBy?.firstName }}
                {{ invitation.invitedBy?.lastName }}</span
              >
            </div>
            <div class="info-item">
              <span class="label">{{ $t("Role") }}:</span>
              <el-tag :type="getRoleTagType(invitation.role)" size="large">
                {{ invitation.role }}
              </el-tag>
            </div>
            <div class="info-item">
              <span class="label">{{ $t("Email") }}:</span>
              <span class="value">{{ invitation.email }}</span>
            </div>
            <div v-if="invitation.message" class="info-item">
              <span class="label">{{ $t("Message") }}:</span>
              <div class="message-content">{{ invitation.message }}</div>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
          <el-button
            type="primary"
            size="large"
            :loading="processing"
            @click="handleAccept"
          >
            {{ $t("Accept Invitation") }}
          </el-button>
          <el-button size="large" :loading="processing" @click="handleDecline">
            {{ $t("Decline") }}
          </el-button>
        </div>

        <!-- Terms Notice -->
        <div class="terms-notice">
          <p class="text-sm text-gray-500 dark:text-gray-400">
            {{ $t("By accepting this invitation, you agree to our") }}
            <a href="/terms" target="_blank" class="link">{{
              $t("Terms of Service")
            }}</a>
            {{ $t("and") }}
            <a href="/privacy" target="_blank" class="link">{{
              $t("Privacy Policy")
            }}</a>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { WarningFilled, SuccessFilled } from "@element-plus/icons-vue";
import { $t } from "@/plugins/i18n";
import { message } from "@/utils/message";
import { useInvitationAcceptHook } from "../utils/invitation-accept-hook";

const route = useRoute();
const router = useRouter();

const token = route.params.token as string;

// Use hook for invitation logic
const {
  loading,
  processing,
  error,
  processed,
  action,
  invitation,
  successMessage,
  fnGetInvitationDetails,
  fnAcceptInvitation,
  fnDeclineInvitation
} = useInvitationAcceptHook(token);

// Methods
const handleAccept = async () => {
  await fnAcceptInvitation();
};

const handleDecline = async () => {
  await fnDeclineInvitation();
};

const goToLogin = () => {
  router.push("/login");
};

const goToHome = () => {
  router.push("/");
};

const getRoleTagType = (role: string) => {
  const roleTypes = {
    owner: "danger",
    admin: "warning",
    member: "info",
    guest: ""
  };
  return roleTypes[role?.toLowerCase()] || "";
};

// Lifecycle
onMounted(() => {
  if (!token) {
    error.value = $t("Invalid invitation token");
    return;
  }
  fnGetInvitationDetails();
});
</script>

<style scoped>
.invitation-accept-page {
  @apply min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4;
}

.container {
  @apply max-w-2xl w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8;
}

.loading-container,
.error-container,
.success-container {
  @apply text-center py-12;
}

.error-icon,
.success-icon {
  @apply mb-6;
}

.error-title,
.success-title {
  @apply text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4;
}

.error-message,
.success-message {
  @apply text-gray-600 dark:text-gray-300 mb-6;
}

.invitation-container {
  @apply space-y-8;
}

.organization-info {
  @apply text-center pb-8 border-b border-gray-200 dark:border-gray-700;
}

.org-logo {
  @apply mb-4;
}

.org-name {
  @apply text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2;
}

.org-description {
  @apply text-gray-600 dark:text-gray-300;
}

.invitation-title {
  @apply text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6 text-center;
}

.invitation-info {
  @apply space-y-4;
}

.info-item {
  @apply flex flex-col sm:flex-row sm:items-center gap-2;
}

.label {
  @apply font-medium text-gray-700 dark:text-gray-300 sm:w-24;
}

.value {
  @apply text-gray-900 dark:text-gray-100;
}

.message-content {
  @apply bg-gray-50 dark:bg-gray-700 p-4 rounded-lg text-gray-900 dark:text-gray-100;
}

.action-buttons {
  @apply flex flex-col sm:flex-row gap-4 justify-center;
}

.terms-notice {
  @apply text-center pt-6 border-t border-gray-200 dark:border-gray-700;
}

.link {
  @apply text-blue-600 dark:text-blue-400 hover:underline;
}
</style>
