<template>
  <el-config-provider :locale="messages">
    <router-view />
    <ReDialog />
    <ReDrawer />
  </el-config-provider>
</template>

<script lang="ts">
import { defineComponent, computed, onBeforeMount, ref, onMounted } from "vue";
import { ElConfigProvider } from "element-plus";
import { ReDialog } from "@/components/ReDialog";
import { ReDrawer } from "@/components/ReDrawer";
import en from "element-plus/es/locale/lang/en";
import vi from "element-plus/es/locale/lang/vi";
import plusEn from "plus-pro-components/es/locale/lang/en";
import plusVi from "@/locales/plus-vi";
import { useSettingStoreHook } from "@/store/modules/settings";
import { storageLocal } from "@pureadmin/utils";
import { useLanguageStoreHook } from "@/store/modules/language";
import { useSoketi } from "@/services/soketi.service";

export default defineComponent({
  name: "app",
  components: {
    [ElConfigProvider.name]: ElConfigProvider,
    ReDialog,
    ReDrawer
  },
  setup() {
    const useStoreSetting = useSettingStoreHook();
    const useStoreLanguage = useLanguageStoreHook();
    const localStorage = storageLocal();
    const { connect, state } = useSoketi();

    const initialLocale = localStorage.getItem("locale");
    const locale = ref(initialLocale || "en");

    const messages = computed(() => {
      return locale.value === "vi"
        ? { ...vi, ...plusVi }
        : { ...en, ...plusEn };
    });

    onBeforeMount(async () => {
      await useStoreSetting.fetchPublicSettings();
      await useStoreLanguage.fetchPublicLanguages();

      localStorage.setItem("languages", useStoreLanguage.languages);

      if (!initialLocale) {
        const defaultLocaleFromSettings =
          useStoreSetting.settings.general?.language;
        if (defaultLocaleFromSettings) {
          locale.value = defaultLocaleFromSettings;
          localStorage.setItem("locale", defaultLocaleFromSettings);
        }
      }
    });

    onMounted(() => {
      connect();
    });

    return {
      messages
    };
  },
  beforeCreate() {
    console.log("App init");
  }
});
</script>
