<script setup lang="ts">
import { ref, nextTick, onMounted, onUnmounted, computed } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { $t } from "@/plugins/i18n";
import { useUserStore } from "@/store/modules/user";

// ===== INTERFACES =====
interface Props {
  mainView?: string;
  chatBot: any;
}

interface Emits {
  (e: "newConversation"): void;
  (e: "showConversation", conversation: any): void;
}

// ===== COMPONENT SETUP =====
const props = withDefaults(defineProps<Props>(), {
  mainView: "list"
});

const emit = defineEmits<Emits>();

// ===== CHAT BOT PROPERTIES =====
const {
  fetchAllConversations,
  promptUpdateConversation,
  confirmDeleteConversation,
  conversations,
  currentConversationId,
  isTyping
} = props.chatBot;

// ===== LOCAL STATE =====
const openedDropdownUuid = ref<string | null>(null);
const loading = ref(false);
const clientHeight = ref(400);
const chatSidebarContainer = ref(null);

// ===== COMPUTED PROPERTIES =====
const user = computed(() => {
  return useUserStore().userInfo;
});

// ===== EVENT HANDLERS =====
const handleDropdownVisibleChange = (uuid: string, isVisible: boolean) => {
  openedDropdownUuid.value = isVisible ? uuid : null;
};

const startNewConversation = () => {
  emit("newConversation");
};

const showConversation = (conversation: any) => {
  isTyping.value = false;
  emit("showConversation", conversation);
};

const updateChatContainerHeight = () => {
  if (chatSidebarContainer.value) {
    const header = document.querySelector(".chat-header");
    const footer = document.querySelector(".chat-footer");
    const headerHeight = header ? header.getBoundingClientRect().height : 81;
    const footerHeight = footer ? footer.getBoundingClientRect().height : 125;
    const parentHeight = chatSidebarContainer.value.parentElement.clientHeight;
    const newHeight = parentHeight - headerHeight - footerHeight;
    clientHeight.value = newHeight > 0 ? newHeight : 400;
  }
};

onMounted(() => {
  nextTick(() => {
    fetchAllConversations();
    updateChatContainerHeight();
  });
  window.addEventListener("resize", updateChatContainerHeight);
});

onUnmounted(() => {
  window.removeEventListener("resize", updateChatContainerHeight);
});
</script>

<template>
  <div
    ref="chatSidebarContainer"
    class="chat-sidebar-container w-full max-w-[360px] h-full relative rounded-l-xl bg-white border-r-2 border-gray-200"
  >
    <div class="agents-content h-full">
      <div class="flex flex-col h-full">
        <header class="flex items-center justify-between px-4 py-3">
          <div class="flex items-center">
            <img
              :src="user.avatarUrl || '/bots/default.png'"
              alt="logo"
              class="w-10 h-10 rounded-full mr-4 object-cover"
            />
            <div>
              <h2 class="text-lg font-semibold text-gray-900">
                {{ user.nickname }}
              </h2>
              <div class="text-sm text-gray-500">
                {{ user.email }}
              </div>
            </div>
          </div>
        </header>
        <div class="relative h-[32px]">
          <div class="absolute top-0 left-0 right-0 flex justify-center">
            <el-button
              type="primary"
              class="w-full !rounded-none"
              @click="startNewConversation"
            >
              <IconifyIconOnline
                :icon="'mdi:chat-plus-outline'"
                class="mr-2 text-2xl"
              />
              {{ $t("New conversation") }}
            </el-button>
          </div>
        </div>
        <div
          class="flex flex-col"
          :class="{ 'h-full justify-center': loading }"
        >
          <!-- Loading State -->
          <div v-if="loading" class="flex items-center justify-center py-8">
            <IconifyIconOnline
              :icon="'eos-icons:loading'"
              class="text-5xl text-amber-700"
            />
          </div>
          <div v-else class="flex flex-col gap-1 px-3 py-2">
            <div
              v-for="(conversation, idx) in conversations"
              :key="conversation.uuid"
              class="group flex items-center justify-between hover:bg-primary hover:text-white cursor-pointer px-2 py-1 rounded-3xl text-xs text-primary"
              :class="{
                'bg-primary text-white':
                  openedDropdownUuid === conversation.uuid ||
                  conversation.uuid === currentConversationId
              }"
            >
              <div
                class="py-2 flex-grow min-w-0 pr-3"
                @click="showConversation(conversation)"
              >
                <div class="truncate font-semibold">
                  {{ conversation.title }}
                </div>
              </div>
              <div
                class="invisible group-hover:visible flex max-w-[80px]"
                :class="{
                  '!visible': openedDropdownUuid === conversation.uuid
                }"
              >
                <el-dropdown
                  trigger="click"
                  @visible-change="
                    handleDropdownVisibleChange(conversation.uuid, $event)
                  "
                >
                  <IconifyIconOnline
                    :icon="'mdi:dots-vertical'"
                    class="text-xl text-white"
                  />
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item
                        :icon="useRenderIcon('ri:edit-2-line')"
                        @click="promptUpdateConversation(idx, conversation)"
                      >
                        {{ $t("Rename") }}
                      </el-dropdown-item>
                      <el-dropdown-item
                        :icon="useRenderIcon('ri:delete-bin-line')"
                        class="!text-red-700"
                        @click="confirmDeleteConversation(idx, conversation)"
                      >
                        {{ $t("Delete") }}
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
