<script setup lang="ts">
import { computed } from "vue";
import { $t } from "@/plugins/i18n";
import { IconifyIconOnline } from "@/components/ReIcon";
import {
  ElCard,
  ElStatistic,
  ElProgress,
  ElSkeleton,
  ElRow,
  ElCol,
  ElSkeletonItem,
  ElButton
} from "element-plus";
import type { OrganizationOverviewStats } from "../../../shared/utils/type";

interface Props {
  stats: OrganizationOverviewStats | null;
  loading: boolean;
}

interface Emits {
  (e: "refresh"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// Utility functions
const formatNumber = (num: number) => {
  if (num >= 1000000) return (num / 1000000).toFixed(1) + "M";
  if (num >= 1000) return (num / 1000).toFixed(1) + "K";
  return num.toString();
};

const formatBytes = (bytes: number) => {
  if (bytes === 0) return "0 MB";
  const k = 1024;
  const sizes = ["MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
};

// Computed properties for stats
const statsCards = computed(() => [
  {
    title: $t("AI Bots"),
    value: props.stats?.bots?.total || 0,
    subtitle: `${props.stats?.bots?.active || 0} ${$t("active")} • ${props.stats?.bots?.draft || 0} ${$t("draft")}`,
    icon: "ri:robot-2-line",
    color: "blue",
    formatter: formatNumber,
    trend: props.stats?.bots
      ? {
          value: Math.round(
            (props.stats.bots.active / props.stats.bots.total) * 100
          ),
          label: $t("Active Rate")
        }
      : null
  },
  {
    title: $t("Total Conversations"),
    value: props.stats?.conversations?.total || 0,
    subtitle: `${props.stats?.conversations?.today || 0} ${$t("today")} • ${props.stats?.conversations?.thisWeek || 0} ${$t("this week")}`,
    icon: "ri:chat-3-line",
    color: "green",
    formatter: formatNumber,
    trend: props.stats?.conversations
      ? {
          value: props.stats.conversations.today,
          label: $t("Today")
        }
      : null
  },
  {
    title: $t("Team Members"),
    value: props.stats?.members?.total || 0,
    subtitle: `${props.stats?.members?.active || 0} ${$t("active")} • ${props.stats?.members?.admins || 0} ${$t("admins")}`,
    icon: "ri:team-line",
    color: "purple",
    formatter: formatNumber,
    trend: props.stats?.members
      ? {
          value: props.stats.members.pending,
          label: $t("Pending")
        }
      : null
  },
  {
    title: $t("Token Usage"),
    value: props.stats?.tokens?.thisMonth || 0,
    subtitle: `${$t("This month")} • ${props.stats?.tokens?.estimatedCost || "$0"}`,
    icon: "ri:coin-line",
    color: "orange",
    formatter: formatNumber,
    trend: props.stats?.tokens
      ? {
          value: props.stats.tokens.totalUsed,
          label: $t("Total Used")
        }
      : null
  }
]);

// Secondary stats for additional metrics
const secondaryStats = computed(() => [
  {
    title: $t("Storage Used"),
    value: props.stats?.storage?.totalUsedMB || 0,
    subtitle: `${formatBytes(props.stats?.storage?.quotaLimitMB || 0)} ${$t("total")}`,
    icon: "ri:database-2-line",
    color: "blue",
    formatter: formatBytes,
    progress: props.stats?.storage?.usagePercent || 0
  },
  {
    title: $t("Avg Conversation Length"),
    value: props.stats?.conversations?.avgConversationLength || 0,
    subtitle: `${$t("messages per conversation")}`,
    icon: "ri:message-3-line",
    color: "green",
    formatter: (val: number) => val.toFixed(1)
  },
  {
    title: $t("Most Used Bot"),
    value: props.stats?.bots?.mostUsedBot || $t("N/A"),
    subtitle: props.stats?.activity?.popularBots?.[0]
      ? `${props.stats.activity.popularBots[0].usage} ${$t("conversations")}`
      : "",
    icon: "ri:star-line",
    color: "purple",
    formatter: (val: string | number) =>
      typeof val === "string" ? val : formatNumber(Number(val))
  },
  {
    title: $t("Peak Activity"),
    value: props.stats?.activity?.peakHours?.[0] || 0,
    subtitle: `${$t("Most active hour")}: ${props.stats?.activity?.peakHours?.[0] || 0}:00`,
    icon: "ri:time-line",
    color: "orange",
    formatter: (val: number) => `${val}:00`
  }
]);

const getColorClasses = (color: string) => {
  const colorMap = {
    blue: "bg-blue-100 text-blue-600",
    green: "bg-green-100 text-green-600",
    purple: "bg-purple-100 text-purple-600",
    orange: "bg-orange-100 text-orange-600"
  };
  return colorMap[color] || colorMap.blue;
};

const getProgressColor = (percentage: number) => {
  if (percentage > 80) return "#f56565";
  if (percentage > 60) return "#ed8936";
  return "#48bb78";
};
</script>

<template>
  <div class="stats-cards gap-4">
    <!-- Primary Stats -->
    <div class="mb-6">
      <el-row :gutter="24">
        <el-col
          v-for="(card, index) in statsCards"
          :key="`primary-${index}`"
          :xs="24"
          :sm="12"
          :md="6"
        >
          <el-card shadow="hover" class="stats-card h-full">
            <div class="flex items-center justify-between h-full">
              <div class="flex-1">
                <div class="text-sm text-gray-600 mb-2">{{ card.title }}</div>

                <!-- Loading State -->
                <el-skeleton v-if="loading" animated>
                  <template #template>
                    <el-skeleton-item
                      variant="text"
                      style="width: 60px; height: 32px"
                    />
                  </template>
                </el-skeleton>

                <!-- Value Display -->
                <div v-else>
                  <div class="text-2xl font-bold text-gray-900 mb-1">
                    {{ card.formatter(Number(card.value)) }}
                  </div>

                  <!-- Subtitle -->
                  <div
                    v-if="card.subtitle && !loading"
                    class="text-xs text-gray-500 mb-2"
                  >
                    {{ card.subtitle }}
                  </div>

                  <!-- Trend Info -->
                  <div
                    v-if="card.trend && !loading"
                    class="text-xs text-blue-600"
                  >
                    {{ card.trend.label }}: {{ card.trend.value }}
                  </div>
                </div>
              </div>

              <!-- Icon -->
              <div
                :class="`w-12 h-12 rounded-lg flex items-center justify-center ${getColorClasses(card.color)}`"
              >
                <IconifyIconOnline :icon="card.icon" class="text-2xl" />
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- Secondary Stats -->
    <div>
      <el-row :gutter="24">
        <el-col
          v-for="(card, index) in secondaryStats"
          :key="`secondary-${index}`"
          :xs="24"
          :sm="12"
          :md="6"
        >
          <el-card shadow="hover" class="stats-card h-full">
            <div class="flex items-center justify-between h-full">
              <div class="flex-1">
                <div class="text-sm text-gray-600 mb-2">{{ card.title }}</div>

                <!-- Loading State -->
                <el-skeleton v-if="loading" animated>
                  <template #template>
                    <el-skeleton-item
                      variant="text"
                      style="width: 60px; height: 32px"
                    />
                  </template>
                </el-skeleton>

                <!-- Value Display -->
                <div v-else>
                  <div class="text-xl font-bold text-gray-900 mb-1">
                    {{ card.formatter(Number(card.value)) }}
                  </div>

                  <!-- Subtitle -->
                  <div
                    v-if="card.subtitle && !loading"
                    class="text-xs text-gray-500 mb-2"
                  >
                    {{ card.subtitle }}
                  </div>

                  <!-- Progress Bar for Storage -->
                  <div
                    v-if="card.progress !== undefined && !loading"
                    class="w-full mt-2"
                  >
                    <el-progress
                      :percentage="card.progress"
                      :stroke-width="6"
                      :show-text="false"
                      :color="getProgressColor(card.progress)"
                    />
                    <div class="text-xs text-gray-500 mt-1">
                      {{ card.progress }}% used
                    </div>
                  </div>
                </div>
              </div>

              <!-- Icon -->
              <div
                :class="`w-12 h-12 rounded-lg flex items-center justify-center ${getColorClasses(card.color)}`"
              >
                <IconifyIconOnline :icon="card.icon" class="text-xl" />
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<style scoped>
.stats-cards {
  padding: 0;
}

.stats-card {
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
  min-height: 120px;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border-color: #d1d5db;
}

.h-full {
  height: 100%;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .stats-card {
    min-height: 100px;
  }

  .text-2xl {
    font-size: 1.5rem;
  }

  .text-xl {
    font-size: 1.25rem;
  }
}

/* Color variations for better visual hierarchy */
.stats-card .text-gray-900 {
  color: #111827;
}

.stats-card .text-gray-600 {
  color: #4b5563;
}

.stats-card .text-gray-500 {
  color: #6b7280;
}

.stats-card .text-blue-600 {
  color: #2563eb;
}
</style>
