# Social Login Implementation Guide

## 📋 Overview

This document describes the implementation of Social Login functionality using **redirect-based authentication**. The implementation supports multiple social providers (Google, Facebook, Twitter, GitHub, LinkedIn) with a secure redirect-based flow that solves popup blocker issues.

## 🔄 Migration Notice

**This implementation has been migrated from popup-based to redirect-based approach to solve popup blocker issues (85-95% blocked by browsers).**

See `docs/SOCIAL_LOGIN_MIGRATION.md` for migration details.

## 🏗️ Architecture

### Frontend Components

- **SocialAuthService** (`src/services/social-auth.service.ts`) - Manages redirect-based authentication
- **SocialLogin Component** (`src/views/auth/components/SocialLogin.vue`) - UI component for social login buttons
- **Social Callback Page** (`src/views/auth/social/callback/index.vue`) - Handles OAuth callback processing
- **Form State Manager** (`src/utils/form-state-manager.ts`) - Preserves form data during redirect

### Authentication Flow

```text
1. User clicks social login button
2. Save current state and redirect to social provider
3. User authenticates with social provider
4. Backend processes OAuth callback
5. Backend redirects to frontend callback with token
6. Frontend processes token and restores state
7. User is redirected to original page
```

## 🔧 Backend Requirements

### API Endpoints
```
GET /auth/social/{provider}
- Redirects to social provider OAuth URL
- Supports: google, facebook, twitter, github, linkedin

GET /auth/social/{provider}/callback  
- Handles OAuth callback from social provider
- Returns HTML page with postMessage script
```

### Callback HTML Response
The backend must return an HTML page that communicates with the popup parent:

```html
<!DOCTYPE html>
<html>
<head>
    <title>Social Login</title>
</head>
<body>
    <script>
        // Success case
        if (window.opener && '{{ $token }}') {
            window.opener.postMessage({
                type: 'SOCIAL_LOGIN_SUCCESS',
                token: '{{ $token }}'
            }, '{{ $frontendOrigin }}');
            window.close();
        }
        
        // Error case
        if (window.opener && '{{ $error }}') {
            window.opener.postMessage({
                type: 'SOCIAL_LOGIN_ERROR',
                error: '{{ $error }}'
            }, '{{ $frontendOrigin }}');
            window.close();
        }
    </script>
    
    <div style="text-align: center; padding: 50px;">
        <h3>Processing social login...</h3>
        <p>This window will close automatically.</p>
    </div>
</body>
</html>
```

## 🚀 Frontend Usage

### Basic Implementation
The social login is already integrated into login and register pages:

```vue
<SocialLogin
  :loading="loading"
  :disabled="disabled"
  @social-login="handleSocialLogin"
  @social-login-success="handleSocialLoginSuccess"
  @social-login-error="handleSocialLoginError"
/>
```

### Custom Implementation
```typescript
import { popupAuthService } from '@/services/popup-auth.service';

async function customSocialLogin(provider: string) {
  try {
    const result = await popupAuthService.openSocialLogin(provider);
    console.log('Token received:', result.token);
    // Process authentication...
  } catch (error) {
    console.error('Social login failed:', error);
  }
}
```

## 🔒 Security Features

### Token Security
- **Short-lived tokens**: 5-minute expiry for security
- **Origin validation**: postMessage origin checking
- **HTTPS enforcement**: Secure communication only
- **One-time use**: Tokens invalidated after use

### Popup Security
- **Controlled communication**: Only via postMessage API
- **Origin whitelist**: Validates message sources
- **Automatic cleanup**: Popup and listeners cleaned up properly

## 🎨 User Experience Features

### Error Handling
- **Popup blocker detection**: Automatic detection and user guidance
- **Browser-specific instructions**: Tailored help for Chrome, Firefox, Safari, Edge
- **Timeout handling**: 30-second timeout with user feedback
- **Cancellation support**: Graceful handling when user closes popup

### Loading States
- **Visual feedback**: Loading spinners on social buttons
- **Button states**: Disabled state during authentication
- **Progress indication**: Clear status communication

### Accessibility
- **Keyboard navigation**: Full keyboard support
- **Screen reader support**: Proper ARIA labels
- **High contrast**: Accessible color schemes

## 🌐 Internationalization

Social login messages are available in multiple languages:

### English (`locales/en.json`)
- "Social login successful"
- "Please allow popups for social login to work"
- Browser-specific popup instructions

### Vietnamese (`locales/vi.json`)
- "Đăng nhập mạng xã hội thành công"
- "Vui lòng cho phép popup để đăng nhập mạng xã hội hoạt động"
- Hướng dẫn popup cho từng trình duyệt

## 🧪 Testing

### Test Scenarios
1. ✅ Successful social login (new user)
2. ✅ Successful social login (existing user)
3. ✅ Account linking scenario
4. ❌ Popup blocked by browser
5. ❌ User cancels authentication
6. ❌ Network timeout
7. ❌ Invalid token from backend
8. ❌ Authentication API failure

### Browser Testing
- Chrome (popup blocker scenarios)
- Firefox (shield icon blocking)
- Safari (popup preferences)
- Edge (popup settings)

## 🔧 Configuration

### Environment Variables
```env
VITE_API_BASE_URL=http://localhost:8000
```

### Social Providers
Configure in `src/views/auth/components/SocialLogin.vue`:
```typescript
const providerConfig = {
  google: { icon: "flat-color-icons:google", name: "Google" },
  facebook: { icon: "logos:facebook", name: "Facebook" },
  twitter: { icon: "ant-design:twitter-circle-filled", name: "Twitter" },
  github: { icon: "akar-icons:github-fill", name: "GitHub" },
  linkedin: { icon: "akar-icons:linkedin-fill", name: "LinkedIn" }
};
```

## 🐛 Troubleshooting

### Common Issues

**Popup Blocked**
- Solution: Show popup instructions to user
- Prevention: Check popup support before opening

**postMessage Not Received**
- Check origin validation in popup service
- Verify backend HTML template is correct
- Ensure CORS is properly configured

**Token Invalid**
- Verify backend token generation
- Check token expiry settings
- Validate API endpoint responses

**Authentication Loop**
- Clear browser storage and cookies
- Check for conflicting authentication states
- Verify redirect URLs are correct

## 📈 Performance Considerations

### Optimization
- **Lazy loading**: Social login components loaded on demand
- **Efficient cleanup**: Proper memory management for popups
- **Minimal dependencies**: Lightweight implementation
- **Caching**: Browser-specific instructions cached locally

### Monitoring
- Track social login success/failure rates
- Monitor popup blocker incidents
- Measure authentication completion times
- Log error patterns for improvement

## 🔄 Future Enhancements

### Planned Features
- **SSO integration**: Enterprise single sign-on support
- **Additional providers**: More social login options
- **Mobile optimization**: Touch-friendly popup handling
- **Analytics integration**: Detailed usage tracking

### Maintenance
- Regular security audits
- Browser compatibility updates
- Performance optimization
- User experience improvements
