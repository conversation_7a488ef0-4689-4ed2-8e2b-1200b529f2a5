import { capitalize, h } from "vue";
import { $t } from "@/plugins/i18n";
import { IconifyIconOnline } from "@/components/ReIcon";
import { ElAvatar } from "element-plus";

export const columns: TableColumnList = [
  {
    type: "selection",
    width: "30px",
    sortable: false
  },
  {
    label: "",
    prop: "avatar",
    width: 100,
    cellRenderer: ({ row }) =>
      h(
        "div",
        {
          class: "flex items-center justify-center"
        },
        [
          h(ElAvatar, {
            size: 60,
            src: row.avatarUrl,
            icon: "User",
            class: "border border-gray-200"
          })
        ]
      )
  },
  {
    prop: "username",
    align: "left",
    sortable: true,
    minWidth: 280,
    headerRenderer: () => $t("User"),
    cellRenderer: ({ row }) => {
      const genderConfig = {
        male: { icon: "mdi:gender-male", class: "text-blue-500" },
        female: { icon: "mdi:gender-female", class: "text-pink-500" },
        other: { icon: "mdi:gender-transgender", class: "text-purple-500" }
      };
      const currentGender = genderConfig[row.gender] || genderConfig.other;

      return h("div", { class: "flex flex-col justify-between" }, [
        h("div", { class: "" }, [
          row.email &&
            h("div", { class: "flex text-xs font-medium text-gray-900" }, [
              row.fullName || $t("Unnamed"),
              row.gender &&
                h(IconifyIconOnline, {
                  icon: currentGender.icon,
                  class: `ml-1.5 w-3 h-3 ${currentGender.class}`
                })
            ]),
          h("div", { class: "flex items-center" }, [
            h(
              "span",
              { class: "text-xs text-gray-500 mr-2" },
              `@${row.username}`
            )
          ])
        ])
      ]);
    }
  },
  {
    prop: "email",
    align: "left",
    sortable: true,
    width: 160,
    headerRenderer: () => $t("Email")
  },
  {
    prop: "phone",
    align: "left",
    sortable: true,
    width: 160,
    headerRenderer: () => $t("Phone")
  },
  {
    prop: "pivot.role",
    align: "left",
    sortable: true,
    width: 160,
    headerRenderer: () => $t("Role"),
    formatter: ({ pivot }) => $t(capitalize(pivot.role))
  },
  {
    label: "",
    fixed: "right",
    width: 140,
    slot: "operation",
    sortable: false
  }
];
