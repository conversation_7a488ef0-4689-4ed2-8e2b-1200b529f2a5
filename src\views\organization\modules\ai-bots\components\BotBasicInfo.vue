<script setup lang="ts">
import { $t } from "@/plugins/i18n";

interface Props {
  values: {
    name?: string;
    model?: string;
    language?: string;
    description?: string;
  };
  aiModels: Array<{ label: string; value: string; description?: string }>;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (e: "update:values", values: any): void;
}>();

const updateField = (field: string, value: any) => {
  emit("update:values", { ...props.values, [field]: value });
};
</script>

<template>
  <div class="card">
    <h2 class="section-title">{{ $t("Basic Information") }}</h2>
    <el-row :gutter="20">
      <el-col :xs="24" :sm="12">
        <el-form-item :label="$t('AI Assistant Name')" prop="name">
          <el-input
            :model-value="props.values.name"
            clearable
            :placeholder="$t('Example: Administrative Procedure Consultant')"
            @update:model-value="val => updateField('name', val)"
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item :label="$t('AI Model')" prop="model">
          <el-select
            :model-value="props.values.model"
            class="w-full"
            placeholder="Chọn mô hình AI"
            popper-class="multi-line-select-dropdown"
            :teleported="false"
            @update:model-value="val => updateField('model', val)"
          >
            <el-option
              v-for="model in props.aiModels"
              :key="model.value"
              :label="model.label"
              :value="model.value"
            >
              <div class="option-content">
                <div class="option-name">{{ model.label }}</div>
                <div class="option-description">{{ model.description }}</div>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-form-item :label="$t('Description')">
      <el-input
        :model-value="props.values.description"
        type="textarea"
        :placeholder="$t('Brief description of AI Assistant functions.')"
        @update:model-value="val => updateField('description', val)"
      />
    </el-form-item>
  </div>
</template>

<style scoped>
/* Tùy chỉnh dropdown để hiển thị nhiều dòng */
:deep(.multi-line-select-dropdown) {
  min-width: 360px !important;
  max-width: 100% !important;
}

/* Cho phép el-option hiển thị chiều cao tùy ý */
:deep(.multi-line-select-dropdown .el-select-dropdown__item) {
  height: auto !important;
  line-height: normal !important;
  padding: 10px 14px !important;
  white-space: normal !important;
  word-wrap: break-word !important;
}

/* Layout cho nội dung trong mỗi option */
.option-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.option-name {
  font-weight: 500;
  font-size: 14px;
  color: var(--el-text-color-primary);
}

.option-description {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
}
</style>

<style lang="scss" scoped>
.card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #333;
}
</style>
