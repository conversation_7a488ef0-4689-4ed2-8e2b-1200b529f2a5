import dayjs from "dayjs";
import { $t } from "@/plugins/i18n";
import { ElAvatar } from "element-plus";
import { h } from "vue";
import { IconifyIconOnline } from "@/components/ReIcon";

export const columns: TableColumnList = [
  {
    type: "selection",
    width: "30px",
    sortable: false
  },
  {
    prop: "logo",
    align: "center",
    width: 90,
    headerRenderer: () => "",
    cellRenderer: ({ row }) => {
      return h(ElAvatar, {
        size: 50,
        src: row.logoUrl || null,
        alt: row.name
      });
    }
  },
  {
    prop: "name",
    align: "left",
    sortable: false,
    minWidth: 210,
    headerRenderer: () => $t("Bot Name"),
    cellRenderer: ({ row }) => {
      return h("div", { class: "flex flex-col" }, [
        h(
          "div",
          {
            class: "font-medium text-gray-900 text-sm line-clamp-2"
          },
          row.name || "Untitled Bot"
        ),
        h(
          "div",
          {
            class: "text-sm text-gray-500 mt-1"
          },
          row.description
        )
      ]);
    }
  },
  {
    prop: "aiModel",
    align: "left",
    width: 180,
    headerRenderer: () => $t("Model AI"),
    cellRenderer: ({ row }) => {
      return h(
        "span",
        {
          class: "text-sm font-medium text-gray-900"
        },
        row.aiModel?.name || "-"
      );
    }
  },
  {
    prop: "createdAt",
    align: "left",
    width: 140,
    headerRenderer: () => $t("Created At"),
    cellRenderer: ({ row }) => {
      return h(
        "span",
        {
          class: "text-sm text-gray-600"
        },
        dayjs(row.createdAt).format("YYYY-MM-DD HH:mm")
      );
    }
  },
  {
    label: "",
    fixed: "right",
    width: 140,
    slot: "operation",
    sortable: false
  }
];
