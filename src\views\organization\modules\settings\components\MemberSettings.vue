<script setup lang="ts">
import { ref } from "vue";
import { $t } from "@/plugins/i18n";
import {
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElSwitch,
  ElButton,
  ElCard,
  ElInputNumber,
  ElDivider
} from "element-plus";

const props = defineProps<{
  settings: any;
  loading: boolean;
}>();

const emit = defineEmits<{
  (e: "save", settings: any): void;
}>();

const formRef = ref();

const formData = ref({
  defaultRole: "member",
  autoApproveInvitations: true,
  allowMemberInvites: true,
  maxMembers: 100,
  requireEmailVerification: true,
  allowRoleChanges: true,
  sessionTimeout: 24,
  enforcePasswordPolicy: true,
  ...props.settings
});

const roles = [
  { label: "Member", value: "member" },
  { label: "Admin", value: "admin" },
  { label: "Viewer", value: "viewer" }
];

const handleSave = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    emit("save", formData.value);
  } catch (error) {
    console.error("Validation failed:", error);
  }
};

const handleReset = () => {
  formData.value = {
    defaultRole: "member",
    autoApproveInvitations: true,
    allowMemberInvites: true,
    maxMembers: 100,
    requireEmailVerification: true,
    allowRoleChanges: true,
    sessionTimeout: 24,
    enforcePasswordPolicy: true,
    ...props.settings
  };
};
</script>

<template>
  <ElCard>
    <template #header>
      <div class="flex items-center justify-between">
        <span class="text-lg font-semibold">Member Settings</span>
        <div class="space-x-2">
          <ElButton @click="handleReset">{{ $t("Reset") }}</ElButton>
          <ElButton type="primary" :loading="loading" @click="handleSave">
            {{ $t("Save") }}
          </ElButton>
        </div>
      </div>
    </template>

    <ElForm
      ref="formRef"
      :model="formData"
      label-width="200px"
      label-position="left"
    >
      <!-- Member Management -->
      <div class="mb-6">
        <h3 class="text-base font-medium text-gray-900 mb-4">Member Management</h3>
        
        <ElFormItem label="Default Role">
          <ElSelect
            v-model="formData.defaultRole"
            placeholder="Select default role"
            style="width: 200px"
          >
            <ElOption
              v-for="item in roles"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </ElSelect>
          <div class="text-sm text-gray-500 mt-1">
            Default role for new members
          </div>
        </ElFormItem>

        <ElFormItem label="Maximum Members">
          <ElInputNumber
            v-model="formData.maxMembers"
            :min="1"
            :max="10000"
            style="width: 200px"
          />
          <div class="text-sm text-gray-500 mt-1">
            Maximum number of members allowed
          </div>
        </ElFormItem>

        <ElFormItem label="Auto Approve Invitations">
          <ElSwitch
            v-model="formData.autoApproveInvitations"
            active-text="Yes"
            inactive-text="No"
          />
          <div class="text-sm text-gray-500 mt-1">
            Automatically approve member invitations
          </div>
        </ElFormItem>

        <ElFormItem label="Allow Member Invites">
          <ElSwitch
            v-model="formData.allowMemberInvites"
            active-text="Yes"
            inactive-text="No"
          />
          <div class="text-sm text-gray-500 mt-1">
            Allow members to invite other members
          </div>
        </ElFormItem>

        <ElFormItem label="Allow Role Changes">
          <ElSwitch
            v-model="formData.allowRoleChanges"
            active-text="Yes"
            inactive-text="No"
          />
          <div class="text-sm text-gray-500 mt-1">
            Allow admins to change member roles
          </div>
        </ElFormItem>
      </div>

      <ElDivider />

      <!-- Security Settings -->
      <div class="mb-6">
        <h3 class="text-base font-medium text-gray-900 mb-4">Security Settings</h3>
        
        <ElFormItem label="Email Verification">
          <ElSwitch
            v-model="formData.requireEmailVerification"
            active-text="Required"
            inactive-text="Optional"
          />
          <div class="text-sm text-gray-500 mt-1">
            Require email verification for new members
          </div>
        </ElFormItem>

        <ElFormItem label="Password Policy">
          <ElSwitch
            v-model="formData.enforcePasswordPolicy"
            active-text="Enforced"
            inactive-text="Disabled"
          />
          <div class="text-sm text-gray-500 mt-1">
            Enforce strong password requirements
          </div>
        </ElFormItem>

        <ElFormItem label="Session Timeout">
          <ElInputNumber
            v-model="formData.sessionTimeout"
            :min="1"
            :max="168"
            style="width: 200px"
          />
          <span class="ml-2 text-sm text-gray-500">hours</span>
          <div class="text-sm text-gray-500 mt-1">
            Automatic logout after inactivity
          </div>
        </ElFormItem>
      </div>
    </ElForm>
  </ElCard>
</template>

<style scoped>
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
}

:deep(.el-card__body) {
  padding: 24px;
}

h3 {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 8px;
}
</style>
