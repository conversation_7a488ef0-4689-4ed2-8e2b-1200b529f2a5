import { ref, nextTick, onUnmounted, type Ref } from "vue";
import * as echarts from "echarts";
import { $t } from "@/plugins/i18n";
import type { OrganizationOverviewStats } from "../../../shared/utils/type";

export function useOverviewCharts(
  stats: Ref<OrganizationOverviewStats | null>
) {
  // Chart refs
  const conversationChartRef = ref<HTMLDivElement>();
  const activityChartRef = ref<HTMLDivElement>();

  // Chart instances
  let conversationChart: echarts.ECharts | null = null;
  let activityChart: echarts.ECharts | null = null;

  // Chart theme configuration
  const getChartTheme = () => ({
    backgroundColor: "transparent",
    textStyle: {
      color: "#374151",
      fontFamily: "Inter, system-ui, sans-serif"
    },
    title: {
      textStyle: {
        color: "#374151",
        fontSize: 14,
        fontWeight: "500"
      }
    },
    tooltip: {
      backgroundColor: "rgba(255, 255, 255, 0.95)",
      borderColor: "#e5e7eb",
      borderWidth: 1,
      textStyle: {
        color: "#374151"
      },
      extraCssText: "box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);"
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      top: "15%",
      containLabel: true
    }
  });

  // Initialize conversation trends chart
  const initConversationChart = () => {
    if (!conversationChartRef.value) return;

    conversationChart = echarts.init(conversationChartRef.value);

    // Sample data - in real app, this would come from stats
    const weekData = [120, 200, 150, 80, 70, 110, 130];

    const option = {
      ...getChartTheme(),
      title: {
        text: $t("Conversation Trends"),
        left: "center",
        textStyle: {
          fontSize: 16,
          fontWeight: "600",
          color: "#1f2937"
        }
      },
      tooltip: {
        ...getChartTheme().tooltip,
        trigger: "axis",
        formatter: (params: any) => {
          const data = params[0];
          return `${data.name}<br/>${$t("Conversations")}: <strong>${data.value}</strong>`;
        }
      },
      xAxis: {
        type: "category",
        data: [
          $t("Mon"),
          $t("Tue"),
          $t("Wed"),
          $t("Thu"),
          $t("Fri"),
          $t("Sat"),
          $t("Sun")
        ],
        axisLine: {
          lineStyle: { color: "#e5e7eb" }
        },
        axisLabel: {
          color: "#6b7280",
          fontSize: 12
        }
      },
      yAxis: {
        type: "value",
        axisLine: {
          lineStyle: { color: "#e5e7eb" }
        },
        axisLabel: {
          color: "#6b7280",
          fontSize: 12
        },
        splitLine: {
          lineStyle: { color: "#f3f4f6" }
        }
      },
      series: [
        {
          name: $t("Conversations"),
          data: weekData,
          type: "line",
          smooth: true,
          symbol: "circle",
          symbolSize: 8,
          itemStyle: {
            color: "#3b82f6",
            borderWidth: 2,
            borderColor: "#ffffff"
          },
          lineStyle: {
            width: 3,
            color: "#3b82f6"
          },
          areaStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: "rgba(59, 130, 246, 0.3)" },
                { offset: 1, color: "rgba(59, 130, 246, 0.05)" }
              ]
            }
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: "rgba(59, 130, 246, 0.3)"
            }
          }
        }
      ]
    };

    conversationChart.setOption(option);
  };

  // Initialize daily activity chart
  const initActivityChart = () => {
    if (!activityChartRef.value) return;

    activityChart = echarts.init(activityChartRef.value);

    // Sample data - in real app, this would come from stats
    const hourlyData = [5, 20, 36, 45, 42, 38, 15];

    const option = {
      ...getChartTheme(),
      title: {
        text: $t("Daily Activity"),
        left: "center",
        textStyle: {
          fontSize: 16,
          fontWeight: "600",
          color: "#1f2937"
        }
      },
      tooltip: {
        ...getChartTheme().tooltip,
        trigger: "axis",
        formatter: (params: any) => {
          const data = params[0];
          return `${data.name}<br/>${$t("Activity")}: <strong>${data.value}</strong>`;
        }
      },
      xAxis: {
        type: "category",
        data: ["00:00", "04:00", "08:00", "12:00", "16:00", "20:00", "24:00"],
        axisLine: {
          lineStyle: { color: "#e5e7eb" }
        },
        axisLabel: {
          color: "#6b7280",
          fontSize: 12
        }
      },
      yAxis: {
        type: "value",
        axisLine: {
          lineStyle: { color: "#e5e7eb" }
        },
        axisLabel: {
          color: "#6b7280",
          fontSize: 12
        },
        splitLine: {
          lineStyle: { color: "#f3f4f6" }
        }
      },
      series: [
        {
          name: $t("Activity"),
          data: hourlyData,
          type: "bar",
          itemStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: "#10b981" },
                { offset: 1, color: "#059669" }
              ]
            },
            borderRadius: [4, 4, 0, 0]
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: "rgba(16, 185, 129, 0.3)"
            }
          }
        }
      ]
    };

    activityChart.setOption(option);
  };

  // Initialize all charts
  const initCharts = async () => {
    await nextTick();
    initConversationChart();
    initActivityChart();
  };

  // Resize charts
  const resizeCharts = () => {
    conversationChart?.resize();
    activityChart?.resize();
  };

  // Destroy charts
  const destroyCharts = () => {
    conversationChart?.dispose();
    activityChart?.dispose();
    conversationChart = null;
    activityChart = null;
  };

  // Update charts with new data
  const updateCharts = (newStats: OrganizationOverviewStats) => {
    // Update conversation chart with real data if available
    if (conversationChart && newStats.conversations) {
      // This would be implemented based on actual data structure
      // For now, keeping the sample data
    }

    // Update activity chart with real data if available
    if (activityChart && newStats.activity) {
      // This would be implemented based on actual data structure
      // For now, keeping the sample data
    }
  };

  // Handle window resize
  const handleResize = () => {
    resizeCharts();
  };

  // Cleanup on unmount
  onUnmounted(() => {
    window.removeEventListener("resize", handleResize);
    destroyCharts();
  });

  return {
    // Refs
    conversationChartRef,
    activityChartRef,

    // Methods
    initCharts,
    resizeCharts,
    destroyCharts,
    updateCharts,
    handleResize
  };
}
