import { http } from "@/utils/http";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { Result } from "@/utils/response";

/*
 ***************************
 *   Read Operations (GET)
 ***************************
 */
export const getOrganizations = (params?: any) => {
  return http.request<Result>("get", "/api/v1/auth/organizations", { params });
};

export const getOrganizationById = (id: string) => {
  return http.request<Result>("get", `/api/v1/auth/organizations/${id}`);
};

export const getOrganizationsDropdown = () => {
  return http.request<Result>("get", "/api/v1/auth/organizations/dropdown");
};

export const getOrganizationOverview = (uuid: string) => {
  return http.request<Result>(
    "get",
    `/api/v1/auth/organizations/${uuid}/overview`
  );
};

/*
 ***************************
 *   Create & Update Operations
 ***************************
 */
export const createOrganization = (data: any) => {
  return http.request<Result>("post", "/api/v1/auth/organizations", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateOrganizationById = (id: string, data: any) => {
  return http.request<Result>("put", `/api/v1/auth/organizations/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

export const partialUpdateOrganizationById = (id: string, data: any) => {
  return http.request<Result>("patch", `/api/v1/auth/organizations/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   File Upload Operations
 ***************************
 */
export const uploadOrganizationLogo = (file: File) => {
  const formData = new FormData();
  formData.append("logo", file);

  return http.request<Result>(
    "post",
    "/api/v1/auth/organizations/upload-logo",
    {
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data"
      }
    }
  );
};

/*
 ***************************
 *   Soft Delete Operations
 ***************************
 */
export const deleteOrganization = (id: string) => {
  return http.request<Result>(
    "delete",
    `/api/v1/auth/organizations/${id}/delete`
  );
};

export const bulkDeleteOrganizations = (data: { ids: string[] }) => {
  return http.request<Result>(
    "delete",
    "/api/v1/auth/organizations/bulk/delete",
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

/*
 ***************************
 *   Permanent Delete Operations
 ***************************
 */
export const deleteOrganizationPermanent = (id: string) => {
  return http.request<Result>(
    "delete",
    `/api/v1/auth/organizations/${id}/force`
  );
};

export const bulkDeleteOrganizationsPermanent = (data: { ids: string[] }) => {
  return http.request<Result>(
    "delete",
    "/api/v1/auth/organizations/bulk/force",
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

/*
 ***************************
 *   Restore Operations
 ***************************
 */
export const restoreOrganization = (id: string) => {
  return http.request<Result>(
    "put",
    `/api/v1/auth/organizations/${id}/restore`
  );
};

export const bulkRestoreOrganizations = (data: { ids: string[] }) => {
  return http.request<Result>(
    "put",
    "/api/v1/auth/organizations/bulk/restore",
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

/*
 ***************************
 *   Organization Settings Operations
 ***************************
 */
export const getOrganizationSettings = (uuid: string) => {
  return http.request<Result>(
    "get",
    `/api/v1/auth/organizations/${uuid}/settings`
  );
};

export const updateOrganizationSettings = (uuid: string, data: any) => {
  return http.request<Result>(
    "put",
    `/api/v1/auth/organizations/${uuid}/settings`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

export const resetOrganizationSettings = (uuid: string) => {
  return http.request<Result>(
    "delete",
    `/api/v1/auth/organizations/${uuid}/settings/reset`
  );
};

export const checkOrganizationAccess = (uuid: string, params?: any) => {
  return http.request<Result>(
    "get",
    `/api/v1/auth/organizations/${uuid}/access`,
    {
      params
    }
  );
};
