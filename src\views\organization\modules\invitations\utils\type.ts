export type Invitation = {
  id: number;
  email: string;
  role: string;
  status: "pending" | "accepted" | "cancelled" | "expired";
  invitedBy: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
  };
  expiresAt: string;
  sentAt: string;
  acceptedAt?: string;
  cancelledAt?: string;
  createdAt: string;
  updatedAt: string;
};

export type FormItemProps = {
  id?: number | null;
  email?: string;
  role?: string;
  message?: string;
  expiresIn?: number; // days
  status?: "pending" | "accepted" | "cancelled" | "expired";
  [key: string]: any;
};

export type InvitationFilterProps = {
  email?: string;
  role?: string;
  status?: string;
  invitedBy?: string;
  dateRange?: string[];
  [key: string]: any;
};
