// Re-export types from shared utils for convenience
export type { OrganizationOverviewStats } from "../../../shared/utils/type";

// Import Vue types for proper typing
import type { Ref, ComputedRef } from "vue";
import type { OrganizationOverviewStats } from "../../../shared/utils/type";

// Additional types specific to overview module
export interface OverviewChartData {
  conversationTrends: number[];
  activityHours: number[];
  labels: string[];
}

export interface ChartTheme {
  backgroundColor: string;
  textStyle: {
    color: string;
    fontFamily: string;
  };
  title: {
    textStyle: {
      color: string;
      fontSize: number;
      fontWeight: string;
    };
  };
  tooltip: {
    backgroundColor: string;
    borderColor: string;
    borderWidth: number;
    textStyle: {
      color: string;
    };
    extraCssText: string;
  };
  grid: {
    left: string;
    right: string;
    bottom: string;
    top: string;
    containLabel: boolean;
  };
}

export interface StatsCard {
  title: string;
  value: number;
  subtitle: string | null;
  icon: string;
  color: "blue" | "green" | "purple" | "orange";
  formatter: (value: number) => string;
  progress?: number;
}

export interface PopularBot {
  name: string;
  usage: number;
}

export interface PeakHour {
  hour: number;
  time: string;
  label: string;
}

export interface OverviewError {
  message: string;
  code?: string;
  details?: any;
}

export interface OverviewState {
  stats: OrganizationOverviewStats | null;
  loading: boolean;
  error: string | null;
}

// Chart configuration types
export interface EChartsOption {
  title?: any;
  tooltip?: any;
  legend?: any;
  grid?: any;
  xAxis?: any;
  yAxis?: any;
  series?: any[];
  backgroundColor?: string;
  textStyle?: any;
}

// Component prop types
export interface StatsCardsProps {
  stats: OrganizationOverviewStats | null;
  loading: boolean;
}

export interface ChartsSectionProps {
  stats: OrganizationOverviewStats | null;
  loading: boolean;
}

export interface ActivitySectionProps {
  stats: OrganizationOverviewStats | null;
  loading: boolean;
}

// Event types
export interface OverviewEvents {
  refresh: () => void;
}

// Utility types
export type ColorVariant = "blue" | "green" | "purple" | "orange" | "red";

export type LoadingState = "idle" | "loading" | "success" | "error";

export type ChartType = "line" | "bar" | "pie" | "area";

// API response types
export interface OverviewApiResponse {
  data: OrganizationOverviewStats;
  success: boolean;
  message?: string;
}

// AI Bot Management specific types
export interface BotPerformanceMetrics {
  activeRate: number;
  utilizationScore: number;
  topPerformer: PopularBot | null;
  averageConversationsPerBot: number;
}

export interface TokenAnalytics {
  efficiency: number;
  averagePerConversation: number;
  costPerConversation: number;
  monthlyTrend: number;
}

export interface ActivityInsights {
  peakHour: {
    hour: number;
    formatted: string;
  } | null;
  engagementRate: number;
  dailyGrowth: number;
}

export interface StorageOptimization {
  documentsPercentage: number;
  attachmentsPercentage: number;
  recommendation: 'cleanup' | 'monitor' | 'optimal';
}

// Composable return types
export interface UseOrganizationOverviewReturn {
  // State
  stats: Ref<OrganizationOverviewStats | null>;
  loading: Ref<boolean>;
  error: Ref<string | null>;

  // Actions
  fetchOverviewData: () => Promise<void>;
  refreshData: () => Promise<void>;

  // Basic Computed
  hasData: ComputedRef<boolean>;
  isEmpty: ComputedRef<boolean>;
  botStats: ComputedRef<any>;
  conversationStats: ComputedRef<any>;
  memberStats: ComputedRef<any>;
  storageStats: ComputedRef<any>;
  tokenStats: ComputedRef<any>;
  activityStats: ComputedRef<any>;
  storageUsageColor: ComputedRef<string>;
  isHighActivity: ComputedRef<boolean>;
  totalActiveUsers: ComputedRef<number>;
  conversationGrowth: ComputedRef<number>;

  // AI Bot Management Specific Computed
  botActiveRate: ComputedRef<number>;
  averageTokensPerConversation: ComputedRef<number>;
  topPerformingBot: ComputedRef<PopularBot | null>;
  botUtilizationScore: ComputedRef<number>;
  tokenEfficiency: ComputedRef<number>;
  peakActivityHour: ComputedRef<{hour: number; formatted: string} | null>;
  memberEngagement: ComputedRef<number>;
  storageOptimization: ComputedRef<StorageOptimization | null>;
}

export interface UseOverviewChartsReturn {
  conversationChartRef: Ref<HTMLDivElement | undefined>;
  activityChartRef: Ref<HTMLDivElement | undefined>;
  initCharts: () => Promise<void>;
  resizeCharts: () => void;
  destroyCharts: () => void;
  updateCharts: (stats: OrganizationOverviewStats) => void;
  handleResize: () => void;
}
