[{"path": "/", "name": "Home", "component": "Layout", "redirect": "/welcome", "meta": {"icon": "ep:home-filled", "title": "Home", "rank": 0}, "children": [{"path": "/welcome", "name": "Welcome", "component": "Welcome", "meta": {"title": "Home", "showLink": "VITE_HIDE_HOME !== 'true'"}}]}, {"path": "/auth/sidebars/index", "name": "Sidebar", "component": "Layout", "redirect": "/auth/sidebars", "meta": {"icon": "ep:home-filled", "title": "Sidebars", "rank": 0}, "children": [{"path": "/auth/sidebars", "name": "SidebarIndex", "component": "SidebarIndex", "meta": {"icon": "ri:book-line", "title": "Sidebars"}}]}, {"path": "/auth/organizations/management", "name": "Organization", "component": "Layout", "redirect": "/auth/organizations", "meta": {"icon": "ri:building-line", "title": "Organizations", "rank": 3}, "children": [{"path": "/auth/organizations", "name": "OrganizationIndex", "component": "OrganizationIndex", "meta": {"icon": "ri:building-line", "title": "Organizations", "showLink": true}}, {"path": "/auth/organizations/:id", "name": "OrganizationDetail", "component": "OrganizationDetail", "meta": {"icon": "ri:building-line", "title": "Organization Detail", "showLink": false, "hiddenTag": true, "activePath": "/auth/organizations"}}]}, {"path": "/auth/ai-bots/management", "name": "Bot", "component": "Layout", "redirect": "/auth/ai-bots", "meta": {"icon": "ri:robot-2-line", "title": "AI Bots", "rank": 4}, "children": [{"path": "/auth/ai-bots", "name": "BotIndex", "component": "BotIndex", "meta": {"icon": "ri:robot-2-line", "title": "AI Bots", "showLink": true, "roles": ["super-admin", "admin"]}}, {"path": "/auth/ai-bots/groups", "name": "BotGroup", "component": "BotGroup", "meta": {"icon": "ri:group-line", "title": "Bot Group", "showLink": false, "roles": ["super-admin", "admin"]}}, {"path": "/auth/ai-bots/share", "name": "BotShare", "component": "BotShare", "meta": {"icon": "ri:share-line", "title": "Bot Share With Me", "showLink": false, "roles": ["super-admin", "admin"]}}]}, {"path": "/auth/ai-chat/management", "name": "Cha<PERSON>", "redirect": "/auth/ai-chat", "component": "Layout", "meta": {"icon": "ri:chat-3-line", "title": "AI Chat", "rank": 5}, "children": [{"path": "/auth/ai-chat", "name": "ChatIndex", "component": "@/views/chat/index.vue", "meta": {"icon": "ri:chat-3-line", "title": "AI Chat", "showLink": true}}]}, {"path": "/auth/ai-knowledge/management", "name": "KnowledgeBase", "component": "Layout", "redirect": "/auth/ai-knowledge", "meta": {"icon": "ri:book-line", "title": "AI Knowledge", "rank": 6}, "children": [{"path": "/auth/ai-knowledge", "name": "KnowledgeBaseIndex", "component": "@/views/knowledge-base/index.vue", "meta": {"icon": "ri:book-line", "title": "AI Knowledge", "showLink": true}}]}, {"path": "/auth/ai-models/management", "name": "ModelAI", "component": "Layout", "redirect": "/auth/ai-models", "meta": {"icon": "ri:cpu-line", "title": "AI Models", "rank": 7}, "children": [{"path": "/auth/ai-models", "name": "ModelAIIndex", "component": "@/views/model-ai/ai/index.vue", "meta": {"icon": "ri:cpu-line", "title": "AI Models", "showLink": true}}, {"path": "/auth/ai-models/categories", "name": "ModelCategoryIndex", "component": "@/views/model-ai/category/index.vue", "meta": {"icon": "ri:folder-3-line", "title": "AI Categories", "showLink": true}}, {"path": "/auth/ai-models/provider", "name": "ModelProviderIndex", "component": "@/views/model-ai/provider/index.vue", "meta": {"icon": "ri:cloud-line", "title": "AI Providers", "showLink": true}}]}, {"path": "/auth/systems", "name": "System", "component": "Layout", "redirect": "/auth/systems/settings", "meta": {"icon": "ri:settings-3-line", "title": "System", "rank": 8, "roles": ["super-admin", "admin"]}, "children": [{"path": "/auth/systems/settings", "name": "SettingIndex", "component": "@/views/system/setting/index.vue", "meta": {"icon": "ri:settings-4-line", "title": "Settings", "showLink": true}}, {"path": "/auth/languages", "name": "LanguageIndex", "component": "@/views/system/language/index.vue", "meta": {"icon": "ri:global-line", "title": "Languages", "showLink": true, "auths": ["language:list"]}}, {"path": "/auth/systems/translations", "name": "TranslationIndex", "component": "@/views/system/translation/index.vue", "meta": {"icon": "ri:translate-2", "title": "Translations", "showLink": true}}, {"path": "/auth/systems/users", "name": "UserIndex", "component": "@/views/system/user/index.vue", "meta": {"icon": "ri:user-line", "title": "Users", "showLink": true}}, {"path": "/auth/systems/roles", "name": "RoleIndex", "component": "@/views/system/role/index.vue", "meta": {"icon": "ri:shield-user-line", "title": "Roles", "showLink": true}}]}, {"path": "/error", "redirect": "/error/403", "meta": {"icon": "ri:information-line", "title": "Abnormal page", "rank": 9, "showLink": false}, "children": [{"path": "/error/403", "name": "403", "component": "@/views/error/403.vue", "meta": {"title": "403"}}, {"path": "/error/404", "name": "404", "component": "@/views/error/404.vue", "meta": {"title": "404"}}, {"path": "/error/500", "name": "500", "component": "@/views/error/500.vue", "meta": {"title": "500"}}]}, {"path": "/auth/profile", "name": "Profile", "component": "Layout", "redirect": "/auth/profile/index", "meta": {"icon": "ri:user-settings-line", "title": "Profile", "rank": 999, "showLink": false}, "children": [{"path": "/auth/profile/index", "name": "ProfileIndex", "component": "@/views/auth/profile/index.vue", "meta": {"icon": "ri:user-settings-line", "title": "My Profile", "showLink": false, "hiddenTag": false, "autoCreateTab": true}}]}, {"path": "/public", "name": "Public", "component": "Layout", "redirect": "/", "meta": {"title": "Public Pages", "rank": 999, "showLink": false, "hiddenTag": true}, "children": [{"path": "/invitations/accept/:token", "name": "InvitationAccept", "component": "@/views/organization/modules/invitations/components/InvitationAcceptPage.vue", "meta": {"title": "Accept Invitation", "showLink": false, "hiddenTag": true, "requiresAuth": false}}]}, {"path": "/login", "name": "<PERSON><PERSON>", "component": "@/views/auth/login/index.vue", "meta": {"title": "<PERSON><PERSON>", "showLink": false, "rank": 101}}, {"path": "/register", "name": "Register", "component": "@/views/auth/register/index.vue", "meta": {"title": "Register", "showLink": false, "rank": 102}}, {"path": "/forget", "name": "ForgetPassword", "component": "@/views/auth/forget/index.vue", "meta": {"title": "Forget Password", "showLink": false, "rank": 103}}, {"path": "/reset-password", "name": "ResetPassword", "component": "@/views/auth/reset-password/index.vue", "meta": {"title": "Reset Password", "showLink": false, "rank": 104}}, {"path": "/verify-email", "name": "VerifyEmail", "component": "@/views/auth/verify-email/index.vue", "meta": {"title": "<PERSON><PERSON><PERSON>", "showLink": false, "rank": 105}}, {"path": "/redirect", "component": "Layout", "meta": {"title": "Loading...", "showLink": false, "rank": 106}, "children": [{"path": "/redirect/:path(.*)", "name": "Redirect", "component": "@/layout/redirect.vue"}]}]