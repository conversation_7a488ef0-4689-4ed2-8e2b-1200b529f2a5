<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, ref, h, nextTick } from "vue"; // watch is not used
import { useRoute } from "vue-router";
import { useConvertKeyToCamel } from "@/utils/helpers";
import { getFindMembers } from "../utils/auth-api";
import InviteTable from "@/views/organization/modules/members/components/InviteTable.vue";
import { uuid } from "@pureadmin/utils";

interface User {
  uuid: string;
  firstName: string;
  lastName: string;
  fullName: string;
  email: string;
  role?: string;
}

const props = defineProps<{
  visible: boolean;
  values: FieldValues;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FieldValues): void;
  (e: "submit", values: FieldValues): Promise<boolean>; // Allow parent to signal completion
}>();

const route = useRoute();
const organizationUuid = computed(() => {
  const param = route.params.id;
  return Array.isArray(param) ? param[0] : param;
});

const loading = ref(false);
const formRef = ref();

// Search and table state
const searchLoading = ref(false);
const searchResults = ref<User[]>([]);
const selectedMembers = ref<User[]>([]);
const showMemberTable = ref(false);

// Search users function
const searchUsers = async (query: string) => {
  if (!query || query.length < 2) {
    searchResults.value = [];
    return;
  }

  try {
    searchLoading.value = true;
    const response = await getFindMembers(organizationUuid.value, {
      search: query,
      limit: 20
    });

    if (response.success) {
      const allUsers: User[] = useConvertKeyToCamel(response?.data || []);
      const selectedEmails = new Set(selectedMembers.value.map(m => m.email));
      searchResults.value = allUsers.filter(
        user => !selectedEmails.has(user.email)
      );
    }
  } catch (error) {
    searchResults.value = [];
  } finally {
    searchLoading.value = false;
  }
};

// Handle user selection from autocomplete
const handleUserSelect = (selectedUser: any) => {
  if (!selectedUser) return;
  if (selectedUser) {
    const user = searchResults.value.find(
      user => user.uuid === selectedUser.uuid
    );
    if (user) {
      selectedMembers.value.push(user);
    }
  }

  // Clear autocomplete input using nextTick
  nextTick(() => {
    if (formRef.value?.formInstance) {
      formRef.value.formInstance.resetFields();
    }
  });

  // Clear results after selection
  searchResults.value = [];
};

// Remove user from selection
const handleRemoveUser = (index: number) => {
  selectedMembers.value.splice(index, 1);
  if (selectedMembers.value.length === 0) {
    showMemberTable.value = false;
  }
};

// Update user role
const handleRoleChange = (index: number, newRole: string) => {
  selectedMembers.value[index].role = newRole;
};

const formColumns: PlusColumn[] = [
  {
    label: computed(() => $t("Search Users")),
    prop: "searchUsers",
    valueType: "autocomplete",
    fieldProps: {
      placeholder: $t("Search by name or email..."),
      loading: searchLoading.value,
      clearable: true,
      debounce: 600,
      // @ts-ignore
      fetchSuggestions: async (
        queryString: string,
        cb: (data: any[]) => void
      ) => {
        await searchUsers(queryString);

        const suggestions = searchResults.value.map(user => ({
          value: `${user.fullName} (${user.email})`,
          uuid: user.uuid
        }));
        cb(suggestions);
      },
      onSelect: handleUserSelect
    },
    colProps: { span: 24 }
  },
  {
    hasLabel: false,
    prop: "selectedMembers",
    // @ts-ignore
    valueType: "custom",
    renderField: () => {
      return h(InviteTable, {
        selectedMembers: selectedMembers.value,
        onRemoveUser: handleRemoveUser,
        onRoleChange: handleRoleChange
      });
    }
  }
];

const handleSubmit = async () => {
  if (!formRef.value?.formInstance) return;
  if (selectedMembers.value.length === 0) {
    return;
  }

  loading.value = true;
  try {
    // Prepare a clean payload for submission
    const submitData = {
      invites: selectedMembers.value.map(member => ({
        uuid: member.uuid,
        email: member.email,
        role: member.role || "member"
      }))
    };
    // Await the parent's async operation
    await emit("submit", submitData);
  } catch {
  } finally {
    loading.value = false;
  }
};

const resetForm = () => {
  if (formRef.value?.formInstance) {
    formRef.value.formInstance.resetFields();
  }
  selectedMembers.value = [];
  showMemberTable.value = false;
  searchResults.value = [];
};

defineExpose({
  resetForm
});
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :visible="visible"
    :model-value="values"
    size="40%"
    :closeOnClickModal="true"
    :closeOnPressEscape="true"
    :showClose="true"
    :destroyOnClose="true"
    :form="{
      columns: formColumns,
      footerAlign: 'center',
      hasFooter: false,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 10 }
    }"
    @update:visible="val => emit('update:visible', val)"
    @update:model-value="val => emit('update:values', val)"
    @close="resetForm"
  >
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">
          {{ $t("Information Form") }}
        </span>
      </div>
    </template>
    <template #drawer-footer>
      <div class="custom-footer">
        <el-button plain @click="emit('update:visible', false)">
          {{ $t("Cancel") }}
        </el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ $t("Submit") }}
        </el-button>
      </div>
    </template>
  </PlusDrawerForm>
</template>
