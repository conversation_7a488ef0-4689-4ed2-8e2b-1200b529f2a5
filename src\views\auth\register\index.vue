<script setup lang="ts">
import Motion from "./utils/motion";
import { useRouter } from "vue-router";
import { message } from "@/utils/message";
import { registerRules } from "./utils/rule";
import { computed, reactive, ref, toRaw } from "vue";
import { debounce } from "@pureadmin/utils";
import { useNav } from "@/layout/hooks/useNav";
import { useEventListener } from "@vueuse/core";
import type { FormInstance } from "element-plus";
import { useLayout } from "@/layout/hooks/useLayout";
import { avatar, bg, illustration } from "./utils/static";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useDataThemeChange } from "@/layout/hooks/useDataThemeChange";

import dayIcon from "@/assets/svg/day.svg?component";
import darkIcon from "@/assets/svg/dark.svg?component";
import globalization from "@/assets/svg/globalization.svg?component";
import Lock from "@iconify-icons/ri/lock-fill";
import User from "@iconify-icons/ri/user-3-fill";
import TypeIt from "@/components/ReTypeit/src";
import i18n, { $t } from "@/plugins/i18n";
import { IconifyIconOnline } from "@/components/ReIcon";
import { useLanguageStoreHook } from "@/store/modules/language";
import { useSettingStoreHook } from "@/store/modules/settings";
import { useTranslation } from "@/layout/hooks/useTranslation";
import { registerUser, type UserResult } from "@/views/auth";
import AuthNavigation from "../components/AuthNavigation.vue";
import SocialLogin from "../components/SocialLogin.vue";
import { useUserStoreHook } from "@/store/modules/user";
import { getTopMenu, initRouter } from "@/router/utils";
import { setToken, removeToken } from "@/utils/auth";

defineOptions({
  name: "Register"
});

const useLanguage = useLanguageStoreHook();
const useSetting = useSettingStoreHook();
const translation = useTranslation();

// Ensure settings are loaded if not already
if (Object.keys(useSetting.settings).length === 0) {
  useSetting.fetchPublicSettings();
}

const router = useRouter();
const loading = ref(false);
const disabled = ref(false);
const ruleFormRef = ref<FormInstance>();

const { initStorage } = useLayout();
initStorage();

const { dataTheme, overallStyle, dataThemeChange } = useDataThemeChange();
dataThemeChange(overallStyle.value);
const { title, getDropdownItemStyle, getDropdownItemClass } = useNav();

const ruleForm = reactive({
  username: "",
  email: "",
  firstName: "",
  lastName: "",
  password: "",
  confirmPassword: "",
  acceptTerms: false
});

const languages = computed(() => {
  return useLanguage.languages?.map((language: any) => ({
    locale: language.code,
    native: language.nativeName
  }));
});

const currentLocale = computed(() => {
  return useLanguage.locale || i18n.global.locale.value;
});

const onRegister = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  try {
    await formEl.validate();
    loading.value = true;
    const res: UserResult = await registerUser({
      username: ruleForm.username,
      email: ruleForm.email,
      firstName: ruleForm.firstName,
      lastName: ruleForm.lastName,
      password: ruleForm.password,
      passwordConfirmation: ruleForm.confirmPassword,
      acceptTerms: ruleForm.acceptTerms
    });

    if (!res.success) {
      message(res.message, { type: "error" });
      return;
    }

    message(res.message, { type: "success" });
    // Redirect to verify email page
    await router.push({
      path: "/verify-email",
      query: { email: ruleForm.email }
    });
  } catch (error: any) {
    message(error?.response?.data?.message || error?.message, {
      type: "error"
    });
  } finally {
    loading.value = false;
  }
};

const immediateDebounce: any = debounce(
  formRef => onRegister(formRef),
  1000,
  true
);

const handleSwitch = (locale: string) => {
  translation.switchLanguage(locale);
};

const goToLogin = () => {
  router.push("/login");
};

const handleSocialLogin = async (provider: string) => {
  // This method is kept for backward compatibility
  // The actual social login is now handled by the redirect service
  console.log(`Social login initiated for provider: ${provider}`);
};

const handleSocialLoginInitiated = (provider: string) => {
  console.log(`Social registration initiated for provider: ${provider}`);
  // The redirect will happen automatically, no need to handle here
  // The callback page will handle the authentication flow
};

const handleSocialLoginError = (error: Error) => {
  console.error('Social login error:', error);
  // Error handling is already done in SocialLogin component
  // This is just for additional logging or custom handling if needed
};

useEventListener(document, "keydown", ({ code }) => {
  if (
    ["Enter", "NumpadEnter"].includes(code) &&
    !disabled.value &&
    !loading.value
  )
    immediateDebounce(ruleFormRef.value);
});
</script>

<template>
  <div class="select-none">
    <img :src="bg" class="wave" alt="Bg" />
    <div class="flex-c absolute right-5 top-3">
      <el-switch
        v-model="dataTheme"
        inline-prompt
        :active-icon="dayIcon"
        :inactive-icon="darkIcon"
        @change="dataThemeChange"
      />
      <el-dropdown trigger="click">
        <globalization
          class="hover:text-primary hover:!bg-[transparent] w-[20px] h-[20px] ml-1.5 cursor-pointer outline-none duration-300"
        />
        <template #dropdown>
          <el-dropdown-menu class="translation">
            <el-dropdown-item
              v-for="lang in languages"
              :key="lang.locale"
              :style="getDropdownItemStyle(currentLocale, lang.locale)"
              :class="[
                'dark:!text-white',
                getDropdownItemClass(currentLocale, lang.locale)
              ]"
              @click="handleSwitch(lang.locale)"
            >
              {{ lang.native }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <div class="login-container">
      <div class="img">
        <component :is="toRaw(illustration)" />
      </div>
      <div class="login-box">
        <div class="login-form">
          <avatar class="avatar" />
          <Motion>
            <h2 class="outline-none">
              <TypeIt
                :options="{
                  strings: [$t('Create Account')],
                  cursor: false,
                  speed: 100
                }"
              />
            </h2>
          </Motion>
          <el-form
            ref="ruleFormRef"
            :model="ruleForm"
            :rules="registerRules(ruleForm)"
            size="large"
          >
            <div class="flex gap-4">
              <Motion :delay="100">
                <el-form-item prop="firstName" class="flex-1">
                  <el-input
                    v-model="ruleForm.firstName"
                    clearable
                    :placeholder="$t('First Name')"
                    :prefix-icon="useRenderIcon(User)"
                  />
                </el-form-item>
              </Motion>

              <Motion :delay="120">
                <el-form-item prop="lastName" class="flex-1">
                  <el-input
                    v-model="ruleForm.lastName"
                    clearable
                    :placeholder="$t('Last Name')"
                    :prefix-icon="useRenderIcon(User)"
                  />
                </el-form-item>
              </Motion>
            </div>

            <Motion :delay="150">
              <el-form-item prop="username">
                <el-input
                  v-model="ruleForm.username"
                  clearable
                  :placeholder="$t('Username')"
                  :prefix-icon="useRenderIcon('ri:user-line')"
                />
              </el-form-item>
            </Motion>

            <Motion :delay="180">
              <el-form-item prop="email">
                <el-input
                  v-model="ruleForm.email"
                  clearable
                  :placeholder="$t('Email')"
                  :prefix-icon="useRenderIcon('ri:mail-line')"
                />
              </el-form-item>
            </Motion>

            <Motion :delay="200">
              <el-form-item prop="password">
                <el-input
                  v-model="ruleForm.password"
                  clearable
                  show-password
                  :placeholder="$t('Password')"
                  :prefix-icon="useRenderIcon(Lock)"
                />
              </el-form-item>
            </Motion>

            <Motion :delay="220">
              <el-form-item prop="confirmPassword">
                <el-input
                  v-model="ruleForm.confirmPassword"
                  clearable
                  show-password
                  :placeholder="$t('Confirm Password')"
                  :prefix-icon="useRenderIcon(Lock)"
                />
              </el-form-item>
            </Motion>

            <Motion :delay="250">
              <el-form-item size="small" prop="acceptTerms">
                <el-checkbox v-model="ruleForm.acceptTerms">
                  <div class="flex items-center">
                    {{ $t("I accept the") }}
                    <el-link type="primary" class="ml-1">
                      {{ $t("Terms and Conditions") }}
                    </el-link>
                  </div>
                </el-checkbox>
              </el-form-item>
            </Motion>

            <Motion :delay="280">
              <el-button
                class="w-full !mt-3 !uppercase"
                size="large"
                type="danger"
                round
                :loading="loading"
                :disabled="disabled"
                @click="onRegister(ruleFormRef)"
              >
                <IconifyIconOnline
                  :icon="'ri:user-add-line'"
                  width="20"
                  class="mr-2"
                />
                {{ $t("Create Account") }}
              </el-button>
            </Motion>

            <Motion :delay="350">
              <SocialLogin
                :loading="loading"
                :disabled="disabled"
                login-type="register"
                @social-login="handleSocialLogin"
                @social-login-initiated="handleSocialLoginInitiated"
                @social-login-error="handleSocialLoginError"
              />
            </Motion>

            <Motion :delay="380">
              <AuthNavigation
                :show-login="true"
                :show-register="false"
                :show-forgot-password="false"
              />
            </Motion>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
@import url("@/style/login.css");
</style>

<style lang="scss" scoped>
:deep(.el-input-group__append, .el-input-group__prepend) {
  padding: 0;
}
</style>
