import { http } from "@/utils/http";
import type { Result } from "@/utils/response";
import { useConvertKeyToSnake } from "@/utils/helpers";

// Chat APIs
export const getChatBots = () => {
  return http.request<Result>("get", "/api/v1/auth/chat/bots");
};

// Conversation Management APIs

// 2.1 Get conversations list
export const getConversations = (params?: object) => {
  return http.request<Result>("get", "/api/v1/auth/conversations", {
    params
  });
};

// 2.2 Create new conversation
export const createConversation = (data: object) => {
  return http.request<Result>("post", "/api/v1/auth/conversations", {
    data: useConvertKeyToSnake(data)
  });
};

// 2.3 Get conversation details by UUID
export const getConversationById = (uuid: string) => {
  return http.request<Result>("get", `/api/v1/auth/conversations/${uuid}`);
};

// 2.4 Search conversations
export const searchConversations = (params: {
  query: string;
  per_page?: number;
}) => {
  return http.request<Result>("get", "/api/v1/auth/conversations/search", {
    params
  });
};

// 2.5 Delete conversation by UUID
export const deleteConversation = (uuid: string) => {
  return http.request<Result>("delete", `/api/v1/auth/conversations/${uuid}`);
};

// 2.6 Update conversation
export const updateConversation = (uuid: string, data: object) => {
  return http.request<Result>("put", `/api/v1/auth/conversations/${uuid}`, {
    data: useConvertKeyToSnake(data)
  });
};

// Message Management APIs

// 3.1 Get messages in conversation
export const getMessages = (conversationUuid: string, params?: object) => {
  return http.request<Result>(
    "get",
    `/api/v1/auth/conversations/${conversationUuid}/messages`,
    {
      params
    }
  );
};

// 3.2 Create new message
export const createMessage = (data: {
  conversation_id: number;
  content: string;
  content_type?: string;
}) => {
  return http.request<Result>("post", "/api/v1/auth/messages", {
    data: useConvertKeyToSnake(data)
  });
};

// 3.3 Get message by ID
export const getMessageById = (messageId: number) => {
  return http.request<Result>("get", `/api/v1/auth/messages/${messageId}`);
};

// 3.4 Generate AI response for conversation
export const generateResponse = (
  conversationUuid: string,
  data?: {
    temperature?: number;
    max_tokens?: number;
    stream?: boolean;
  }
) => {
  return http.request<Result>(
    "post",
    `/api/v1/auth/conversations/${conversationUuid}/generate-response`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

// 3.5 Send message and get AI response
export const sendAndRespond = (data: Record<any, any>) => {
  return http.request<Result>(
    "post",
    `/api/v1/auth/conversations/${data.conversationUuid}/messages`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

export const sendGeneralTitleConversation = (id: string) => {
  return http.request<Result>(
    "get",
    `/api/v1/auth/conversations/general-title/${id}`
  );
};

// 3.6 Stream AI response (Server-Sent Events)
export const streamResponse = (
  conversationUuid: string,
  data?: {
    temperature?: number;
    max_tokens?: number;
  }
) => {
  return http.request<Result>(
    "post",
    `/api/v1/auth/conversations/${conversationUuid}/stream-response`,
    {
      data: useConvertKeyToSnake(data),
      headers: {
        Accept: "text/event-stream",
        "Cache-Control": "no-cache"
      }
    }
  );
};

// Legacy Chat APIs (keeping for backward compatibility)
export const getChats = (params?: object) => {
  return http.request<Result>("get", "/api/v1/auth/chats", {
    params
  });
};

export const getChatById = (id: number) => {
  return http.request<Result>("get", `/api/v1/auth/chats/${id}`);
};

export const createChat = (data: object) => {
  return http.request<Result>("post", "/api/v1/auth/chats", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateChatById = (id: number, data: object) => {
  return http.request<Result>("put", `/api/v1/auth/chats/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

export const deleteChatById = (id: number) => {
  return http.request<Result>("delete", `/api/v1/auth/chats/${id}`);
};

export const bulkDeleteChats = (ids: number[]) => {
  return http.request<Result>("delete", "/api/v1/auth/chats/bulk", {
    data: { ids }
  });
};

export const destroyChatById = (id: number) => {
  return http.request<Result>("delete", `/api/v1/auth/chats/${id}/force`);
};

export const bulkDestroyChats = (ids: number[]) => {
  return http.request<Result>("delete", "/api/v1/auth/chats/bulk/force", {
    data: { ids }
  });
};

export const restoreChatById = (id: number) => {
  return http.request<Result>("post", `/api/v1/auth/chats/${id}/restore`);
};

export const bulkRestoreChats = (ids: number[]) => {
  return http.request<Result>("post", "/api/v1/auth/chats/bulk/restore", {
    data: { ids }
  });
};

// Get Users for dropdown
export const getUsers = () => {
  return http.request<Result>("get", "/api/v1/auth/users");
};

// Get Bots for dropdown
export const getBots = () => {
  return http.request<Result>("get", "/api/v1/auth/bots");
};
