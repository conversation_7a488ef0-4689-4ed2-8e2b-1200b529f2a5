<script setup lang="ts">
import { ref, onMounted, computed, defineAsyncComponent } from "vue";
import { useRoute } from "vue-router";
import { $t } from "@/plugins/i18n";
import { IconifyIconOnline } from "@/components/ReIcon";
import { ElCard, ElMenu, ElMenuItem, ElRow, ElCol } from "element-plus";
import { useSettingsHook } from "./utils/hook";

// Lazy load setting components
const GeneralSettings = defineAsyncComponent(
  () => import("./components/GeneralSettings.vue")
);
const MemberSettings = defineAsyncComponent(
  () => import("./components/MemberSettings.vue")
);
const BillingSettings = defineAsyncComponent(
  () => import("./components/BillingSettings.vue")
);

const route = useRoute();
const activeSection = ref("general");

const organizationId = computed(() => route.params.id as string);

// Settings hook for data management
const { loading, settings, fnGetSettings, handleSaveSettings, handleResetSettings } =
  useSettingsHook(organizationId.value);

// Settings menu items
const settingsMenu = [
  {
    key: "general",
    title: "General Settings",
    icon: "ri:settings-3-line",
    description: "Basic information and customization"
  },
  {
    key: "members",
    title: "Member Settings",
    icon: "ri:team-line",
    description: "Manage roles and permissions"
  },
  {
    key: "billing",
    title: "Billing & Usage",
    icon: "ri:money-dollar-circle-line",
    description: "Plans and usage tracking"
  }
];

const handleMenuSelect = (key: string) => {
  activeSection.value = key;
};

const getCurrentComponent = () => {
  const componentMap = {
    general: GeneralSettings,
    members: MemberSettings,
    billing: BillingSettings
  };

  return componentMap[activeSection.value] || GeneralSettings;
};

const getCurrentMenuInfo = computed(() => {
  return (
    settingsMenu.find(item => item.key === activeSection.value) ||
    settingsMenu[0]
  );
});

onMounted(() => {
  fnGetSettings();
});
</script>

<template>
  <div class="settings-management">
    <el-row :gutter="24" class="h-full">
      <!-- Settings Menu Sidebar -->
      <el-col :xs="24" :sm="8" :md="6" :lg="5">
        <el-card shadow="never" class="settings-menu-card h-full">
          <template #header>
            <div class="flex items-center">
              <IconifyIconOnline
                icon="ri:settings-4-line"
                class="text-lg text-gray-600 mr-2"
              />
              <span class="font-semibold text-gray-800">{{
                $t("Settings")
              }}</span>
            </div>
          </template>

          <el-menu
            :default-active="activeSection"
            class="settings-menu"
            @select="handleMenuSelect"
          >
            <el-menu-item
              v-for="item in settingsMenu"
              :key="item.key"
              :index="item.key"
              class="settings-menu-item"
            >
              <div class="flex items-start w-full">
                <IconifyIconOnline
                  :icon="item.icon"
                  class="text-lg mr-3 mt-0.5 flex-shrink-0"
                />
                <div class="flex-1 min-w-0">
                  <div class="font-medium text-sm">{{ item.title }}</div>
                  <div class="text-xs text-gray-500 mt-1 line-clamp-2">
                    {{ item.description }}
                  </div>
                </div>
              </div>
            </el-menu-item>
          </el-menu>
        </el-card>
      </el-col>

      <!-- Settings Content -->
      <el-col :xs="24" :sm="16" :md="18" :lg="19">
        <el-card shadow="never" class="settings-content-card h-full">
          <template #header>
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <IconifyIconOnline
                  :icon="getCurrentMenuInfo.icon"
                  class="text-xl text-primary mr-3"
                />
                <div>
                  <h2 class="text-lg font-semibold text-gray-800 m-0">
                    {{ getCurrentMenuInfo.title }}
                  </h2>
                  <p class="text-sm text-gray-600 m-0 mt-1">
                    {{ getCurrentMenuInfo.description }}
                  </p>
                </div>
              </div>

              <div class="flex items-center space-x-2">
                <el-button
                  size="small"
                  :loading="loading"
                  @click="fnGetSettings"
                >
                  <IconifyIconOnline icon="ri:refresh-line" class="mr-1" />
                  Refresh
                </el-button>
              </div>
            </div>
          </template>

          <!-- Dynamic Settings Component -->
          <div class="settings-content">
            <component
              :is="getCurrentComponent()"
              :organization-id="organizationId"
              :settings="settings"
              :loading="loading"
              @save="handleSaveSettings"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<style scoped>
.settings-management {
  padding: 24px;
  background-color: #f8fafc;
  min-height: 100vh;
}

.settings-menu-card,
.settings-content-card {
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  min-height: calc(100vh - 200px);
}

.settings-menu {
  border: none;
  background: transparent;
}

.settings-menu-item {
  height: auto !important;
  padding: 16px 12px !important;
  margin-bottom: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.settings-menu-item:hover {
  background-color: #f1f5f9;
}

.settings-menu-item.is-active {
  background-color: #eff6ff;
  border-right: 3px solid #3b82f6;
  color: #3b82f6;
}

.settings-menu-item.is-active .text-gray-500 {
  color: #6b7280;
}

.settings-content {
  padding: 20px 0;
  max-height: calc(100vh - 300px);
  overflow-y: auto;
}

/* Custom scrollbar */
.settings-content::-webkit-scrollbar {
  width: 6px;
}

.settings-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.settings-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.settings-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .settings-management {
    padding: 16px;
  }

  .settings-menu-item {
    padding: 12px 8px !important;
  }

  .settings-content {
    max-height: none;
    padding: 16px 0;
  }
}

/* Line clamp utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
