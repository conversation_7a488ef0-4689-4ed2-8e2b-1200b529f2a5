/**
 * Social Login Test Utilities
 * For testing redirect-based social authentication flow
 */

import { socialAuthService } from '@/services/social-auth.service';
import { FormStateManager } from '@/utils/form-state-manager';

export interface TestResult {
  success: boolean;
  message: string;
  details?: any;
}

export class SocialLoginTester {
  
  /**
   * Test social auth service functionality
   */
  static async testSocialAuthService(): Promise<TestResult> {
    try {
      console.log('Testing Social Auth Service...');
      
      // Test state saving and restoring
      const testState = {
        returnUrl: '/test-page',
        timestamp: Date.now(),
        formData: { username: '<EMAIL>' },
        provider: 'google',
        loginType: 'login' as const
      };
      
      // Save state
      socialAuthService['saveCurrentState'](testState);
      
      // Restore state
      const restoredState = socialAuthService.restoreState();
      
      if (!restoredState) {
        return {
          success: false,
          message: 'Failed to restore saved state'
        };
      }
      
      // Verify state data
      if (restoredState.returnUrl !== testState.returnUrl) {
        return {
          success: false,
          message: 'State data mismatch',
          details: { expected: testState, actual: restoredState }
        };
      }
      
      // Clean up
      socialAuthService.clearState();
      
      return {
        success: true,
        message: 'Social Auth Service test passed'
      };
      
    } catch (error: any) {
      return {
        success: false,
        message: `Social Auth Service test failed: ${error.message}`,
        details: error
      };
    }
  }
  
  /**
   * Test form state manager functionality
   */
  static async testFormStateManager(): Promise<TestResult> {
    try {
      console.log('Testing Form State Manager...');
      
      // Create test form
      const testForm = document.createElement('form');
      const testInput = document.createElement('input');
      testInput.name = 'test-field';
      testInput.value = 'test-value';
      testForm.appendChild(testInput);
      document.body.appendChild(testForm);
      
      // Test form data extraction
      const formData = FormStateManager.extractFormData();
      
      if (!formData || formData['test-field'] !== 'test-value') {
        return {
          success: false,
          message: 'Failed to extract form data',
          details: formData
        };
      }
      
      // Test state saving and restoring
      FormStateManager.saveFormState(formData, '/test');
      const restoredState = FormStateManager.restoreFormState();
      
      if (!restoredState) {
        return {
          success: false,
          message: 'Failed to restore form state'
        };
      }
      
      // Clean up
      document.body.removeChild(testForm);
      FormStateManager.clearFormState();
      
      return {
        success: true,
        message: 'Form State Manager test passed'
      };
      
    } catch (error: any) {
      return {
        success: false,
        message: `Form State Manager test failed: ${error.message}`,
        details: error
      };
    }
  }
  
  /**
   * Test callback URL processing
   */
  static testCallbackProcessing(): TestResult {
    try {
      console.log('Testing Callback Processing...');
      
      // Test successful callback
      const successParams = new URLSearchParams('?token=test-token&provider=google');
      const successResult = socialAuthService.processCallback(successParams);
      
      if (!successResult.success || successResult.token !== 'test-token') {
        return {
          success: false,
          message: 'Failed to process successful callback',
          details: successResult
        };
      }
      
      // Test error callback
      const errorParams = new URLSearchParams('?error=access_denied&provider=facebook');
      const errorResult = socialAuthService.processCallback(errorParams);
      
      if (errorResult.success || !errorResult.error) {
        return {
          success: false,
          message: 'Failed to process error callback',
          details: errorResult
        };
      }
      
      return {
        success: true,
        message: 'Callback processing test passed'
      };
      
    } catch (error: any) {
      return {
        success: false,
        message: `Callback processing test failed: ${error.message}`,
        details: error
      };
    }
  }
  
  /**
   * Test URL building
   */
  static testUrlBuilding(): TestResult {
    try {
      console.log('Testing URL Building...');
      
      // Test social login URL building
      const service = new (socialAuthService.constructor as any)();
      const url = service.buildSocialLoginUrl('google');
      
      if (!url.includes('/auth/social/google')) {
        return {
          success: false,
          message: 'Invalid social login URL',
          details: { url }
        };
      }
      
      if (!url.includes('redirect_url=')) {
        return {
          success: false,
          message: 'Missing redirect_url parameter',
          details: { url }
        };
      }
      
      return {
        success: true,
        message: 'URL building test passed'
      };
      
    } catch (error: any) {
      return {
        success: false,
        message: `URL building test failed: ${error.message}`,
        details: error
      };
    }
  }
  
  /**
   * Run all tests
   */
  static async runAllTests(): Promise<TestResult[]> {
    console.log('Running Social Login Tests...');
    
    const tests = [
      this.testSocialAuthService(),
      this.testFormStateManager(),
      this.testCallbackProcessing(),
      this.testUrlBuilding()
    ];
    
    const results = await Promise.all(tests);
    
    const passedTests = results.filter(r => r.success).length;
    const totalTests = results.length;
    
    console.log(`Tests completed: ${passedTests}/${totalTests} passed`);
    
    results.forEach((result, index) => {
      const status = result.success ? '✅' : '❌';
      console.log(`${status} Test ${index + 1}: ${result.message}`);
      if (!result.success && result.details) {
        console.error('Details:', result.details);
      }
    });
    
    return results;
  }
  
  /**
   * Simulate social login flow (for development testing)
   */
  static simulateSocialLogin(provider: string = 'google'): void {
    console.log(`Simulating social login with ${provider}...`);
    
    try {
      // Create test form data
      const testForm = document.createElement('form');
      const emailInput = document.createElement('input');
      emailInput.name = 'email';
      emailInput.value = '<EMAIL>';
      testForm.appendChild(emailInput);
      document.body.appendChild(testForm);
      
      // Simulate social login initiation
      socialAuthService.initiateSocialLogin(provider, {
        returnUrl: window.location.pathname,
        loginType: 'login',
        formData: { email: '<EMAIL>' }
      });
      
      // Clean up
      document.body.removeChild(testForm);
      
    } catch (error) {
      console.error('Simulation failed:', error);
    }
  }
  
  /**
   * Check if current environment supports social login
   */
  static checkEnvironment(): TestResult {
    const issues: string[] = [];
    
    // Check required APIs
    if (typeof sessionStorage === 'undefined') {
      issues.push('sessionStorage not available');
    }
    
    if (typeof URLSearchParams === 'undefined') {
      issues.push('URLSearchParams not available');
    }
    
    // Check environment variables
    if (!import.meta.env.VITE_API_BASE_URL) {
      issues.push('VITE_API_BASE_URL not configured');
    }
    
    // Check DOM APIs
    if (typeof document === 'undefined') {
      issues.push('DOM not available (SSR environment)');
    }
    
    if (issues.length > 0) {
      return {
        success: false,
        message: 'Environment check failed',
        details: issues
      };
    }
    
    return {
      success: true,
      message: 'Environment check passed'
    };
  }
}

// Export for console testing
if (typeof window !== 'undefined') {
  (window as any).SocialLoginTester = SocialLoginTester;
  console.log('SocialLoginTester available in console for testing');
}
