import { http } from "@/utils/http";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { Result } from "@/utils/response";

type r = Result;

// Read Operations
export const getMembers = (uuid: string, params?: any) => {
  return http.request<r>("get", `/api/v1/auth/organizations/${uuid}/members`, {
    params
  });
};

export const getFindMembers = (uuid: string, params?: any) => {
  return http.request<r>(
    "get",
    `/api/v1/auth/organizations/${uuid}/members/find`,
    { params }
  );
};

export const getAllMembers = (uuid: string, params?: any) => {
  return http.request<r>(
    "get",
    `/api/v1/auth/organizations/${uuid}/members/all`,
    { params }
  );
};

// Create Operations
export const createMember = (uuid: string, data: any) => {
  return http.request<r>("post", `/api/v1/auth/organizations/${uuid}/members`, {
    data: useConvertKeyToSnake(data)
  });
};

// Bulk Role Update Operations
export const bulkUpdateMemberRole = (
  uuid: string,
  data: object
) => {
  return http.request<r>(
    "patch",
    `/api/v1/auth/organizations/${uuid}/members/bulk/update-role`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

// Permanent Delete Operations
export const deleteMemberPermanent = (uuid: string, id: number) => {
  return http.request<r>(
    "delete",
    `/api/v1/auth/organizations/${uuid}/members/${id}/force`
  );
};

export const bulkDeleteMembersPermanent = (
  uuid: string,
  data: { ids: number[] }
) => {
  return http.request<r>(
    "delete",
    `/api/v1/auth/organizations/${uuid}/members/bulk/force`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};
