<script setup lang="ts">
import { ref, computed } from "vue";
import { $t } from "@/plugins/i18n";
import { PlusDrawerForm, type PlusColumn } from "plus-pro-components";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { FilterProps } from "../utils/type";

const props = defineProps<{
  visible: boolean;
  values: FilterProps;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FilterProps): void;
  (e: "submit", values: FilterProps): void;
  (e: "reset"): void;
}>();

const formRef = ref();
const loading = ref(false);

const formColumns: PlusColumn[] = [
  {
    label: computed(() => $t("Title")),
    prop: "title",
    valueType: "input",
    fieldProps: {
      placeholder: "",
      clearable: true
    },
    colProps: { span: 24 }
  },
  {
    label: computed(() => $t("Route Name")),
    prop: "name",
    valueType: "input",
    fieldProps: {
      placeholder: "",
      clearable: true
    },
    colProps: { span: 24 }
  },
  {
    label: computed(() => $t("Route Path")),
    prop: "path",
    valueType: "input",
    fieldProps: {
      placeholder: "",
      clearable: true
    },
    colProps: { span: 24 }
  },
  {
    label: computed(() => $t("Status")),
    prop: "status",
    valueType: "select",
    fieldProps: {
      placeholder: "",
      clearable: true
    },
    options: [
      { label: $t("Active"), value: "active" },
      { label: $t("Inactive"), value: "inactive" }
    ],
    colProps: { span: 24 }
  },
  {
    label: computed(() => $t("Trashed")),
    prop: "isTrashed",
    valueType: "select",
    fieldProps: {
      placeholder: "",
      clearable: true
    },
    options: [
      { label: $t("Yes"), value: "yes" },
      { label: $t("No"), value: "no" }
    ],
    colProps: { span: 24 }
  }
];

const handleSubmit = async (values: FilterProps) => {
  try {
    loading.value = true;
    emit("submit", values);
  } finally {
    loading.value = false;
  }
};

const handleReset = () => {
  resetForm();
  emit("reset");
};

const resetForm = () => {
  if (formRef.value?.formInstance) {
    formRef.value.formInstance.resetFields();
  }
};

defineExpose({
  resetForm
});
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :visible="visible"
    :model-value="values"
    size="30%"
    :closeOnClickModal="true"
    :closeOnPressEscape="true"
    :showClose="true"
    :destroyOnClose="true"
    :form="{
      columns: formColumns,
      footerAlign: 'center',
      hasFooter: false,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 10 }
    }"
    @update:visible="val => emit('update:visible', val)"
    @update:model-value="val => emit('update:values', val)"
  >
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">
          {{ $t("Filter Sidebar") }}
        </span>
      </div>
    </template>
    <template #drawer-footer>
      <div class="custom-footer">
        <el-button plain @click="handleReset">
          {{ $t("Reset") }}
        </el-button>
        <el-button
          plain
          type="primary"
          :loading="loading"
          @click="handleSubmit(values)"
        >
          {{ $t("Apply Filter") }}
        </el-button>
      </div>
    </template>
  </PlusDrawerForm>
</template>
