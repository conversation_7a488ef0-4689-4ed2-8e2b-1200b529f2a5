export type FormItemProps = {
  id?: number | null;
  name?: string;
  description?: string;
  website?: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  timezone?: string;
  logo?: string | File;
  logoUrl?: string;
  status?: "active" | "inactive" | "suspended";
  organizationType?: "company" | "nonprofit" | "government" | "educational";
  industry?: string;
  employeeCount?: number;
  foundedYear?: number;
  isVerified?: boolean;
  settings?: Record<string, any>;
  metadata?: Record<string, any>;
};

export interface OrganizationOverviewStats {
  bots: {
    total: number;
    active: number;
    draft: number;
    mostUsedBot?: string;
  };
  conversations: {
    total: number;
    today: number;
    thisWeek: number;
    thisMonth: number;
    avgConversationLength: number;
  };
  members: {
    total: number;
    active: number;
    pending: number;
    admins: number;
  };
  storage: {
    totalUsedMB: number;
    documentsSizeMB: number;
    attachmentsSizeMB: number;
    remainingQuotaMB: number;
    quotaLimitMB: number;
    usagePercent: number;
  };
  tokens: {
    totalUsed: number;
    thisMonth: number;
    estimatedCost: string;
  };
  activity: {
    lastActiveAt?: string;
    peakHours: number[];
    popularBots: Array<{
      name: string;
      usage: number;
    }>;
  };
}

export interface OrganizationOverviewProps {
  organizationId: string;
  stats: OrganizationOverviewStats;
  loading?: boolean;
}

export type OrganizationFilterProps = {
  name?: string;
  email?: string;
  status?: "active" | "inactive" | "suspended";
  organizationType?: "company" | "nonprofit" | "government" | "educational";
  industry?: string;
  country?: string;
  isVerified?: boolean;
  isTrashed?: "yes" | "no";
  [key: string]: any;
};
