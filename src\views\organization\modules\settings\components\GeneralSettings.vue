<script setup lang="ts">
import { ref, computed } from "vue";
import { $t } from "@/plugins/i18n";
import {
  ElForm,
  ElFormItem,
  ElInput,
  ElTextarea,
  ElSelect,
  ElOption,
  ElSwitch,
  ElButton,
  ElCard,
  ElUpload,
  ElAvatar,
  ElMessage
} from "element-plus";
import type { UploadProps } from "element-plus";

const props = defineProps<{
  settings: any;
  loading: boolean;
}>();

const emit = defineEmits<{
  (e: "save", settings: any): void;
}>();

const formRef = ref();
const uploading = ref(false);

const formData = ref({
  name: "",
  description: "",
  website: "",
  industry: "",
  size: "",
  timezone: "",
  language: "en",
  logoUrl: "",
  isPublic: false,
  allowGuestAccess: true,
  ...props.settings
});

const industries = [
  { label: "Technology", value: "technology" },
  { label: "Healthcare", value: "healthcare" },
  { label: "Finance", value: "finance" },
  { label: "Education", value: "education" },
  { label: "Retail", value: "retail" },
  { label: "Manufacturing", value: "manufacturing" },
  { label: "Other", value: "other" }
];

const organizationSizes = [
  { label: "1-10 employees", value: "1-10" },
  { label: "11-50 employees", value: "11-50" },
  { label: "51-200 employees", value: "51-200" },
  { label: "201-500 employees", value: "201-500" },
  { label: "500+ employees", value: "500+" }
];

const timezones = [
  { label: "UTC", value: "UTC" },
  { label: "America/New_York", value: "America/New_York" },
  { label: "America/Los_Angeles", value: "America/Los_Angeles" },
  { label: "Europe/London", value: "Europe/London" },
  { label: "Europe/Paris", value: "Europe/Paris" },
  { label: "Asia/Tokyo", value: "Asia/Tokyo" },
  { label: "Asia/Shanghai", value: "Asia/Shanghai" }
];

const languages = [
  { label: "English", value: "en" },
  { label: "Español", value: "es" },
  { label: "Français", value: "fr" },
  { label: "Deutsch", value: "de" },
  { label: "中文", value: "zh" },
  { label: "日本語", value: "ja" }
];

const rules = {
  name: [
    {
      required: true,
      message: "Please input organization name",
      trigger: "blur"
    },
    {
      min: 2,
      max: 100,
      message: "Length should be between 2 and 100 characters",
      trigger: "blur"
    }
  ],
  industry: [
    { required: true, message: "Please select industry", trigger: "change" }
  ],
  size: [
    {
      required: true,
      message: "Please select organization size",
      trigger: "change"
    }
  ]
};

const handleLogoUpload = (file: File) => {
  const isImage = file.type.startsWith("image/");
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isImage) {
    ElMessage.error("Logo must be an image file");
    return false;
  }
  if (!isLt2M) {
    ElMessage.error("Logo size must be less than 2MB");
    return false;
  }

  uploading.value = true;

  // Simulate upload
  setTimeout(() => {
    formData.value.logoUrl = URL.createObjectURL(file);
    uploading.value = false;
    ElMessage.success($t("Upload successful"));
  }, 1000);

  return false; // Prevent auto upload
};

const handleSave = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    emit("save", formData.value);
  } catch (error) {
    console.error("Validation failed:", error);
  }
};

const handleReset = () => {
  formData.value = {
    name: "",
    description: "",
    website: "",
    industry: "",
    size: "",
    timezone: "",
    language: "en",
    logoUrl: "",
    isPublic: false,
    allowGuestAccess: true,
    ...props.settings
  };
};
</script>

<template>
  <ElCard>
    <template #header>
      <div class="flex items-center justify-between">
        <span class="text-lg font-semibold">{{ $t("Settings") }}</span>
        <div class="space-x-2">
          <ElButton @click="handleReset">{{ $t("Reset") }}</ElButton>
          <ElButton type="primary" :loading="loading" @click="handleSave">
            {{ $t("Save") }}
          </ElButton>
        </div>
      </div>
    </template>

    <ElForm
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="140px"
      label-position="left"
    >
      <!-- Logo Upload -->
      <ElFormItem label="Organization Logo">
        <div class="flex items-center space-x-4">
          <ElAvatar
            :size="80"
            :src="formData.logoUrl"
            :alt="formData.name"
            class="border-2 border-gray-200"
          >
            {{ formData.name?.charAt(0)?.toUpperCase() || "O" }}
          </ElAvatar>
          <ElUpload
            :show-file-list="false"
            :before-upload="handleLogoUpload"
            :loading="uploading"
          >
            <ElButton :loading="uploading">
              {{ uploading ? "Uploading..." : "Upload Logo" }}
            </ElButton>
          </ElUpload>
        </div>
      </ElFormItem>

      <!-- Basic Information -->
      <ElFormItem label="Organization Name" prop="name">
        <ElInput
          v-model="formData.name"
          placeholder="Enter organization name"
          maxlength="100"
          show-word-limit
        />
      </ElFormItem>

      <ElFormItem label="Description">
        <ElTextarea
          v-model="formData.description"
          placeholder="Enter organization description"
          :rows="3"
          maxlength="500"
          show-word-limit
        />
      </ElFormItem>

      <ElFormItem label="Website">
        <ElInput
          v-model="formData.website"
          placeholder="Enter website URL"
          type="url"
        />
      </ElFormItem>

      <!-- Organization Details -->
      <ElFormItem :label="$t('Industry')" prop="industry">
        <ElSelect
          v-model="formData.industry"
          :placeholder="$t('Select industry')"
          style="width: 100%"
        >
          <ElOption
            v-for="item in industries"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </ElSelect>
      </ElFormItem>

      <ElFormItem :label="$t('Organization Size')" prop="size">
        <ElSelect
          v-model="formData.size"
          :placeholder="$t('Select organization size')"
          style="width: 100%"
        >
          <ElOption
            v-for="item in organizationSizes"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </ElSelect>
      </ElFormItem>

      <!-- Localization -->
      <ElFormItem :label="$t('Timezone')">
        <ElSelect
          v-model="formData.timezone"
          :placeholder="$t('Select timezone')"
          style="width: 100%"
          filterable
        >
          <ElOption
            v-for="item in timezones"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </ElSelect>
      </ElFormItem>

      <ElFormItem :label="$t('Language')">
        <ElSelect
          v-model="formData.language"
          :placeholder="$t('Select language')"
          style="width: 100%"
        >
          <ElOption
            v-for="item in languages"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </ElSelect>
      </ElFormItem>

      <!-- Privacy Settings -->
      <ElFormItem :label="$t('Public Organization')">
        <ElSwitch
          v-model="formData.isPublic"
          :active-text="$t('Yes')"
          :inactive-text="$t('No')"
        />
        <div class="text-sm text-gray-500 mt-1">
          {{ $t("Allow others to discover this organization") }}
        </div>
      </ElFormItem>

      <ElFormItem :label="$t('Guest Access')">
        <ElSwitch
          v-model="formData.allowGuestAccess"
          :active-text="$t('Enabled')"
          :inactive-text="$t('Disabled')"
        />
        <div class="text-sm text-gray-500 mt-1">
          {{ $t("Allow guest users to access organization resources") }}
        </div>
      </ElFormItem>
    </ElForm>
  </ElCard>
</template>

<style scoped>
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
}

:deep(.el-card__body) {
  padding: 24px;
}
</style>
