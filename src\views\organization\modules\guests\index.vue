<script setup lang="ts">
import { ref, nextTick, onMounted, defineAsyncComponent, computed } from "vue";
import { useGuestHook } from "./utils/hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { columns } from "./utils/columns";
import { deviceDetection } from "@pureadmin/utils";
import { $t } from "@/plugins/i18n";
import PureTable from "@pureadmin/table";
import { hasAuth } from "@/router/utils";
import { IconifyIconOnline } from "@/components/ReIcon";

// Props definition
const props = defineProps<{
  userRole: string; // 'admin' | 'owner' | 'member' | 'guest'
}>();

// Role-based permissions
const canCreateGuest = computed(() => {
  return ["admin", "owner"].includes(props.userRole);
});

const canEditGuest = computed(() => {
  return ["admin", "owner"].includes(props.userRole);
});

const canDeleteGuest = computed(() => {
  return ["admin", "owner"].includes(props.userRole);
});

const canViewGuest = computed(() => {
  return true; // All roles can view guests
});

// Lazy load components
const GuestDrawerForm = defineAsyncComponent(
  () => import("./components/GuestDrawerForm.vue")
);

const GuestFilterForm = defineAsyncComponent(
  () => import("./components/GuestFilterForm.vue")
);

const tableRef = ref();
const contentRef = ref();

const {
  // Data/State
  loading,
  filterRef,
  pagination,
  records,
  multipleSelection,
  // Event handlers
  fnGetGuests,
  fnHandlePageChange,
  fnHandleSelectionChange,
  fnHandleSortChange,
  fnHandleSizeChange,
  // Form related
  filterVisible,
  drawerVisible,
  drawerValues,
  guestFormRef,
  handleSubmit,
  handleFilter,
  handleBulkPermanentDelete,
  handlePermanentDelete
} = useGuestHook();

onMounted(() => {
  nextTick(() => {
    fnGetGuests();
  });
});
</script>

<template>
  <div class="main">
    <div
      ref="contentRef"
      :class="['flex', deviceDetection() ? 'flex-wrap' : '']"
    >
      <PureTableBar
        class="!w-full"
        style="transition: width 220ms cubic-bezier(0.4, 0, 0.2, 1)"
        :title="$t('Guest Management')"
        :columns="columns"
        @refresh="fnGetGuests"
        @filter="filterVisible = true"
      >
        <template #buttons>
          <el-tooltip :content="$t('Create new')" placement="top">
            <el-button
              type="text"
              class="font-bold text-[16px]"
              :disabled="!canCreateGuest"
              @click="
                () => {
                  drawerVisible = true;
                }
              "
            >
              <IconifyIconOnline
                :icon="canCreateGuest ? 'flat-color-icons:plus' : 'icons8:plus'"
                width="18px"
              />
            </el-button>
          </el-tooltip>
          <el-tooltip :content="$t('Bulk Destroy')" placement="top">
            <el-button
              type="text"
              class="font-bold text-[16px]"
              :disabled="
                multipleSelection.length === 0 ||
                (multipleSelection.length > 0 && !canDeleteGuest)
              "
              @click="() => handleBulkPermanentDelete()"
            >
              <IconifyIconOnline
                icon="tabler:trash-x-filled"
                width="18px"
                :class="{
                  'text-red-700': multipleSelection.length > 0 && canDeleteGuest
                }"
              />
            </el-button>
          </el-tooltip>
        </template>
        <template v-slot="{ size, dynamicColumns }">
          <PureTable
            ref="tableRef"
            align-whole="center"
            table-layout="auto"
            :loading="loading"
            :size="size"
            adaptive
            :adaptiveConfig="{ offsetBottom: 108 }"
            :data="records"
            :columns="dynamicColumns"
            :pagination="pagination"
            :header-cell-style="{
              background: 'var(--el-fill-color-light)',
              color: 'var(--el-text-color-primary)'
            }"
            @sort-change="fnHandleSortChange"
            @page-size-change="fnHandleSizeChange"
            @page-current-change="fnHandlePageChange"
            @selection-change="fnHandleSelectionChange"
          >
            <template #operation="{ row }">
              <el-dropdown
                split-button
                trigger="click"
                size="small"
                style="min-width: 110px"
              >
                {{ $t("Action") }}
                <template #dropdown>
                  <el-dropdown-menu class="min-w-[130px]">
                    <el-dropdown-item disabled>
                      {{ $t("Action") }}
                    </el-dropdown-item>
                    <el-dropdown-item
                      :disabled="!canDeleteGuest"
                      @click="handlePermanentDelete(row)"
                    >
                      <IconifyIconOnline
                        icon="tabler:trash-x"
                        class="text-red-800"
                      />
                      <span class="ml-2">
                        {{ $t("Destroy") }}
                      </span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </PureTable>
        </template>
      </PureTableBar>
    </div>

    <GuestDrawerForm
      ref="guestFormRef"
      v-model:visible="drawerVisible"
      v-model:values="drawerValues"
      @submit="handleSubmit"
      @close="
        () => {
          guestFormRef?.resetForm();
          drawerValues = {
            status: 'active'
          };
        }
      "
    />

    <GuestFilterForm
      ref="filterFormRef"
      v-model:visible="filterVisible"
      v-model:values="filterRef"
      @submit="handleFilter"
      @reset="
        () => {
          filterRef = { isTrashed: 'no' };
          fnGetGuests();
        }
      "
    />
  </div>
</template>

<style lang="scss" scoped>
.main {
  @apply p-4;
}
</style>
