import { http } from "@/utils/http";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { Result } from "@/utils/response";

type r = Result;

/*
 ***************************
 *   Read Operations (GET)
 ***************************
 */
export const getBots = (uuid: string, params?: any) => {
  return http.request<r>("get", `/api/v1/auth/organizations/${uuid}/bots`, {
    params
  });
};

export const getBotById = (uuid: string, id: string) => {
  return http.request<r>(
    "get",
    `/api/v1/auth/organizations/${uuid}/bots/${id}`
  );
};

export const getBotsDropdown = (uuid: string) => {
  return http.request<r>(
    "get",
    `/api/v1/auth/organizations/${uuid}/bots/dropdown`
  );
};

/*
 ***************************
 *   File Upload Operations
 ***************************
 */
export const uploadBotAvatar = (uuid: string, file: File) => {
  const formData = new FormData();
  formData.append("avatar", file);

  return http.request<r>(
    "post",
    `/api/v1/auth/organizations/${uuid}/bots/upload-avatar`,
    {
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data"
      }
    }
  );
};

/*
 ***************************
 *   Create & Update Operations
 ***************************
 */
export const createBot = (uuid: string, data: any) => {
  return http.request<r>("post", `/api/v1/auth/organizations/${uuid}/bots`, {
    data: useConvertKeyToSnake(data)
  });
};

export const updateBotById = (uuid: string, id: string, data: any) => {
  return http.request<r>(
    "put",
    `/api/v1/auth/organizations/${uuid}/bots/${id}`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

export const partialUpdateBotById = (uuid: string, id: string, data: any) => {
  return http.request<r>(
    "patch",
    `/api/v1/auth/organizations/${uuid}/bots/${id}`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

/*
 ***************************
 *   Bot Specific Operations
 ***************************
 */
export const duplicateBot = (uuid: string, id: string) => {
  return http.request<r>(
    "post",
    `/api/v1/auth/organizations/${uuid}/bots/${id}/duplicate`
  );
};

export const toggleBotStatus = (uuid: string, id: string) => {
  return http.request<r>(
    "post",
    `/api/v1/auth/organizations/${uuid}/bots/${id}/toggle-status`
  );
};

export const bulkDuplicateBots = (uuid: string, data: { ids: string[] }) => {
  return http.request<r>(
    "post",
    `/api/v1/auth/organizations/${uuid}/bots/bulk/duplicate`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

export const bulkToggleBotStatus = (uuid: string, data: { ids: string[] }) => {
  return http.request<r>(
    "patch",
    `/api/v1/auth/organizations/${uuid}/bots/bulk/toggle-status`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

/*
 ***************************
 *   Soft Delete Operations
 ***************************
 */
export const deleteBot = (uuid: string, id: string) => {
  return http.request<r>(
    "delete",
    `/api/v1/auth/organizations/${uuid}/bots/${id}/delete`
  );
};

export const bulkDeleteBots = (uuid: string, data: { ids: string[] }) => {
  return http.request<r>(
    "delete",
    `/api/v1/auth/organizations/${uuid}/bots/bulk/delete`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

/*
 ***************************
 *   Permanent Delete Operations
 ***************************
 */
export const deleteBotPermanent = (uuid: string, id: string) => {
  return http.request<r>(
    "delete",
    `/api/v1/auth/organizations/${uuid}/bots/${id}/force`
  );
};

export const bulkDeleteBotsPermanent = (
  uuid: string,
  data: { ids: string[] }
) => {
  return http.request<r>(
    "delete",
    `/api/v1/auth/organizations/${uuid}/bots/bulk/force`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

/*
 ***************************
 *   Restore Operations
 ***************************
 */
export const restoreBot = (uuid: string, id: string) => {
  return http.request<r>(
    "put",
    `/api/v1/auth/organizations/${uuid}/bots/${id}/restore`
  );
};

export const bulkRestoreBots = (uuid: string, data: { ids: string[] }) => {
  return http.request<r>(
    "put",
    `/api/v1/auth/organizations/${uuid}/bots/bulk/restore`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

// Aliases for compatibility
export const destroyBot = deleteBotPermanent;
export const bulkDestroyBots = bulkDeleteBotsPermanent;

/*
 ***************************
 *   Bot User Assignment Operations (Private Bots)
 ***************************
 */
export const assignUsersToBot = (uuid: string, botId: string, data: object) => {
  return http.request<r>(
    "post",
    `/api/v1/auth/organizations/${uuid}/bots/${botId}/assign-users`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

export const unassignUsersFromBot = (
  uuid: string,
  botId: string,
  data: object
) => {
  return http.request<r>(
    "delete",
    `/api/v1/auth/organizations/${uuid}/bots/${botId}/unassign-users`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

export const getBotAssignedUsers = (
  uuid: string,
  botId: string,
  params?: any
) => {
  return http.request<r>(
    "get",
    `/api/v1/auth/organizations/${uuid}/bots/${botId}/users`,
    {
      params
    }
  );
};

/*
 ***************************
 *   Dropdown Operations
 ***************************
 */
export const getGeneralPrompts = (params?: object) => {
  return http.request<r>("get", `/api/v1/auth/bot-general-prompt`, {
    params
  });
};
