import { h } from "vue";
import { $t } from "@/plugins/i18n";
import { IconifyIconOnline } from "@/components/ReIcon";
import { dayjs, ElAvatar, ElTag } from "element-plus";

export const columns: TableColumnList = [
  {
    type: "selection",
    width: "30px",
    sortable: false
  },
  {
    type: "index",
    width: 40,
    headerRenderer: () => $t("No.")
  },
  {
    label: "",
    prop: "invitee.avatarUrl",
    width: 100,
    cellRenderer: ({ row }) =>
      h(
        "div",
        {
          class: "flex items-center justify-center"
        },
        [
          h(ElAvatar, {
            size: 40,
            src: row.invitee.avatarUrl,
            icon: "User",
            class: "border border-gray-200"
          })
        ]
      )
  },
  {
    headerRenderer: () => $t("Full Name"),
    prop: "invitee",
    align: "left",
    minWidth: 180,
    cellRenderer: ({ row }) => {
      return h("div", { class: "flex flex-col" }, [
        h(
          "div",
          {
            class: "font-medium text-gray-900"
          },
          row.invitee.fullName || $t("Unnamed")
        ),
        h(
          "div",
          {
            class: "text-xs text-gray-500"
          },
          row.invitee.email
        )
      ]);
    }
  },
  {
    prop: "role",
    align: "center",
    sortable: true,
    width: 100,
    headerRenderer: () => $t("Role"),
    cellRenderer: ({ row }) => {
      const roleConfig = {
        admin: { color: "danger", text: "Admin", icon: "mdi:account-star" },
        member: { color: "primary", text: "Member", icon: "mdi:account-group" },
        guest: { color: "info", text: "Guest", icon: "mdi:account-multiple" }
      };
      const config = roleConfig[row.role] || {
        color: "info",
        text: row.role,
        icon: "mdi:account-circle"
      };

      return h(
        ElTag,
        {
          type: config.color,
          size: "small"
        },
        () =>
          h(
            "div",
            {
              class: "flex items-center"
            },
            [
              h(IconifyIconOnline, {
                icon: config.icon,
                class: `mr-1 text-${config.color}`
              }),
              config.text
            ]
          )
      );
    }
  },
  {
    prop: "status",
    align: "center",
    sortable: true,
    width: 120,
    headerRenderer: () => $t("Status"),
    cellRenderer: ({ row }) => {
      const statusConfig = {
        pending: {
          color: "warning",
          text: "Pending",
          icon: "mdi:clock-outline"
        },
        accepted: {
          color: "success",
          text: "Accepted",
          icon: "mdi:check-circle"
        },
        cancelled: {
          color: "danger",
          text: "Cancelled",
          icon: "mdi:cancel"
        },
        expired: {
          color: "info",
          text: "Expired",
          icon: "mdi:clock-alert-outline"
        }
      };
      const config = statusConfig[row.status] || {
        color: "info",
        text: row.status,
        icon: "mdi:help-circle"
      };

      return h(
        "div",
        {
          class: "flex items-center justify-center"
        },
        h(
          ElTag,
          {
            type: config.color,
            size: "small"
          },
          () =>
            h(
              "div",
              {
                class: "flex items-center"
              },
              [
                h(IconifyIconOnline, {
                  icon: config.icon,
                  class: `mr-1 text-${config.color}`
                }),
                config.text
              ]
            )
        )
      );
    }
  },
  {
    prop: "inviter.fullName",
    align: "left",
    sortable: true,
    width: 180,
    headerRenderer: () => $t("Invited By")
  },
  {
    prop: "expiresAt",
    align: "center",
    sortable: true,
    width: 150,
    headerRenderer: () => $t("Expires At"),
    cellRenderer: ({ row }) => {
      const expiresAt = new Date(row.expiresAt);
      const now = new Date();
      const isExpired = expiresAt < now;

      return h(
        "div",
        {
          class: `text-sm ${isExpired ? "text-red-500" : "text-gray-600"}`
        },
        dayjs(row.expiresAt).format("YYYY-MM-DD HH:mm")
      );
    }
  },
  {
    prop: "createdAt",
    align: "center",
    sortable: true,
    width: 150,
    headerRenderer: () => $t("Created At"),
    cellRenderer: ({ row }) => {
      return h(
        "div",
        {
          class: "text-sm"
        },
        dayjs(row.createdAt).format("YYYY-MM-DD HH:mm")
      );
    }
  },
  {
    label: "",
    fixed: "right",
    width: 140,
    slot: "operation",
    sortable: false
  }
];
