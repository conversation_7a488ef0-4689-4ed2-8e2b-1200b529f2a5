<script setup lang="ts">
import { $t } from "@/plugins/i18n";
import { IconifyIconOnline } from "@/components/ReIcon";

interface User {
  uuid: string;
  firstName: string;
  lastName: string;
  fullName: string;
  email: string;
  role?: string;
}

const props = defineProps<{
  selectedMembers: User[];
}>();

const emit = defineEmits<{
  (e: "removeUser", index: number): void;
  (e: "roleChange", index: number, role: string): void;
}>();

// Role options
const roleOptions = [
  { label: "Admin", value: "admin" },
  { label: "Member", value: "member" }
];

const handleRemoveUser = (index: number) => {
  emit("removeUser", index);
};

const handleRoleChange = (index: number, role: string) => {
  emit("roleChange", index, role);
};
</script>

<template>
  <div v-if="selectedMembers.length > 0" class="invite-table-container w-full">
    <div class="table-header">
      <h4>{{ $t("Selected Members") }} ({{ selectedMembers.length }})</h4>
    </div>

    <el-table
      :data="selectedMembers"
      style="width: 100%"
      border
      size="small"
      class="invite-table"
    >
      <el-table-column :label="$t('Name')" prop="fullName" min-width="150" />

      <el-table-column :label="$t('Email')" prop="email" min-width="200" />

      <el-table-column :label="$t('Role')" min-width="120">
        <template #default="{ row, $index }">
          <el-select
            :model-value="row.role || 'member'"
            size="small"
            style="width: 100%"
            @update:model-value="value => handleRoleChange($index, value)"
          >
            <el-option
              v-for="option in roleOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </template>
      </el-table-column>

      <el-table-column :label="$t('Actions')" width="80" align="center">
        <template #default="{ $index }">
          <el-button
            type="danger"
            size="small"
            circle
            @click="handleRemoveUser($index)"
          >
            <IconifyIconOnline icon="tabler:trash" width="16px" />
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>

  <div v-else class="empty-state flex items-center justify-center w-full">
    <el-empty :description="$t('No members selected')" :image-size="80" />
  </div>
</template>

<style scoped>
.invite-table-container {
  margin-top: 20px;
}

.table-header {
  margin-bottom: 16px;
}

.table-header h4 {
  margin: 0;
  color: var(--el-text-color-primary);
  font-weight: 600;
}

.invite-table {
  border-radius: 6px;
  overflow: hidden;
}

.empty-state {
  padding: 40px 20px;
  text-align: center;
}

:deep(.el-table__header) {
  background-color: var(--el-fill-color-lighter);
}

:deep(.el-table td) {
  padding: 8px 0;
}

:deep(.el-table th) {
  padding: 12px 0;
  font-weight: 600;
}
</style>
