export interface SidebarItem {
  id?: number;
  name: string;
  title: string;
  icon?: string;
  path: string;
  component?: string;
  redirect?: string;
  meta?: {
    title: string;
    icon?: string;
    rank?: number;
    showLink?: boolean;
    showParent?: boolean;
    keepAlive?: boolean;
    frameSrc?: string;
    frameLoading?: boolean;
    hiddenTag?: boolean;
    dynamicLevel?: number;
    activePath?: string;
    auths?: string[];
    roles?: string[];
  };
  children?: SidebarItem[];
  parent_id?: number;
  sort_order?: number;
  status?: "active" | "inactive";
  created_at?: string;
  updated_at?: string;
  deleted_at?: string;
}

export interface FilterProps {
  title?: string;
  status?: string;
  parent_id?: number;
  isTrashed?: "yes" | "no" | "only";
  filter?: any;
}

export interface SidebarFormData {
  id?: number;
  name: string;
  title: string;
  icon?: string;
  path: string;
  component?: string;
  redirect?: string;
  parent_id?: number;
  sort_order?: number;
  status?: "active" | "inactive";
  menuType?: number;
  meta?: {
    title: string;
    icon?: string;
    rank?: number;
    showLink?: boolean;
    showParent?: boolean;
    keepAlive?: boolean;
    frameSrc?: string;
    frameLoading?: boolean;
    hiddenTag?: boolean;
    dynamicLevel?: number;
    activePath?: string;
    auths?: string[];
    roles?: string[];
  };
}

export interface FormItemProps {
  id?: number;
  name?: string;
  title?: string;
  icon?: string;
  path?: string;
  component?: string;
  redirect?: string;
  parent_id?: number;
  sort_order?: number;
  status?: "active" | "inactive";
  menuType?: number;
  meta?: {
    title?: string;
    icon?: string;
    rank?: number;
    showLink?: boolean;
    showParent?: boolean;
    keepAlive?: boolean;
    frameSrc?: string;
    frameLoading?: boolean;
    hiddenTag?: boolean;
    dynamicLevel?: number;
    activePath?: string;
    auths?: string[];
    roles?: string[];
  };
}

export interface SidebarListParams {
  page?: number;
  per_page?: number;
  search?: string;
  status?: string;
  parent_id?: number;
  sort_by?: string;
  sort_order?: "asc" | "desc";
  isTrashed?: "yes" | "no" | "only";
}

export interface SidebarListResponse {
  data: SidebarItem[];
  meta: {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
}
