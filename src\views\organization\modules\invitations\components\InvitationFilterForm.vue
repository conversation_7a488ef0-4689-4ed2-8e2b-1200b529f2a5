<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, ref } from "vue";
import { IconifyIconOnline } from "@/components/ReIcon";

const props = defineProps<{
  visible: boolean;
  values: FieldValues;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FieldValues): void;
  (e: "submit", values: FieldValues): void;
  (e: "reset"): void;
}>();

const loading = ref(false);
const formRef = ref();

const formColumns: PlusColumn[] = [
  {
    label: computed(() => $t("Email")),
    prop: "email",
    valueType: "input",
    fieldProps: {
      placeholder: $t("Filter by email address"),
      clearable: true
    }
  },
  {
    label: computed(() => $t("Role")),
    prop: "role",
    valueType: "select",
    options: [
      { label: $t("All Roles"), value: "" },
      { label: "Admin", value: "admin" },
      { label: "Member", value: "member" },
      { label: "Guest", value: "guest" }
    ],
    fieldProps: {
      placeholder: $t("Filter by role"),
      clearable: true
    }
  },
  {
    label: computed(() => $t("Status")),
    prop: "status",
    valueType: "select",
    options: [
      { label: $t("All Status"), value: "" },
      { label: $t("Pending"), value: "pending" },
      { label: $t("Accepted"), value: "accepted" },
      { label: $t("Cancelled"), value: "cancelled" },
      { label: $t("Expired"), value: "expired" }
    ],
    fieldProps: {
      placeholder: $t("Filter by status"),
      clearable: true
    }
  },
  {
    label: computed(() => $t("Invited By")),
    prop: "invitedBy",
    valueType: "input",
    fieldProps: {
      placeholder: $t("Filter by who sent the invitation"),
      clearable: true
    }
  },
  {
    label: computed(() => $t("Date Range")),
    prop: "dateRange",
    valueType: "date-picker",
    fieldProps: {
      type: "daterange",
      rangeSeparator: $t("To"),
      startPlaceholder: $t("Start Date"),
      endPlaceholder: $t("End Date"),
      format: "YYYY-MM-DD",
      valueFormat: "YYYY-MM-DD",
      clearable: true
    }
  }
];

const handleSubmit = async (values: FieldValues) => {
  if (!formRef.value?.formInstance) return;
  const valid = await formRef.value.formInstance.validate();
  if (!valid) return;

  try {
    loading.value = true;
    emit("submit", values);
  } finally {
    setTimeout(() => {
      loading.value = false;
    }, 1000);
  }
};

const handleReset = () => {
  emit("reset");
};

const resetForm = () => {
  if (formRef.value?.formInstance) {
    formRef.value.formInstance.resetFields();
  }
};

defineExpose({
  resetForm
});
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :visible="visible"
    :model-value="values"
    size="30%"
    :closeOnClickModal="true"
    :closeOnPressEscape="true"
    :showClose="true"
    :destroyOnClose="true"
    :form="{
      columns: formColumns,
      footerAlign: 'center',
      hasFooter: false,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 10 }
    }"
    @update:visible="val => emit('update:visible', val)"
    @update:model-value="val => emit('update:values', val)"
  >
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">{{ $t("Filter") }}</span>
      </div>
    </template>
    <template #drawer-footer>
      <div class="custom-footer">
        <el-button round @click="handleReset">
          <IconifyIconOnline icon="tabler:refresh" class="mr-1.5" />
          {{ $t("Reset") }}
        </el-button>
        <el-button
          round
          type="warning"
          :loading="loading"
          @click="handleSubmit(values)"
        >
          <IconifyIconOnline icon="tabler:filter" class="mr-1.5" />
          {{ $t("Apply Filter") }}
        </el-button>
      </div>
    </template>
  </PlusDrawerForm>
</template>
