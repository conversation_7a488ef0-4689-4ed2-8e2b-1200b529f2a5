import { $t } from "@/plugins/i18n";

export interface SocialLoginState {
  returnUrl: string;
  timestamp: number;
  formData?: Record<string, any>;
  provider?: string;
  loginType?: 'login' | 'register';
}

export interface SocialAuthResult {
  success: boolean;
  token?: string;
  error?: string;
  provider: string;
}

export class SocialAuthService {
  private static readonly STATE_KEY = 'social_login_state';
  private static readonly STATE_EXPIRY = 10 * 60 * 1000; // 10 minutes

  /**
   * Initiate social login with redirect approach
   */
  async initiateSocialLogin(
    provider: string, 
    options: {
      returnUrl?: string;
      loginType?: 'login' | 'register';
      formData?: Record<string, any>;
    } = {}
  ): Promise<void> {
    try {
      // Save current application state
      this.saveCurrentState({
        returnUrl: options.returnUrl || this.getCurrentUrl(),
        timestamp: Date.now(),
        formData: options.formData,
        provider,
        loginType: options.loginType || 'login'
      });

      // Construct social login URL
      const socialUrl = this.buildSocialLoginUrl(provider);
      
      console.log(`Redirecting to social login: ${provider}`);
      
      // Redirect to social provider
      window.location.href = socialUrl;
      
    } catch (error) {
      console.error('Failed to initiate social login:', error);
      throw new Error($t("Failed to initiate social login"));
    }
  }

  /**
   * Build social login URL with proper callback
   */
  private buildSocialLoginUrl(provider: string): string {
    const baseUrl = import.meta.env.VITE_API_BASE_URL || "";
    const callbackUrl = `${window.location.origin}/auth/social/callback`;
    
    // Add callback URL as parameter
    const url = new URL(`${baseUrl}/auth/social/${provider}`);
    url.searchParams.set('redirect_url', callbackUrl);
    
    return url.toString();
  }

  /**
   * Get current URL for return navigation
   */
  private getCurrentUrl(): string {
    return window.location.pathname + window.location.search;
  }

  /**
   * Save current application state to sessionStorage
   */
  private saveCurrentState(state: SocialLoginState): void {
    try {
      sessionStorage.setItem(this.STATE_KEY, JSON.stringify(state));
      console.log('Social login state saved:', state);
    } catch (error) {
      console.error('Failed to save social login state:', error);
      // Continue without state saving - not critical
    }
  }

  /**
   * Restore saved application state
   */
  restoreState(): SocialLoginState | null {
    try {
      const stateStr = sessionStorage.getItem(this.STATE_KEY);
      if (!stateStr) {
        console.log('No social login state found');
        return null;
      }

      const state: SocialLoginState = JSON.parse(stateStr);
      
      // Check if state is expired
      if (Date.now() - state.timestamp > this.STATE_EXPIRY) {
        console.log('Social login state expired');
        this.clearState();
        return null;
      }

      console.log('Social login state restored:', state);
      return state;
      
    } catch (error) {
      console.error('Failed to restore social login state:', error);
      this.clearState();
      return null;
    }
  }

  /**
   * Clear saved state
   */
  clearState(): void {
    try {
      sessionStorage.removeItem(this.STATE_KEY);
      console.log('Social login state cleared');
    } catch (error) {
      console.error('Failed to clear social login state:', error);
    }
  }

  /**
   * Process social login callback
   */
  processCallback(urlParams: URLSearchParams): SocialAuthResult {
    const token = urlParams.get('token');
    const error = urlParams.get('error');
    const provider = urlParams.get('provider') || 'unknown';

    if (error) {
      return {
        success: false,
        error: this.getErrorMessage(error),
        provider
      };
    }

    if (!token) {
      return {
        success: false,
        error: $t("No authentication token received"),
        provider
      };
    }

    return {
      success: true,
      token,
      provider
    };
  }

  /**
   * Get user-friendly error message
   */
  private getErrorMessage(error: string): string {
    const errorMessages: Record<string, string> = {
      'access_denied': $t("Access denied by social provider"),
      'invalid_request': $t("Invalid authentication request"),
      'server_error': $t("Social provider server error"),
      'temporarily_unavailable': $t("Social provider temporarily unavailable"),
      'cancelled': $t("Social login was cancelled"),
      'timeout': $t("Social login timed out"),
      'invalid_token': $t("Invalid authentication token"),
      'user_not_found': $t("User not found"),
      'email_not_verified': $t("Email not verified with social provider")
    };

    return errorMessages[error] || error || $t("Social login failed");
  }

  /**
   * Check if current page is social callback
   */
  static isSocialCallback(): boolean {
    return window.location.pathname === '/auth/social/callback';
  }

  /**
   * Extract form data from current page (for state saving)
   */
  extractFormData(): Record<string, any> | undefined {
    try {
      // Try to extract form data from common form elements
      const forms = document.querySelectorAll('form');
      if (forms.length === 0) return undefined;

      const formData: Record<string, any> = {};
      const form = forms[0]; // Get first form

      // Extract input values
      const inputs = form.querySelectorAll('input, select, textarea');
      inputs.forEach((input: any) => {
        if (input.name && input.value && input.type !== 'password') {
          formData[input.name] = input.value;
        }
      });

      return Object.keys(formData).length > 0 ? formData : undefined;
    } catch (error) {
      console.error('Failed to extract form data:', error);
      return undefined;
    }
  }

  /**
   * Restore form data to current page
   */
  restoreFormData(formData: Record<string, any>): void {
    try {
      Object.entries(formData).forEach(([name, value]) => {
        const input = document.querySelector(`[name="${name}"]`) as HTMLInputElement;
        if (input && input.type !== 'password') {
          input.value = value;
          
          // Trigger Vue reactivity
          const event = new Event('input', { bubbles: true });
          input.dispatchEvent(event);
        }
      });
      
      console.log('Form data restored:', formData);
    } catch (error) {
      console.error('Failed to restore form data:', error);
    }
  }

  /**
   * Get redirect URL after successful authentication
   */
  getRedirectUrl(savedState: SocialLoginState | null, defaultUrl: string = '/'): string {
    if (savedState?.returnUrl) {
      // Validate return URL is safe (same origin)
      try {
        const url = new URL(savedState.returnUrl, window.location.origin);
        if (url.origin === window.location.origin) {
          return savedState.returnUrl;
        }
      } catch (e) {
        console.warn('Invalid return URL:', savedState.returnUrl);
      }
    }
    
    return defaultUrl;
  }
}

// Export singleton instance
export const socialAuthService = new SocialAuthService();
