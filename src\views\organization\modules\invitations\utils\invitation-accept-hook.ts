import { ref } from "vue";
import { $t } from "@/plugins/i18n";
import { message } from "@/utils/message";
import { useConvertKeyToCamel } from "@/utils/helpers";
import {
  acceptInvitation,
  getInvitationByToken,
  validateInvitationToken
} from "./public-api";

export function useInvitationAcceptHook(token: string) {
  /* ***************************
   * Data/State Management
   *************************** */

  const loading = ref(false);
  const processing = ref(false);
  const error = ref("");
  const processed = ref(false);
  const action = ref<"accept" | "decline" | null>(null);
  const invitation = ref<any>({});
  const successMessage = ref("");

  /* ***************************
   * API Operations
   *************************** */

  const fnGetInvitationDetails = async () => {
    try {
      loading.value = true;
      error.value = "";

      // First validate the token
      const validationResponse = await validateInvitationToken(token);

      if (!validationResponse.success) {
        error.value =
          validationResponse.message ||
          $t("Invalid or expired invitation token");
        return;
      }

      // Get invitation details
      const response = await getInvitationByToken(token);

      if (response.success) {
        invitation.value = useConvertKeyToCamel(response.data);

        // Check if invitation is still valid
        if (invitation.value.status !== "pending") {
          error.value = $t("This invitation has already been processed");
          return;
        }

        // Check if invitation is expired
        if (
          invitation.value.expiresAt &&
          new Date(invitation.value.expiresAt) < new Date()
        ) {
          error.value = $t("This invitation has expired");
          return;
        }
      } else {
        error.value =
          response.message || $t("Failed to load invitation details");
      }
    } catch (err) {
      console.error("Get invitation details error:", err);
      error.value =
        err.response?.data?.message || $t("Failed to load invitation details");
    } finally {
      loading.value = false;
    }
  };

  const fnAcceptInvitation = async () => {
    try {
      processing.value = true;
      action.value = "accept";

      const response = await acceptInvitation({
        token,
        action: "accept"
      });

      if (response.success) {
        processed.value = true;
        successMessage.value =
          response.message ||
          $t(
            "You have successfully joined the organization. You can now log in to access your account."
          );

        message($t("Invitation accepted successfully"), {
          type: "success"
        });
      } else {
        error.value = response.message || $t("Failed to accept invitation");
        message(error.value, {
          type: "error"
        });
      }
    } catch (err) {
      console.error("Accept invitation error:", err);
      error.value =
        err.response?.data?.message || $t("Failed to accept invitation");
      message(error.value, {
        type: "error"
      });
    } finally {
      processing.value = false;
    }
  };

  const fnDeclineInvitation = async () => {
    try {
      processing.value = true;
      action.value = "decline";

      const response = await acceptInvitation({
        token,
        action: "decline"
      });

      if (response.success) {
        processed.value = true;
        successMessage.value =
          response.message ||
          $t(
            "You have declined the invitation. The organization has been notified."
          );

        message($t("Invitation declined"), {
          type: "info"
        });
      } else {
        error.value = response.message || $t("Failed to decline invitation");
        message(error.value, {
          type: "error"
        });
      }
    } catch (err) {
      console.error("Decline invitation error:", err);
      error.value =
        err.response?.data?.message || $t("Failed to decline invitation");
      message(error.value, {
        type: "error"
      });
    } finally {
      processing.value = false;
    }
  };

  /* ***************************
   * Return Hook Interface
   *************************** */

  return {
    // Data/State
    loading,
    processing,
    error,
    processed,
    action,
    invitation,
    successMessage,

    // API Operations
    fnGetInvitationDetails,
    fnAcceptInvitation,
    fnDeclineInvitation
  };
}
