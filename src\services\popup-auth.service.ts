import { $t } from "@/plugins/i18n";

export interface SocialLoginResult {
  provider: string;
  token: string;
}

export interface PopupMessageData {
  type: 'SOCIAL_LOGIN_SUCCESS' | 'SOCIAL_LOGIN_ERROR';
  token?: string;
  error?: string;
  provider?: string;
}

export class PopupAuthService {
  private popup: Window | null = null;
  private checkClosed: NodeJS.Timeout | null = null;
  private messageHandler: ((event: MessageEvent) => void) | null = null;
  private currentProvider: string = '';

  /**
   * Open social login popup and wait for authentication result
   */
  async openSocialLogin(provider: string): Promise<SocialLoginResult> {
    return new Promise((resolve, reject) => {
      try {
        this.currentProvider = provider;
        
        // 1. Construct popup URL
        const baseUrl = import.meta.env.VITE_API_BASE_URL || "";
        const popupUrl = `${baseUrl}/auth/social/${provider}`;
        
        // 2. Get popup configuration
        const popupFeatures = this.getPopupFeatures();
        
        // 3. Open popup window
        this.popup = window.open(popupUrl, `social-login-${provider}`, popupFeatures);
        
        if (!this.popup) {
          throw new Error('Popup blocked by browser');
        }

        // Focus the popup window
        this.popup.focus();
        
        // 4. Setup message listener for communication with popup
        this.setupMessageListener(resolve, reject);
        
        // 5. Monitor popup close (user cancellation)
        this.monitorPopupClose(reject);
        
        // 6. Setup timeout (30 seconds)
        this.setupTimeout(reject);
        
      } catch (error) {
        this.cleanup();
        reject(error);
      }
    });
  }

  /**
   * Get popup window features/configuration
   */
  private getPopupFeatures(): string {
    const width = 500;
    const height = 600;
    const left = Math.round((screen.width - width) / 2);
    const top = Math.round((screen.height - height) / 2);
    
    return [
      `width=${width}`,
      `height=${height}`,
      `left=${left}`,
      `top=${top}`,
      'scrollbars=yes',
      'resizable=yes',
      'status=yes',
      'location=yes',
      'toolbar=no',
      'menubar=no',
      'directories=no'
    ].join(',');
  }

  /**
   * Setup message listener for popup communication
   */
  private setupMessageListener(resolve: (result: SocialLoginResult) => void, reject: (error: Error) => void) {
    this.messageHandler = (event: MessageEvent<PopupMessageData>) => {
      // Security: Validate message origin
      if (!this.isValidOrigin(event.origin)) {
        console.warn('PopupAuth: Rejected message from unauthorized origin:', event.origin);
        return;
      }

      const { data } = event;
      
      if (!data || typeof data !== 'object') {
        return;
      }

      if (data.type === 'SOCIAL_LOGIN_SUCCESS') {
        if (data.token) {
          this.cleanup();
          resolve({
            provider: this.currentProvider,
            token: data.token
          });
        } else {
          this.cleanup();
          reject(new Error('No token received from social login'));
        }
      } else if (data.type === 'SOCIAL_LOGIN_ERROR') {
        this.cleanup();
        reject(new Error(data.error || 'Social login failed'));
      }
    };
    
    window.addEventListener('message', this.messageHandler);
  }

  /**
   * Monitor popup window close (user cancellation)
   */
  private monitorPopupClose(reject: (error: Error) => void) {
    this.checkClosed = setInterval(() => {
      if (this.popup?.closed) {
        this.cleanup();
        reject(new Error('Social login cancelled'));
      }
    }, 1000);
  }

  /**
   * Setup timeout for popup authentication
   */
  private setupTimeout(reject: (error: Error) => void) {
    setTimeout(() => {
      if (this.popup && !this.popup.closed) {
        this.cleanup();
        reject(new Error('Social login timeout'));
      }
    }, 30000); // 30 seconds timeout
  }

  /**
   * Validate message origin for security
   */
  private isValidOrigin(origin: string): boolean {
    const allowedOrigins = [
      window.location.origin,
      import.meta.env.VITE_API_BASE_URL?.replace(/\/$/, ''), // Remove trailing slash
      // Add other allowed origins if needed
    ].filter(Boolean);

    return allowedOrigins.includes(origin);
  }

  /**
   * Cleanup popup and event listeners
   */
  private cleanup() {
    // Close popup window
    if (this.popup && !this.popup.closed) {
      this.popup.close();
    }
    this.popup = null;
    
    // Clear interval for monitoring popup close
    if (this.checkClosed) {
      clearInterval(this.checkClosed);
      this.checkClosed = null;
    }
    
    // Remove message event listener
    if (this.messageHandler) {
      window.removeEventListener('message', this.messageHandler);
      this.messageHandler = null;
    }

    // Reset current provider
    this.currentProvider = '';
  }

  /**
   * Check if popup is currently open
   */
  isPopupOpen(): boolean {
    return this.popup !== null && !this.popup.closed;
  }

  /**
   * Force close popup (for cleanup)
   */
  closePopup() {
    this.cleanup();
  }

  /**
   * Check if browser supports popups
   */
  static isPopupSupported(): boolean {
    try {
      const testPopup = window.open('', '_blank', 'width=1,height=1');
      if (testPopup) {
        testPopup.close();
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /**
   * Get user-friendly error message
   */
  static getErrorMessage(error: Error): string {
    switch (error.message) {
      case 'Popup blocked by browser':
        return $t("Please allow popups for social login to work. Check your browser's popup blocker settings.");
      case 'Social login cancelled':
        return $t("Social login was cancelled");
      case 'Social login timeout':
        return $t("Social login timed out. Please try again.");
      case 'No token received from social login':
        return $t("Authentication failed. Please try again.");
      default:
        return error.message || $t("Social login failed");
    }
  }
}

// Export singleton instance
export const popupAuthService = new PopupAuthService();
