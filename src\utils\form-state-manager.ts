/**
 * Form State Manager for Social Login Redirect Flow
 * Handles saving and restoring form data during social authentication
 */

export interface FormFieldData {
  name: string;
  value: any;
  type: string;
  checked?: boolean;
  selected?: boolean;
}

export interface FormStateData {
  fields: FormFieldData[];
  timestamp: number;
  url: string;
}

export class FormStateManager {
  private static readonly STORAGE_KEY = 'form_state_data';
  private static readonly STATE_EXPIRY = 10 * 60 * 1000; // 10 minutes

  /**
   * Extract form data from current page
   */
  static extractFormData(): Record<string, any> | undefined {
    try {
      const forms = document.querySelectorAll('form');
      if (forms.length === 0) return undefined;

      const formData: Record<string, any> = {};
      
      // Process all forms on the page
      forms.forEach(form => {
        const inputs = form.querySelectorAll('input, select, textarea');
        
        inputs.forEach((input: any) => {
          if (!input.name || !input.value) return;
          
          // Skip sensitive fields
          if (this.isSensitiveField(input)) return;
          
          // Handle different input types
          switch (input.type) {
            case 'checkbox':
            case 'radio':
              if (input.checked) {
                formData[input.name] = input.value;
              }
              break;
            case 'select-multiple':
              const selectedOptions = Array.from(input.selectedOptions)
                .map((option: any) => option.value);
              if (selectedOptions.length > 0) {
                formData[input.name] = selectedOptions;
              }
              break;
            default:
              formData[input.name] = input.value;
          }
        });
      });

      return Object.keys(formData).length > 0 ? formData : undefined;
    } catch (error) {
      console.error('Failed to extract form data:', error);
      return undefined;
    }
  }

  /**
   * Save form state to storage
   */
  static saveFormState(formData: Record<string, any>, url: string): void {
    try {
      const stateData: FormStateData = {
        fields: this.convertToFieldData(formData),
        timestamp: Date.now(),
        url
      };

      sessionStorage.setItem(this.STORAGE_KEY, JSON.stringify(stateData));
      console.log('Form state saved:', stateData);
    } catch (error) {
      console.error('Failed to save form state:', error);
    }
  }

  /**
   * Restore form state from storage
   */
  static restoreFormState(): FormStateData | null {
    try {
      const stateStr = sessionStorage.getItem(this.STORAGE_KEY);
      if (!stateStr) return null;

      const stateData: FormStateData = JSON.parse(stateStr);
      
      // Check if state is expired
      if (Date.now() - stateData.timestamp > this.STATE_EXPIRY) {
        this.clearFormState();
        return null;
      }

      // Check if URL matches (optional validation)
      if (stateData.url !== window.location.pathname) {
        console.warn('Form state URL mismatch:', stateData.url, 'vs', window.location.pathname);
      }

      return stateData;
    } catch (error) {
      console.error('Failed to restore form state:', error);
      this.clearFormState();
      return null;
    }
  }

  /**
   * Apply form state to current page
   */
  static applyFormState(stateData: FormStateData): void {
    try {
      stateData.fields.forEach(field => {
        const input = document.querySelector(`[name="${field.name}"]`) as HTMLInputElement;
        if (!input) return;

        // Apply value based on input type
        switch (field.type) {
          case 'checkbox':
          case 'radio':
            input.checked = field.checked || false;
            break;
          case 'select-multiple':
            if (Array.isArray(field.value)) {
              Array.from(input.options).forEach((option: any) => {
                option.selected = field.value.includes(option.value);
              });
            }
            break;
          default:
            input.value = field.value || '';
        }

        // Trigger Vue reactivity
        this.triggerInputEvent(input);
      });

      console.log('Form state applied:', stateData);
    } catch (error) {
      console.error('Failed to apply form state:', error);
    }
  }

  /**
   * Clear form state from storage
   */
  static clearFormState(): void {
    try {
      sessionStorage.removeItem(this.STORAGE_KEY);
      console.log('Form state cleared');
    } catch (error) {
      console.error('Failed to clear form state:', error);
    }
  }

  /**
   * Check if field contains sensitive data
   */
  private static isSensitiveField(input: HTMLInputElement): boolean {
    const sensitiveTypes = ['password', 'hidden'];
    const sensitiveNames = ['password', 'token', 'csrf', 'secret', 'key'];
    
    // Check input type
    if (sensitiveTypes.includes(input.type.toLowerCase())) {
      return true;
    }

    // Check input name
    const name = input.name.toLowerCase();
    return sensitiveNames.some(sensitive => name.includes(sensitive));
  }

  /**
   * Convert form data to field data structure
   */
  private static convertToFieldData(formData: Record<string, any>): FormFieldData[] {
    return Object.entries(formData).map(([name, value]) => {
      const input = document.querySelector(`[name="${name}"]`) as HTMLInputElement;
      
      return {
        name,
        value,
        type: input?.type || 'text',
        checked: input?.checked,
        selected: input?.selected
      };
    });
  }

  /**
   * Trigger input event for Vue reactivity
   */
  private static triggerInputEvent(input: HTMLInputElement): void {
    try {
      // Trigger multiple events to ensure Vue reactivity
      const events = ['input', 'change', 'blur'];
      
      events.forEach(eventType => {
        const event = new Event(eventType, { 
          bubbles: true, 
          cancelable: true 
        });
        input.dispatchEvent(event);
      });
    } catch (error) {
      console.error('Failed to trigger input event:', error);
    }
  }

  /**
   * Auto-save form data on page unload (for social login)
   */
  static setupAutoSave(): void {
    const handleBeforeUnload = () => {
      const formData = this.extractFormData();
      if (formData) {
        this.saveFormState(formData, window.location.pathname);
      }
    };

    // Save on page unload
    window.addEventListener('beforeunload', handleBeforeUnload);

    // Save periodically (every 30 seconds)
    const autoSaveInterval = setInterval(() => {
      const formData = this.extractFormData();
      if (formData) {
        this.saveFormState(formData, window.location.pathname);
      }
    }, 30000);

    // Cleanup on page unload
    window.addEventListener('beforeunload', () => {
      clearInterval(autoSaveInterval);
    });
  }

  /**
   * Auto-restore form data on page load
   */
  static setupAutoRestore(): void {
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        this.performAutoRestore();
      });
    } else {
      this.performAutoRestore();
    }
  }

  /**
   * Perform auto-restore with delay for Vue components
   */
  private static performAutoRestore(): void {
    // Wait a bit for Vue components to mount
    setTimeout(() => {
      const stateData = this.restoreFormState();
      if (stateData) {
        this.applyFormState(stateData);
        this.clearFormState(); // Clear after successful restore
      }
    }, 500);
  }
}

// Auto-setup for convenience
if (typeof window !== 'undefined') {
  FormStateManager.setupAutoSave();
  FormStateManager.setupAutoRestore();
}
