# Organization Overview Module

## Purpose
Provides a comprehensive overview and statistics dashboard for organization management with modular architecture and reusable components.

## Architecture
This module follows a clean, modular architecture with separation of concerns:

### 📁 Structure
```
overview/
├── index.vue                    # Main container component
├── components/                  # Reusable UI components
│   ├── StatsCards.vue          # Statistics cards display
│   ├── ChartsSection.vue       # Data visualization charts
│   └── ActivitySection.vue     # Activity and usage metrics
├── composables/                # Business logic and state management
│   ├── useOrganizationOverview.ts  # Data fetching and state
│   └── useOverviewCharts.ts    # Chart management and configuration
└── README.md                   # This documentation
```

## Features
- **📊 Statistics Cards**: Real-time metrics for bots, conversations, members, and storage
- **📈 Interactive Charts**: ECharts-powered visualizations for trends and activity
- **🎯 Activity Tracking**: Popular bots, peak hours, and usage analytics
- **⚡ Performance Optimized**: Lazy loading, efficient re-renders, proper cleanup
- **📱 Responsive Design**: Mobile-first approach with adaptive layouts
- **🔄 Error Handling**: Graceful fallbacks and mock data for development
- **♿ Accessibility**: ARIA labels, keyboard navigation, screen reader support

## Components

### `index.vue`
Main container component that orchestrates the overview dashboard.
- Manages overall layout and error states
- Coordinates data fetching and loading states
- Provides clean component composition

### `StatsCards.vue`
Displays key organization metrics in card format.
- **Props**: `stats`, `loading`
- **Events**: `refresh`
- **Features**: Animated loading, formatted numbers, progress indicators

### `ChartsSection.vue`
Renders interactive data visualization charts.
- **Props**: `stats`, `loading`
- **Features**: ECharts integration, responsive design, theme support

### `ActivitySection.vue`
Shows activity metrics and popular bot usage.
- **Props**: `stats`, `loading`
- **Features**: Bot rankings, peak hours, activity summaries

## Composables

### `useOrganizationOverview.ts`
Manages data fetching and state for organization overview.
- **Returns**: `stats`, `loading`, `error`, `fetchOverviewData`, `refreshData`
- **Features**: API integration, mock data fallback, error handling

### `useOverviewCharts.ts`
Handles chart initialization and management.
- **Returns**: Chart refs, initialization methods, resize handlers
- **Features**: ECharts lifecycle management, responsive charts, cleanup

## Dependencies
- **Vue 3**: Composition API, reactivity system
- **ECharts**: Data visualization and charting
- **Element Plus**: UI component library
- **TypeScript**: Type safety and better DX

## Usage

### Basic Implementation
```vue
<script setup lang="ts">
import Overview from '@/views/organization/modules/overview/index.vue'
</script>

<template>
  <Overview />
</template>
```

### With Custom Organization ID
```vue
<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import Overview from '@/views/organization/modules/overview/index.vue'

const route = useRoute()
const organizationId = computed(() => route.params.id as string)
</script>

<template>
  <Overview />
</template>
```

## Development

### Mock Data
The module includes comprehensive mock data for development and testing:
- Realistic statistics and metrics
- Sample chart data
- Activity and usage patterns

### Error Handling
- API failure graceful degradation
- Loading state management
- User-friendly error messages

### Performance
- Efficient chart rendering
- Proper component cleanup
- Optimized re-renders

## Customization

### Styling
Components use scoped CSS with utility classes for easy customization:
```css
.stats-card {
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
}
```

### Chart Themes
Charts support custom themes and responsive design:
```typescript
const getChartTheme = () => ({
  backgroundColor: "transparent",
  textStyle: {
    color: "#374151",
    fontFamily: "Inter, system-ui, sans-serif"
  }
})
```

## Testing
- Unit tests for composables
- Component testing with Vue Test Utils
- E2E testing for user workflows

## Future Enhancements
- Real-time data updates via WebSocket
- Advanced filtering and date ranges
- Export functionality for reports
- Custom dashboard layouts
