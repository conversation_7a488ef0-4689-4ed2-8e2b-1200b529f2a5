/**
 * Format date string to readable format
 * @param dateString - ISO date string or Date object
 * @returns Formatted date string
 */
export function formatDate(dateString: string | Date): string {
  if (!dateString) return "";

  try {
    const date = typeof dateString === "string" ? new Date(dateString) : dateString;

    if (isNaN(date.getTime())) {
      return "";
    }

    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric"
    });
  } catch (error) {
    console.warn("Invalid date format:", dateString);
    return "";
  }
}

/**
 * Format date with time
 * @param dateString - ISO date string or Date object
 * @returns Formatted date with time
 */
export function formatDateTime(dateString: string | Date): string {
  if (!dateString) return "";

  try {
    const date = typeof dateString === "string" ? new Date(dateString) : dateString;

    if (isNaN(date.getTime())) {
      return "";
    }

    return date.toLocaleString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    });
  } catch (error) {
    console.warn("Invalid date format:", dateString);
    return "";
  }
}

/**
 * Format date for display in tables
 * @param dateString - ISO date string or Date object
 * @returns Formatted date for tables (YYYY-MM-DD)
 */
export function formatTableDate(dateString: string | Date): string {
  if (!dateString) return "";

  try {
    const date = typeof dateString === "string" ? new Date(dateString) : dateString;

    if (isNaN(date.getTime())) {
      return "";
    }

    return date.toISOString().split('T')[0];
  } catch (error) {
    console.warn("Invalid date format:", dateString);
    return "";
  }
}
