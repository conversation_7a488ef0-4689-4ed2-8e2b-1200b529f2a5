import { ref } from "vue";
import { useConvertKeyToCamel, useConvertKeyToSnake } from "@/utils/helpers";
import { $t } from "@/plugins/i18n";
import { message } from "@/utils/message";
import {
  getOrganizationSettings,
  updateOrganizationSettings,
  resetOrganizationSettings
} from "../../shared/utils/auth-api";

// Mock data for development - will be replaced by API response
const getMockSettingsData = () => {
  return {
    data: {
      // General Settings
      general: {
        name: "Acme Corporation",
        description: "Leading AI solutions provider",
        website: "https://acme.com",
        email: "<EMAIL>",
        phone: "******-0123",
        address: "123 Tech Street, Silicon Valley, CA 94000",
        timezone: "America/Los_Angeles",
        language: "en",
        currency: "USD",
        organizationType: "company",
        industry: "Technology",
        employeeCount: 150,
        foundedYear: 2020,
        logoUrl: null
      },
          
          // Security Settings
          security: {
            ssoEnabled: false,
            ssoProvider: "",
            twoFactorRequired: false,
            passwordPolicy: {
              minLength: 8,
              requireUppercase: true,
              requireLowercase: true,
              requireNumbers: true,
              requireSymbols: false,
              expiryDays: 90
            },
            ipWhitelist: [],
            sessionTimeout: 480, // minutes
            maxConcurrentSessions: 3,
            apiKeysEnabled: true,
            webhooksEnabled: true
          },
          
          // Member Settings
          members: {
            defaultRole: "member",
            allowSelfRegistration: false,
            requireEmailVerification: true,
            allowGuestAccess: true,
            maxMembers: 100,
            inviteExpiration: 7, // days
            autoApproveInvites: true,
            allowRoleChange: true,
            allowMemberLeave: true,
            notifyOnNewMember: true,
            notifyOnMemberLeave: true
          },
          
          // Billing Settings
          billing: {
            plan: "professional",
            billingCycle: "monthly",
            nextBillingDate: "2024-02-01",
            paymentMethod: {
              type: "card",
              last4: "4242",
              brand: "visa",
              expiryMonth: 12,
              expiryYear: 2025
            },
            billingAddress: {
              line1: "123 Tech Street",
              line2: "",
              city: "Silicon Valley",
              state: "CA",
              postalCode: "94000",
              country: "US"
            },
            usage: {
              conversations: {
                current: 1250,
                limit: 5000,
                percentage: 25
              },
              storage: {
                current: 2048, // MB
                limit: 10240, // MB
                percentage: 20
              },
              members: {
                current: 25,
                limit: 100,
                percentage: 25
              },
              apiCalls: {
                current: 45000,
                limit: 100000,
                percentage: 45
              }
            },
            invoices: [
              {
                id: "inv_001",
                date: "2024-01-01",
                amount: 99.00,
                status: "paid",
                downloadUrl: "/invoices/inv_001.pdf"
              },
              {
                id: "inv_002",
                date: "2023-12-01",
                amount: 99.00,
                status: "paid",
                downloadUrl: "/invoices/inv_002.pdf"
              }
            ]
          },
          
          // Notification Settings
          notifications: {
            email: {
              enabled: true,
              newMember: true,
              memberLeft: true,
              newBot: true,
              botError: true,
              usageAlert: true,
              billingAlert: true,
              securityAlert: true,
              weeklyReport: true,
              monthlyReport: true
            },
            slack: {
              enabled: false,
              webhookUrl: "",
              channel: "#general",
              newMember: false,
              memberLeft: false,
              newBot: false,
              botError: true,
              usageAlert: true,
              billingAlert: true,
              securityAlert: true
            },
            webhook: {
              enabled: false,
              url: "",
              secret: "",
              events: []
            }
          },
          
          // Integration Settings
          integrations: {
            slack: {
              enabled: false,
              teamId: "",
              teamName: "",
              accessToken: "",
              botUserId: "",
              configuredChannels: []
            },
            discord: {
              enabled: false,
              guildId: "",
              guildName: "",
              botToken: "",
              configuredChannels: []
            },
            teams: {
              enabled: false,
              tenantId: "",
              appId: "",
              configuredTeams: []
            },
            zapier: {
              enabled: false,
              apiKey: "",
              connectedZaps: []
            },
            webhook: {
              enabled: false,
              endpoints: []
            }
          },
          
          // API Settings
          api: {
            enabled: true,
            rateLimit: {
              requestsPerMinute: 100,
              requestsPerHour: 1000,
              requestsPerDay: 10000
            },
            allowedOrigins: ["https://acme.com"],
            apiKeys: [
              {
                id: "key_001",
                name: "Production API Key",
                keyPreview: "ak_live_****1234",
                permissions: ["read", "write"],
                lastUsed: "2024-01-15T10:30:00Z",
                createdAt: "2024-01-01T00:00:00Z"
              }
            ],
            webhooks: [
              {
                id: "wh_001",
                name: "Bot Events Webhook",
                url: "https://api.acme.com/webhooks/bot-events",
                events: ["bot.created", "bot.updated", "conversation.started"],
                secret: "wh_****5678",
                isActive: true,
                lastTriggered: "2024-01-15T09:45:00Z"
              }
            ]
          }
        }
      }
    };
  };
};

export function useSettingsHook(organizationId: string) {
  const loading = ref(false);
  const settings = ref({});

  const fnGetSettings = async () => {
    loading.value = true;
    try {
      const response = await getOrganizationSettings(organizationId);
      if (response.success) {
        settings.value = useConvertKeyToCamel(response.data);
      } else {
        // Fallback to mock data for development
        const mockData = getMockSettingsData();
        settings.value = useConvertKeyToCamel(mockData.data);
      }
      console.log("Settings loaded:", settings.value);
    } catch (e) {
      console.error("Get Settings error:", e);
      // Fallback to mock data on error
      const mockData = getMockSettingsData();
      settings.value = useConvertKeyToCamel(mockData.data);
      message(e.response?.data?.message || e?.message || $t("Failed to load settings"), {
        type: "warning"
      });
    } finally {
      loading.value = false;
    }
  };

  const handleSaveSettings = async (section: string, data: any) => {
    loading.value = true;
    try {
      const payload = { [section]: data };
      const response = await updateOrganizationSettings(organizationId, payload);

      if (response.success) {
        // Update local settings
        settings.value = {
          ...settings.value,
          [section]: data
        };
        message($t("Settings updated successfully"), { type: "success" });
      } else {
        message(response.message || $t("Failed to update settings"), {
          type: "error"
        });
      }
    } catch (e) {
      console.error("Save Settings error:", e);
      message(e.response?.data?.message || e?.message || $t("Failed to update settings"), {
        type: "error"
      });
    } finally {
      loading.value = false;
    }
  };

  const handleResetSettings = async () => {
    loading.value = true;
    try {
      const response = await resetOrganizationSettings(organizationId);

      if (response.success) {
        // Reload settings after reset
        await fnGetSettings();
        message($t("Settings reset successfully"), { type: "success" });
      } else {
        message(response.message || $t("Failed to reset settings"), {
          type: "error"
        });
      }
    } catch (e) {
      console.error("Reset Settings error:", e);
      message(e.response?.data?.message || e?.message || $t("Failed to reset settings"), {
        type: "error"
      });
    } finally {
      loading.value = false;
    }
  };

  return {
    loading,
    settings,
    fnGetSettings,
    handleSaveSettings,
    handleResetSettings
  };
}
