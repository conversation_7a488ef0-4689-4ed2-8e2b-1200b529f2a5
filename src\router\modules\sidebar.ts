const Layout = () => import("@/layout/index.vue");

export default {
  path: "/auth/sidebars/index",
  name: "Sidebar",
  component: Layout,
  redirect: "/auth/sidebars",
  meta: {
    icon: "ep:home-filled",
    title: "Sidebars",
    rank: 0
  },
  children: [
    {
      path: "/auth/sidebars",
      name: "SidebarIndex",
      component: () => import("@/views/system/sidebars/index.vue"),
      meta: {
        icon: "ri:book-line",
        title: "Sidebars"
      }
    }
  ]
} satisfies RouteConfigsTable;
