<template>
  <el-dialog
    v-model="dialogVisible"
    :title="$t('Assign Users to Bot')"
    width="800px"
    :before-close="handleClose"
  >
    <!-- Search -->
    <div class="mb-4">
      <el-input
        v-model="searchQuery"
        :placeholder="$t('Search users...')"
        :prefix-icon="Search"
        clearable
        @input="handleSearch"
      />
    </div>

    <!-- Available Users Table -->
    <el-table
      ref="tableRef"
      v-loading="loading"
      :data="availableUsers"
      max-height="400"
      stripe
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />

      <el-table-column :label="$t('User')" min-width="250">
        <template #default="{ row }">
          <div class="flex items-center space-x-3">
            <el-avatar
              :size="32"
              :src="row.avatar"
              :alt="row.firstName + ' ' + row.lastName"
            >
              {{ (row.firstName?.[0] || "") + (row.lastName?.[0] || "") }}
            </el-avatar>
            <div>
              <div class="font-medium text-gray-900 dark:text-gray-100">
                {{ row.firstName }} {{ row.lastName }}
              </div>
              <div class="text-sm text-gray-500 dark:text-gray-400">
                {{ row.email }}
              </div>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column :label="$t('Role')" width="120">
        <template #default="{ row }">
          <el-tag :type="getRoleTagType(row.role)" size="small">
            {{ row.role }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column :label="$t('Status')" width="100">
        <template #default="{ row }">
          <el-tag
            :type="row.status === 'active' ? 'success' : 'info'"
            size="small"
          >
            {{ row.status }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>

    <!-- Pagination -->
    <div class="mt-4 flex justify-center">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50]"
        layout="total, sizes, prev, pager, next"
        small
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>

    <!-- Footer -->
    <template #footer>
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-600 dark:text-gray-300">
          {{ $t("Selected") }}: {{ selectedUsers.length }}
        </div>
        <div class="space-x-2">
          <el-button @click="handleClose">
            {{ $t("Cancel") }}
          </el-button>
          <el-button
            type="primary"
            :loading="loading"
            :disabled="selectedUsers.length === 0"
            @click="handleAssign"
          >
            {{ $t("Assign Selected") }}
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from "vue";
import { Search } from "@element-plus/icons-vue";
import { $t } from "@/plugins/i18n";
import { message } from "@/utils/message";
import { useConvertKeyToCamel } from "@/utils/helpers";
import { getAllMembers } from "../../members/utils/auth-api";
import { useBotUserAssignmentHook } from "../utils/user-assignment-hook";

interface Props {
  visible: boolean;
  botId: string;
  organizationUuid: string;
  assignedUserIds: string[];
}

interface Emits {
  (e: "update:visible", value: boolean): void;
  (e: "assigned"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const tableRef = ref();
const searchQuery = ref("");
const loading = ref(false);
const availableUsers = ref([]);
const selectedUsers = ref([]);

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
});

// Use assignment hook
const { fnAssignUsers } = useBotUserAssignmentHook(
  props.organizationUuid,
  props.botId
);

// Computed
const dialogVisible = computed({
  get: () => props.visible,
  set: value => emit("update:visible", value)
});

// Methods
const fnGetAvailableUsers = async (params?: any) => {
  try {
    loading.value = true;
    const queryParams = {
      page: pagination.page,
      limit: pagination.size,
      search: params?.search || "",
      ...params
    };

    const response = await getAllMembers(props.organizationUuid, queryParams);

    if (response.success) {
      // Filter out already assigned users from server response
      const allUsers = useConvertKeyToCamel(
        response.data?.data || response.data || []
      ) as any[];

      const filteredUsers = allUsers.filter(
        (user: any) => !props.assignedUserIds.includes(user.id || user.uuid)
      );

      availableUsers.value = filteredUsers;
      pagination.total = response.data?.total || response.total || 0;
    } else {
      message(response.message || $t("Failed to load users"), {
        type: "error"
      });
    }
  } catch (error) {
    console.error("Get available users error:", error);
    message(error.response?.data?.message || $t("Failed to load users"), {
      type: "error"
    });
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  pagination.page = 1;
  fnGetAvailableUsers({ search: searchQuery.value });
};

const handleSelectionChange = (selection: any[]) => {
  selectedUsers.value = selection;
};

const handlePageChange = (page: number) => {
  pagination.page = page;
  fnGetAvailableUsers();
};

const handleSizeChange = (size: number) => {
  pagination.size = size;
  pagination.page = 1;
  fnGetAvailableUsers();
};

const handleAssign = async () => {
  const userIds = selectedUsers.value.map(user => user.id || user.uuid);
  const success = await fnAssignUsers(userIds);

  if (success) {
    emit("assigned");
    handleClose();
  }
};

const handleClose = () => {
  dialogVisible.value = false;
  selectedUsers.value = [];
  searchQuery.value = "";
  pagination.page = 1;
  tableRef.value?.clearSelection();
};

const getRoleTagType = (role: string) => {
  const roleTypes = {
    owner: "danger",
    admin: "warning",
    member: "info",
    guest: ""
  };
  return roleTypes[role.toLowerCase()] || "";
};

// Load data when component mounts
onMounted(() => {
  fnGetAvailableUsers();
});

// Watch for dialog visibility
watch(
  () => props.visible,
  visible => {
    if (visible) {
      fnGetAvailableUsers();
    }
  }
);
</script>
