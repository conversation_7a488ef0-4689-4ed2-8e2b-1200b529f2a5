<script setup lang="ts">
import { defineAsyncComponent, onMounted, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import { $t } from "@/plugins/i18n";
import { IconifyIconOnline } from "@/components/ReIcon";
import { checkOrganizationAccess } from "./shared/utils/auth-api";

const Overview = defineAsyncComponent(
  () => import("./modules/overview/index.vue")
);
const AiBot = defineAsyncComponent(() => import("./modules/ai-bots/index.vue"));

const Members = defineAsyncComponent(
  () => import("./modules/members/index.vue")
);

const Guests = defineAsyncComponent(() => import("./modules/guests/index.vue"));

const KnowledgeBase = defineAsyncComponent(
  () => import("./modules/knowledge-base/index.vue")
);
const Invitations = defineAsyncComponent(
  () => import("./modules/invitations/index.vue")
);

const route = useRoute();
const router = useRouter();

const activeTab = ref("overview");
const userRole = ref(null);
const hasAccess = ref(false);
const loading = ref(true);

// Check organization access and get user role
const checkAccess = async () => {
  try {
    const uuid = route.params?.id as string;
    const response = await checkOrganizationAccess(uuid);
    if (response.success && response.data.hasAccess) {
      hasAccess.value = true;
      userRole.value = response.data.currentRole; // 'admin', 'owner', 'member', 'guest'
    } else {
      // Redirect to 403 or show error
      router.push("/error/403");
    }
  } catch (error) {
    router.push("/error/404");
  } finally {
    loading.value = false;
  }
};

// Helper function to check tab access
const canAccessTab = (tabName: string) => {
  const restrictedTabs = ["knowledge", "invitations"];
  const allowedRoles = ["admin", "owner"];

  if (restrictedTabs.includes(tabName)) {
    return allowedRoles.includes(userRole.value);
  }
  return true; // All roles can access other tabs
};

onMounted(() => {
  checkAccess();
});
</script>

<template>
  <div v-if="!loading && hasAccess" class="main flex flex-col gap-4">
    <div class="flex flex-col w-full">
      <div class="flex flex-col md:flex-row justify-start items-stretch gap-4">
        <div
          class="logo flex justify-center items-center w-full md:max-w-2xs rounded bg-white p-4"
        />
        <div class="description flex flex-col rounded bg-white w-full p-4">
          <h1 class="font-bold text-gray-800 mb-6 flex items-start">
            {{ $t("Organization Name") }}
            <IconifyIconOnline
              icon="ri:checkbox-circle-fill"
              class="text-primary text-xs ml-1"
            />
          </h1>
          <div
            class="text-sm text-gray-500 mb-6"
            v-html="$t('Organization description')"
          />
        </div>
      </div>
    </div>
    <div class="flex md:flex-col">
      <el-card class="!w-full">
        <el-tabs v-model="activeTab" class="organization-tabs">
          <!-- Tab Tổng thể - No role prop -->
          <el-tab-pane name="overview">
            <template #label>
              <span class="flex items-center">
                <IconifyIconOnline
                  :icon="'fa6-solid:chart-pie'"
                  class="text-lg text-blue-600 mr-2"
                />
                {{ $t("Overview") }}
              </span>
            </template>
            <Overview />
          </el-tab-pane>

          <!-- Tab AI Bot - With role prop -->
          <el-tab-pane name="ai-bot">
            <template #label>
              <span class="flex items-center">
                <IconifyIconOnline
                  :icon="'fa6-solid:robot'"
                  class="text-lg text-teal-700 mr-2"
                />
                {{ $t("AI Bot") }}
              </span>
            </template>
            <AiBot :user-role="userRole" />
          </el-tab-pane>

          <!-- Tab Cơ sở tri thức - No role prop, conditional rendering -->
          <el-tab-pane v-if="canAccessTab('knowledge')" name="knowledge">
            <template #label>
              <span class="flex items-center">
                <IconifyIconOnline
                  :icon="'ri:book-open-line'"
                  class="text-lg text-purple-700 mr-2"
                />
                {{ $t("Knowledge Base") }}
              </span>
            </template>
            <KnowledgeBase />
          </el-tab-pane>

          <!-- Tab Thành viên - With role prop -->
          <el-tab-pane name="members">
            <template #label>
              <span class="flex items-center">
                <IconifyIconOnline
                  :icon="'fa6-solid:users'"
                  class="text-lg text-indigo-700 mr-2"
                />
                {{ $t("Members") }}
              </span>
            </template>
            <Members :user-role="userRole" />
          </el-tab-pane>

          <!-- Tab Khách mời - With role prop -->
          <el-tab-pane name="guests">
            <template #label>
              <span class="flex items-center">
                <IconifyIconOnline
                  :icon="'fa6-solid:user-tag'"
                  class="text-lg text-amber-700 mr-2"
                />
                {{ $t("Guests") }}
              </span>
            </template>
            <Guests :user-role="userRole" />
          </el-tab-pane>

          <!-- Tab Lời mời - No role prop, conditional rendering -->
          <el-tab-pane v-if="canAccessTab('invitations')" name="invitations">
            <template #label>
              <span class="flex items-center">
                <IconifyIconOnline
                  :icon="'fa6-solid:envelope'"
                  class="text-lg text-green-700 mr-2"
                />
                {{ $t("Invitations") }}
              </span>
            </template>
            <Invitations />
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>
  </div>

  <!-- Loading state -->
  <div v-else-if="loading" class="flex justify-center items-center h-64">
    <el-loading />
  </div>
</template>
