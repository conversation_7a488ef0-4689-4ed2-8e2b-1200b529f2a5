<script setup lang="ts">
import { $t } from "@/plugins/i18n";

interface Props {
  knowledgeEnabled: boolean;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (e: "update:knowledgeEnabled", value: boolean): void;
}>();

const handleKnowledgeToggle = (value: boolean) => {
  emit("update:knowledgeEnabled", value);
};
</script>

<template>
  <div class="card">
    <h2 class="section-title">{{ $t("Advanced Settings") }}</h2>
    <el-form-item :label="$t('Use Knowledge Base')">
      <el-switch
        :model-value="props.knowledgeEnabled"
        size="large"
        @update:model-value="handleKnowledgeToggle"
      />
    </el-form-item>
    <p class="text-xs text-gray-500">
      {{
        $t(
          "Enable this feature to allow Agent access to your private knowledge sources."
        )
      }}
    </p>
  </div>
</template>

<style lang="scss" scoped>
.card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #333;
}
</style>
