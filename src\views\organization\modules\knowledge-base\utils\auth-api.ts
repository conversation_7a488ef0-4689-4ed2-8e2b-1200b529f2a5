import { http } from "@/utils/http";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { Result } from "@/utils/response";

type r = Result;

/*
 ***************************
 *   Read Operations (GET)
 ***************************
 */
export const getKnowledgeBases = (uuid: string, params?: any) => {
  return http.request<r>(
    "get",
    `/api/v1/auth/organizations/${uuid}/knowledge-bases`,
    {
      params
    }
  );
};

export const getKnowledgeBaseById = (uuid: string, id: string) => {
  return http.request<r>(
    "get",
    `/api/v1/auth/organizations/${uuid}/knowledge-bases/${id}`
  );
};

export const getKnowledgeBasesDropdown = (uuid: string) => {
  return http.request<r>(
    "get",
    `/api/v1/auth/organizations/${uuid}/knowledge-bases/dropdown`
  );
};

export const getKnowledgeBaseFiles = (uuid: string, params?: object) => {
  return http.request<r>(
    "get",
    `/api/v1/auth/organizations/${uuid}/knowledge-bases/files`,
    {
      params
    }
  );
};

/*
 ***************************
 *   Create & Update Operations
 ***************************
 */
export const createKnowledgeBase = (uuid: string, data: any) => {
  return http.request<r>(
    "post",
    `/api/v1/auth/organizations/${uuid}/knowledge-bases`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

export const updateKnowledgeBaseById = (
  uuid: string,
  id: string,
  data: any
) => {
  return http.request<r>(
    "put",
    `/api/v1/auth/organizations/${uuid}/knowledge-bases/${id}`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

export const partialUpdateKnowledgeBaseById = (
  uuid: string,
  id: string,
  data: any
) => {
  return http.request<r>(
    "patch",
    `/api/v1/auth/organizations/${uuid}/knowledge-bases/${id}`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

/*
 ***************************
 *   Knowledge Base Creation Operations
 ***************************
 */
export const createKnowledgeBaseFromText = (uuid: string, data: any) => {
  return http.request<r>(
    "post",
    `/api/v1/auth/organizations/${uuid}/knowledge-bases/create-from-text`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

export const createKnowledgeBaseFromFile = (uuid: string, data: any) => {
  return http.request<r>(
    "post",
    `/api/v1/auth/organizations/${uuid}/knowledge-bases/create-from-file`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

/*
 ***************************
 *   File Management Operations
 ***************************
 */
export const uploadKnowledgeBaseFile = (uuid: string, file: File) => {
  const formData = new FormData();
  formData.append("file", file);

  return http.request<r>(
    "post",
    `/api/v1/auth/organizations/${uuid}/knowledge-bases/files/upload`,
    {
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data"
      }
    }
  );
};

export const removeKnowledgeBaseFile = (uuid: string, data: object) => {
  return http.request<r>(
    "delete",
    `/api/v1/auth/organizations/${uuid}/knowledge-bases/files/remove`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

/*
 ***************************
 *   Retrain Operations
 ***************************
 */
export const retrainKnowledgeBase = (uuid: string, id: string) => {
  return http.request<r>(
    "post",
    `/api/v1/auth/organizations/${uuid}/knowledge-bases/${id}/retrain`
  );
};

export const bulkRetrainKnowledgeBases = (
  uuid: string,
  data: { ids: string[] }
) => {
  return http.request<r>(
    "post",
    `/api/v1/auth/organizations/${uuid}/knowledge-bases/bulk/retrain`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

/*
 ***************************
 *   Bulk Upload Operations
 ***************************
 */
export const bulkUploadKnowledgeBases = (uuid: string, files: File[]) => {
  const formData = new FormData();
  files.forEach((file, index) => {
    formData.append(`files[${index}]`, file);
  });

  return http.request<r>(
    "post",
    `/api/v1/auth/organizations/${uuid}/knowledge-bases/bulk/upload`,
    {
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data"
      }
    }
  );
};

/*
 ***************************
 *   Soft Delete Operations
 ***************************
 */
export const deleteKnowledgeBase = (uuid: string, id: string) => {
  return http.request<r>(
    "delete",
    `/api/v1/auth/organizations/${uuid}/knowledge-bases/${id}/delete`
  );
};

export const bulkDeleteKnowledgeBases = (
  uuid: string,
  data: { ids: string[] }
) => {
  return http.request<r>(
    "delete",
    `/api/v1/auth/organizations/${uuid}/knowledge-bases/bulk/delete`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

/*
 ***************************
 *   Permanent Delete Operations
 ***************************
 */
export const deleteKnowledgeBasePermanent = (uuid: string, id: string) => {
  return http.request<r>(
    "delete",
    `/api/v1/auth/organizations/${uuid}/knowledge-bases/${id}/force`
  );
};

export const bulkDeleteKnowledgeBasesPermanent = (
  uuid: string,
  data: { ids: string[] }
) => {
  return http.request<r>(
    "delete",
    `/api/v1/auth/organizations/${uuid}/knowledge-bases/bulk/force`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

/*
 ***************************
 *   Restore Operations
 ***************************
 */
export const restoreKnowledgeBase = (uuid: string, id: string) => {
  return http.request<r>(
    "put",
    `/api/v1/auth/organizations/${uuid}/knowledge-bases/${id}/restore`
  );
};

export const bulkRestoreKnowledgeBases = (
  uuid: string,
  data: { ids: string[] }
) => {
  return http.request<r>(
    "put",
    `/api/v1/auth/organizations/${uuid}/knowledge-bases/bulk/restore`,
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

// Aliases for compatibility
export const destroyKnowledgeBase = deleteKnowledgeBasePermanent;
export const bulkDestroyKnowledgeBases = bulkDeleteKnowledgeBasesPermanent;
