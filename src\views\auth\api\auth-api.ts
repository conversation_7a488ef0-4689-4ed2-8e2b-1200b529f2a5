import { http } from "@/utils/http";
import { useConvertKeyToSnake } from "@/utils/helpers";

export type Result<T = any> = {
  success: boolean;
  message: string;
  data: T;
};

export type UserResult<T = any> = {
  success: boolean;
  message: string;
  data: T;
};

export interface AuthLoginData {
  user: {
    username: string;
    firstName: string;
    lastName: string;
    fullName: string;
    avatar: string | null;
    birthday: string | null;
    gender: string;
    email: string;
    phone: string | null;
    address: string | null;
    status: string;
    lastLoginAt: string | null;
    lastLoginIp: string | null;
    preferences: any;
    isVerified: boolean;
    newsletterSubscribed: boolean;
    createdAt: string;
    updatedAt: string;
    permissions: Array<string>;
    roles: Array<string>;
    country: any;
  };
  token: string;
  tokenType: string;
  expiresIn: number;
}

// Auth API functions
export const loginUser = (data?: object) => {
  return http.request<UserResult<AuthLoginData>>("post", "/api/v1/auth/login", {
    data: useConvertKeyToSnake(data)
  });
};

export const logoutUser = () => {
  return http.request<UserResult>("post", "/api/v1/auth/logout");
};

export const getCurrentUser = () => {
  return http.request<UserResult<AuthLoginData>>("get", "/api/v1/auth/me");
};

export const updateCurrentUser = (data: object) => {
  return http.request<UserResult>("put", "/api/v1/auth/profile", {
    data: useConvertKeyToSnake(data)
  });
};

export const sendPasswordChangeOTP = (data: object) => {
  return http.request<Result>("put", "/api/v1/auth/password/change", {
    data: useConvertKeyToSnake(data)
  });
};

export const changePasswordWithOTP = (data: object) => {
  return http.request<Result>("put", "/api/v1/auth/password/confirm-change", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateCurrentUserAvatar = (data: FormData) => {
  return http.request<UserResult>("post", "/api/v1/auth/me/avatar", {
    data,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
};

export const verifyPasswordChangeOTP = (data: object) => {
  return http.request<UserResult>("post", "/api/v1/auth/password/verify-otp", {
    data: useConvertKeyToSnake(data)
  });
};

export const registerUser = (data?: object) => {
  return http.request<UserResult>("post", "/api/v1/auth/register", {
    data: useConvertKeyToSnake(data)
  });
};

export const verifyEmail = (data?: object) => {
  return http.request<UserResult>("post", "/api/v1/auth/verify-email", {
    data
  });
};

export const forgotPassword = (data?: object) => {
  return http.request<UserResult>("post", "/api/v1/auth/forgot-password", {
    data: useConvertKeyToSnake(data)
  });
};

export const resetPassword = (data?: object) => {
  return http.request<UserResult>("post", "/api/v1/auth/reset-password", {
    data: useConvertKeyToSnake(data)
  });
};

export const resendVerification = (data?: object) => {
  return http.request<UserResult>("post", "/api/v1/auth/resend-verification", {
    data
  });
};

export const socialLogin = (provider: string, data?: object) => {
  return http.request<UserResult>("post", `/api/v1/auth/social/${provider}`, {
    data
  });
};
